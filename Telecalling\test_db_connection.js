#!/usr/bin/env node

/**
 * Database Connection and Super Admin Test Script
 */

require('dotenv').config();
const { dbHelpers } = require('./config/supabase');

async function testDatabaseConnection() {
  console.log('🧪 Testing Database Connection and Super Admin Setup\n');

  try {
    // Test 1: Basic database connection
    console.log('1️⃣ Testing basic database connection...');
    const timeResult = await dbHelpers.query('SELECT NOW() as current_time');
    console.log('✅ Database connected successfully:', timeResult[0].current_time);

    // Test 2: Check if admins table exists
    console.log('\n2️⃣ Checking admins table structure...');
    const tableInfo = await dbHelpers.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'admins' 
      ORDER BY ordinal_position
    `);
    
    if (tableInfo.length > 0) {
      console.log('✅ Admins table exists with columns:');
      tableInfo.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
    } else {
      console.log('❌ Admins table does not exist');
      return;
    }

    // Test 3: Check existing admins
    console.log('\n3️⃣ Checking existing admins...');
    const admins = await dbHelpers.query('SELECT admin_id, name, email, role, is_active FROM admins ORDER BY created_at');
    
    if (admins.length > 0) {
      console.log(`✅ Found ${admins.length} admin(s):`);
      admins.forEach(admin => {
        console.log(`   - ${admin.admin_id} (${admin.name}) - Role: ${admin.role || 'not set'} - Active: ${admin.is_active}`);
      });
    } else {
      console.log('❌ No admins found in database');
    }

    // Test 4: Check if super admin exists
    console.log('\n4️⃣ Checking super admin specifically...');
    const superAdmin = await dbHelpers.findOne('admins', { admin_id: 'superadmin001' });
    
    if (superAdmin) {
      console.log('✅ Super admin found:');
      console.log(`   - ID: ${superAdmin.admin_id}`);
      console.log(`   - Name: ${superAdmin.name}`);
      console.log(`   - Email: ${superAdmin.email}`);
      console.log(`   - Role: ${superAdmin.role}`);
      console.log(`   - Password: ${superAdmin.password_hash}`);
      console.log(`   - Active: ${superAdmin.is_active}`);
    } else {
      console.log('❌ Super admin not found');
      
      // Test 5: Create super admin if missing
      console.log('\n5️⃣ Creating super admin...');
      try {
        const newSuperAdmin = await dbHelpers.insert('admins', {
          admin_id: 'superadmin001',
          password_hash: 'superadmin123',
          name: 'Super Administrator',
          email: '<EMAIL>',
          role: 'super_admin',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        });
        console.log('✅ Super admin created successfully:', newSuperAdmin.admin_id);
      } catch (error) {
        console.log('❌ Failed to create super admin:', error.message);
      }
    }

    // Test 6: Test authentication logic
    console.log('\n6️⃣ Testing authentication logic...');
    const testAdmin = await dbHelpers.findOne('admins', { admin_id: 'superadmin001' });
    if (testAdmin) {
      const testPassword = 'superadmin123';
      const isValid = testPassword === testAdmin.password_hash;
      console.log(`✅ Password test: ${isValid ? 'PASS' : 'FAIL'}`);
      console.log(`   Expected: ${testPassword}`);
      console.log(`   Stored: ${testAdmin.password_hash}`);
      console.log(`   Match: ${isValid}`);
    }

    console.log('\n🎉 Database tests completed!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('Full error:', error);
    
    if (error.message.includes('getaddrinfo ENOTFOUND')) {
      console.log('\n💡 DNS Resolution Issue Detected:');
      console.log('This could be due to:');
      console.log('1. Network connectivity issues');
      console.log('2. Firewall blocking the connection');
      console.log('3. Supabase service temporarily unavailable');
      console.log('4. Incorrect database URL in .env file');
      console.log('\nTry:');
      console.log('- Check your internet connection');
      console.log('- Verify DATABASE_URL in .env file');
      console.log('- Try connecting from Supabase dashboard');
    }
  }
}

// Run the test
testDatabaseConnection();

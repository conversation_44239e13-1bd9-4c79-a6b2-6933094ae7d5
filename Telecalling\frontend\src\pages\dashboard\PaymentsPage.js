import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Calendar,
  TrendingUp,
  CheckCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Crown,
  Zap,
  Star
} from 'lucide-react';
import subscriptionAPI from '../../services/subscriptionAPI';
import PaymentModal from '../../components/modals/PaymentModal';
import PaymentSuccessModal from '../../components/modals/PaymentSuccessModal';

const PaymentsPage = () => {
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [availablePlans, setAvailablePlans] = useState([]);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentResult, setPaymentResult] = useState(null);

  useEffect(() => {
    fetchPaymentData();
  }, []);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);
      const [subscriptionResponse, plansResponse, historyResponse] = await Promise.all([
        subscriptionAPI.getCurrentSubscription(),
        subscriptionAPI.getPlans(),
        subscriptionAPI.getPaymentHistory(1, 10)
      ]);

      if (subscriptionResponse.success) {
        setCurrentSubscription(subscriptionResponse.subscription);
      }

      if (plansResponse.success) {
        setAvailablePlans(plansResponse.plans);
      }

      if (historyResponse.success) {
        setPaymentHistory(historyResponse.payments);
      }
    } catch (error) {
      console.error('Error fetching payment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = (plan) => {
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = (result) => {
    setPaymentResult(result);
    setShowPaymentModal(false);
    setShowSuccessModal(true);
    fetchPaymentData(); // Refresh data
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'Active' },
      expired: { color: 'bg-red-100 text-red-800', icon: AlertCircle, text: 'Expired' },
      free: { color: 'bg-gray-100 text-gray-800', icon: Clock, text: 'Free Plan' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' }
    };

    const config = statusConfig[status] || statusConfig.free;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
        <Icon className="w-4 h-4 mr-1" />
        {config.text}
      </span>
    );
  };

  const getPlanIcon = (planName) => {
    const icons = {
      free: Clock,
      basic: Zap,
      pro: Star,
      business: Crown,
      enterprise: TrendingUp
    };
    return icons[planName] || Clock;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const currentPlan = currentSubscription?.subscription_plans;
  const isFreePlan = !currentSubscription || currentPlan?.name === 'free';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Payments & Billing
          </h2>
          <p className="text-gray-600 mt-1">
            Manage your subscription and view payment history
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            onClick={fetchPaymentData}
            className="btn btn-outline flex items-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Current Subscription */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <CreditCard className="w-5 h-5 mr-2" />
          Current Subscription
        </h3>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              {React.createElement(getPlanIcon(currentPlan?.name), {
                className: "w-8 h-8 text-blue-600"
              })}
            </div>
            <div>
              <h4 className="text-xl font-bold text-gray-900">
                {currentPlan?.display_name || 'Free Plan'}
              </h4>
              <p className="text-gray-600">
                {currentPlan?.description || 'Basic features for testing'}
              </p>
              {currentSubscription?.ends_at && (
                <p className="text-sm text-gray-500 mt-1 flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  Expires on {new Date(currentSubscription.ends_at).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>

          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900 mb-2">
              {currentPlan?.price ? `₹${currentPlan.price.toLocaleString()}` : 'Free'}
              {currentPlan?.price && <span className="text-sm text-gray-500 font-normal">/month</span>}
            </div>
            {getStatusBadge(currentSubscription?.status || 'free')}
          </div>
        </div>

        {isFreePlan && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800 text-sm">
              🚀 Upgrade to a paid plan to unlock advanced features and increase your limits!
            </p>
          </div>
        )}
      </div>

      {/* Available Plans */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">
          Available Plans
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {availablePlans.filter(plan => plan.name !== 'free').map((plan) => {
            const Icon = getPlanIcon(plan.name);
            const isCurrentPlan = currentPlan?.id === plan.id;
            const canUpgrade = !isCurrentPlan && (
              !currentPlan ||
              plan.sort_order > (currentPlan.sort_order || 0)
            );

            return (
              <div
                key={plan.id}
                className={`relative rounded-lg border-2 p-6 ${
                  isCurrentPlan
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                } transition-colors`}
              >
                {plan.name === 'pro' && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-600 text-white px-3 py-1 text-xs font-medium rounded-full">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center">
                  <Icon className="w-8 h-8 mx-auto text-blue-600 mb-3" />
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    {plan.display_name}
                  </h4>
                  <div className="text-3xl font-bold text-gray-900 mb-1">
                    {plan.price === 0 ? 'Custom' : `₹${plan.price.toLocaleString()}`}
                  </div>
                  {plan.price > 0 && (
                    <div className="text-sm text-gray-500 mb-4">/month</div>
                  )}
                  <p className="text-sm text-gray-600 mb-4">
                    {plan.description}
                  </p>

                  {/* Plan Features */}
                  <div className="text-left mb-6">
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        <span>
                          {plan.max_calls === -1 ? 'Unlimited' : plan.max_calls} calls/month
                        </span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        <span>
                          {plan.max_phone_numbers === -1 ? 'Unlimited' : plan.max_phone_numbers} phone number{plan.max_phone_numbers !== 1 ? 's' : ''}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        <span>
                          {plan.max_products === -1 ? 'Unlimited' : plan.max_products} products
                        </span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                        <span>
                          {plan.features?.support || 'Email'} support
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Action Button */}
                  {isCurrentPlan ? (
                    <button
                      disabled
                      className="w-full btn btn-outline opacity-50 cursor-not-allowed"
                    >
                      Current Plan
                    </button>
                  ) : canUpgrade ? (
                    <button
                      onClick={() => handleUpgrade(plan)}
                      className={`w-full btn ${
                        plan.name === 'pro' ? 'btn-primary' : 'btn-outline'
                      }`}
                    >
                      {plan.name === 'enterprise' ? 'Contact Sales' : 'Upgrade Now'}
                    </button>
                  ) : (
                    <button
                      disabled
                      className="w-full btn btn-outline opacity-50 cursor-not-allowed"
                    >
                      Lower Plan
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Payment History */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Payment History
          </h3>
        </div>

        <div className="overflow-x-auto">
          {paymentHistory.length > 0 ? (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment ID
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paymentHistory.map((payment) => (
                  <tr key={payment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(payment.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {payment.client_subscriptions?.subscription_plans?.display_name || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {payment.description}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        ₹{payment.amount?.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {payment.currency}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getPaymentStatusBadge(payment.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 font-mono">
                        {payment.razorpay_payment_id || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-500 font-mono">
                        {payment.razorpay_order_id}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-center py-12">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No payment history</h3>
              <p className="mt-1 text-sm text-gray-500">
                Your payment transactions will appear here once you make a purchase.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Payment Modals */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        selectedPlan={selectedPlan}
        onSuccess={handlePaymentSuccess}
      />

      <PaymentSuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        paymentData={paymentResult}
      />
    </div>
  );
};

// Helper function for payment status badges
const getPaymentStatusBadge = (status) => {
  const statusConfig = {
    paid: { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'Paid' },
    created: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' },
    failed: { color: 'bg-red-100 text-red-800', icon: AlertCircle, text: 'Failed' },
    cancelled: { color: 'bg-gray-100 text-gray-800', icon: AlertCircle, text: 'Cancelled' }
  };

  const config = statusConfig[status] || statusConfig.created;
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      <Icon className="w-3 h-3 mr-1" />
      {config.text}
    </span>
  );
};

export default PaymentsPage;
// Error handling middleware
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error
  let error = {
    status: 500,
    message: 'Internal server error'
  };

  // Supabase errors
  if (err.code) {
    switch (err.code) {
      case '23505': // Unique constraint violation
        error = {
          status: 409,
          message: 'Resource already exists',
          details: err.detail
        };
        break;
      case '23503': // Foreign key constraint violation
        error = {
          status: 400,
          message: 'Invalid reference',
          details: err.detail
        };
        break;
      case '23502': // Not null constraint violation
        error = {
          status: 400,
          message: 'Required field missing',
          details: err.detail
        };
        break;
      case 'PGRST116': // Row not found
        error = {
          status: 404,
          message: 'Resource not found'
        };
        break;
      default:
        error = {
          status: 500,
          message: 'Database error',
          details: process.env.NODE_ENV === 'development' ? err.message : undefined
        };
    }
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = {
      status: 401,
      message: 'Invalid token'
    };
  }

  if (err.name === 'TokenExpiredError') {
    error = {
      status: 401,
      message: 'Token expired'
    };
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    error = {
      status: 400,
      message: 'Validation error',
      details: err.details
    };
  }

  // Multer errors (file upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    error = {
      status: 400,
      message: 'File too large'
    };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    error = {
      status: 400,
      message: 'Unexpected file field'
    };
  }

  // Custom application errors
  if (err.statusCode) {
    error = {
      status: err.statusCode,
      message: err.message,
      details: err.details
    };
  }

  // Send error response
  res.status(error.status).json({
    error: error.message,
    ...(error.details && { details: error.details }),
    ...(process.env.NODE_ENV === 'development' && { 
      stack: err.stack,
      original: err.message 
    })
  });
};

// Custom error class
class AppError extends Error {
  constructor(message, statusCode = 500, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.details = details;
    this.name = 'AppError';
  }
}

// Async error wrapper
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  AppError,
  asyncHandler
}; 
# 🚀 Admin Panel Setup Guide

## 📋 **Overview**

The admin panel runs on **Port 4000** as a separate server from the client server (Port 5000). This allows company customer support admins to manage clients independently.

## 🗄️ **Step 1: Database Setup**

### Run the Complete Schema
```sql
-- Execute this in Supabase SQL Editor
-- File: Telecalling/database/complete_schema.sql
```

This creates:
- ✅ **admins** table - Admin authentication
- ✅ **admin_sessions** table - Session management  
- ✅ **admin_client_assignments** table - Each admin manages max 250 clients
- ✅ **Updated clients** table - Added profile fields (SAFE - won't affect existing auth)
- ✅ **All other tables** - Products, call logs, SMS logs, support tickets, etc.

### Default Admin Accounts Created:

**Regular Admin:**
- **Admin ID**: `admin001`
- **Password**: `admin123` (bcrypt hashed)
- **Email**: `<EMAIL>`

**Super Admin:**
- **Admin ID**: `superadmin001`
- **Password**: `superadmin123` (simple password, no encryption)
- **Email**: `<EMAIL>`

## 🔧 **Step 2: Install Admin Panel Dependencies**

```bash
# Navigate to admin panel directory
cd Telecalling/admin-panel

# Install dependencies
npm install

# Dependencies installed:
# - express, cors, helmet, compression, morgan
# - express-rate-limit, bcryptjs, jsonwebtoken
# - uuid, resend, dotenv
```

## ⚙️ **Step 3: Environment Configuration**

### Update Backend .env (Telecalling/backend/.env)
```env
# Existing variables...
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
RESEND_API_KEY=your_resend_api_key_here
```

### Create Admin Panel .env (Telecalling/admin-panel/.env)
```env
# Database (same as backend)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# JWT Secrets (same as backend)
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here

# Email Service
RESEND_API_KEY=your_resend_api_key_here

# Admin Panel Configuration
ADMIN_PORT=4000
ADMIN_FRONTEND_URL=http://localhost:3001
NODE_ENV=development
```

## 🚀 **Step 4: Start Both Servers**

### Terminal 1: Start Client Server (Port 5000)
```bash
cd Telecalling/backend
npm start
```
**Output**: `🚀 Server running on port 5000`

### Terminal 2: Start Admin Panel Server (Port 4000)
```bash
cd Telecalling/admin-panel
npm start
```
**Output**: `🚀 Admin Panel Server running on port 4000`

## 🔐 **Step 5: Test Admin Authentication**

### Test Admin Login
```bash
curl -X POST http://localhost:4000/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "adminId": "admin001",
    "password": "admin123"
  }'
```

**Expected Response**:
```json
{
  "message": "Admin login successful",
  "admin": {
    "id": "uuid-here",
    "adminId": "admin001",
    "name": "System Administrator",
    "email": "<EMAIL>"
  },
  "tokens": {
    "accessToken": "jwt-token-here",
    "refreshToken": "refresh-token-here"
  }
}
```

### Test Admin Dashboard
```bash
curl -X GET http://localhost:4000/api/admin/panel/dashboard \
  -H "Authorization: Bearer YOUR_ADMIN_ACCESS_TOKEN"
```

## 📊 **Step 6: Admin Panel API Routes**

### **Authentication Routes** (`/api/admin/auth`)
- ✅ `POST /login` - Admin login with ID/password
- ✅ `POST /logout` - Admin logout
- ✅ `POST /refresh` - Refresh tokens
- ✅ `GET /profile` - Get admin profile

### **Panel Management** (`/api/admin/panel`)
- ✅ `GET /dashboard` - Dashboard overview
- ✅ `GET /clients` - Get assigned clients (max 250)
- ✅ `GET /clients/:id` - Get client details
- ✅ `PUT /clients/:id/assign-number` - Assign Plivo number
- ✅ `DELETE /clients/:id/remove-number` - Remove Plivo number

### **Support Management** (`/api/admin/support`)
- ✅ `GET /tickets` - Get support tickets
- ✅ `GET /tickets/:id` - Get ticket details
- ✅ `PUT /tickets/:id/respond` - Respond to ticket
- ✅ `GET /stats` - Support statistics

## 🔒 **Step 7: Security Features**

### **Rate Limiting**
- Authentication: 5 requests/15 minutes
- General API: 100 requests/15 minutes

### **Data Isolation**
- Admins can only access assigned clients
- Maximum 250 clients per admin
- JWT token validation on all routes

### **Session Management**
- JWT tokens with 8-hour expiry
- Refresh tokens with 7-day expiry
- Session tracking in database

## 🎯 **Step 8: Assign Clients to Admin**

### Method 1: Direct Database Assignment
```sql
-- Assign first 10 clients to admin001
INSERT INTO admin_client_assignments (id, admin_id, client_id, assigned_at)
SELECT 
  gen_random_uuid(),
  (SELECT id FROM admins WHERE admin_id = 'admin001'),
  c.id,
  NOW()
FROM clients c
LIMIT 10;
```

### Method 2: API Assignment (Future Feature)
```bash
curl -X POST http://localhost:4000/api/admin/panel/assign-clients \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "clientIds": ["client-uuid-1", "client-uuid-2"]
  }'
```

## 📱 **Step 9: Test Client Management**

### Get Assigned Clients
```bash
curl -X GET "http://localhost:4000/api/admin/panel/clients?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Assign Plivo Number
```bash
curl -X PUT http://localhost:4000/api/admin/panel/clients/CLIENT_ID/assign-number \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "plivoNumber": "+************"
  }'
```

## 🛠️ **Step 10: Support Ticket Management**

### Get Support Tickets
```bash
curl -X GET "http://localhost:4000/api/admin/support/tickets?status=open" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Respond to Ticket
```bash
curl -X PUT http://localhost:4000/api/admin/support/tickets/TICKET_ID/respond \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "response": "Thank you for contacting support. We have resolved your issue.",
    "status": "resolved"
  }'
```

## 🔍 **Step 11: Health Checks**

### Client Server Health
```bash
curl http://localhost:5000/health
```

### Admin Panel Health
```bash
curl http://localhost:4000/health
```

## 📝 **Step 12: Package.json Scripts**

### Add to Telecalling/admin-panel/package.json
```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  }
}
```

### For Development with Auto-restart
```bash
cd Telecalling/admin-panel
npm install -g nodemon
npm run dev
```

## 🚨 **Troubleshooting**

### Common Issues:

1. **Port 4000 already in use**
   ```bash
   # Kill process on port 4000
   lsof -ti:4000 | xargs kill -9
   ```

2. **Database connection issues**
   - Check Supabase credentials in .env
   - Ensure database schema is properly executed

3. **JWT token errors**
   - Ensure JWT_SECRET and JWT_REFRESH_SECRET are set
   - Check token expiry

4. **Admin login fails**
   - Verify admin account exists in database
   - Check password hash in admins table

## ✅ **Verification Checklist**

- [ ] Database schema executed successfully
- [ ] Admin panel server starts on port 4000
- [ ] Client server runs on port 5000
- [ ] Admin login works with admin001/admin123
- [ ] Admin can access dashboard
- [ ] Admin can view assigned clients
- [ ] Admin can assign Plivo numbers
- [ ] Support ticket system works
- [ ] Email notifications sent

## 🎯 **Next Steps**

1. **Create Admin Frontend** (Port 3001)
2. **Add more admin accounts**
3. **Implement client assignment UI**
4. **Add analytics dashboard for admins**
5. **Set up production deployment**

The admin panel is now fully functional and ready for use! 🚀

const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Plivo webhook for incoming calls
router.post('/webhook', asyncHandler(async (req, res) => {
  const { From, To, CallUUID, CallStatus, Duration } = req.body;

  console.log('Plivo webhook received:', req.body);

  // Find client by Plivo number
  const { data: client, error: clientError } = await supabaseAdmin
    .from('clients')
    .select(`
      *,
      bot_features (*),
      products (*)
    `)
    .eq('plivo_number', To)
    .single();

  if (clientError || !client) {
    console.error('Client not found for number:', To);
    return res.status(404).json({ error: 'Client not found' });
  }

  // Generate voice bot response based on client data
  const botResponse = await generateBotResponse(client, From);

  // Create call log entry
  await supabaseAdmin
    .from('call_logs')
    .insert({
      id: uuidv4(),
      client_id: client.id,
      caller_number: From,
      call_time: new Date().toISOString(),
      duration: parseInt(Duration) || 0,
      summary: `Call from ${From} handled by voice bot`,
      call_status: CallStatus || 'completed',
      plivo_call_uuid: CallUUID
    });

  // Return Plivo XML response
  res.set('Content-Type', 'text/xml');
  res.send(botResponse);
}));

// Plivo webhook for call events
router.post('/events', asyncHandler(async (req, res) => {
  const { CallUUID, Event, From, To, Duration } = req.body;

  console.log('Plivo event received:', req.body);

  if (Event === 'hangup') {
    // Update call log with final duration
    await supabaseAdmin
      .from('call_logs')
      .update({
        duration: parseInt(Duration) || 0,
        call_status: 'completed'
      })
      .eq('plivo_call_uuid', CallUUID);
  }

  res.status(200).json({ success: true });
}));

// Generate dynamic bot response based on client data
async function generateBotResponse(client, callerNumber) {
  const { shop_name, business_summary, bot_features, products } = client;

  // Build greeting message
  let greeting = `Hello! Welcome to ${shop_name}. `;
  
  if (business_summary) {
    greeting += `${business_summary}. `;
  }

  greeting += `I'm your virtual assistant. How can I help you today?`;

  // Build product information
  let productInfo = '';
  if (products && products.length > 0) {
    productInfo = 'Here are some of our products: ';
    products.slice(0, 3).forEach((product, index) => {
      const productData = product.product_data;
      productInfo += `${index + 1}. ${productData.name} for ${productData.price} rupees. `;
    });
  }

  // Build service options
  let serviceOptions = 'You can also ';
  const options = [];

  if (bot_features?.appointments_enabled) {
    options.push('book an appointment');
  }

  if (bot_features?.estimate_enabled) {
    options.push('request an estimate');
  }

  if (bot_features?.payment_enabled) {
    options.push('make a payment');
  }

  if (options.length > 0) {
    serviceOptions += options.join(', ') + '. ';
  }

  // Generate Plivo XML response
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Speak voice="WOMAN">${greeting}</Speak>
  <Pause length="1"/>
  ${productInfo ? `<Speak voice="WOMAN">${productInfo}</Speak><Pause length="1"/>` : ''}
  ${options.length > 0 ? `<Speak voice="WOMAN">${serviceOptions}</Speak><Pause length="1"/>` : ''}
  <Speak voice="WOMAN">Please hold while I connect you to our team, or you can hang up and we'll send you more information via WhatsApp.</Speak>
  <GetDigits action="/api/plivo/handle-input" method="POST" numDigits="1" timeout="10">
    <Speak voice="WOMAN">Press 1 for product information, 2 to book an appointment, 3 for estimates, or 0 to speak with someone. Press any key to continue.</Speak>
  </GetDigits>
  <Speak voice="WOMAN">Thank you for calling ${shop_name}. We'll send you our details via WhatsApp shortly. Have a great day!</Speak>
</Response>`;

  // Send follow-up WhatsApp message
  setTimeout(() => {
    sendFollowUpWhatsApp(client, callerNumber);
  }, 2000);

  return xml;
}

// Handle digit input from caller
router.post('/handle-input', asyncHandler(async (req, res) => {
  const { Digits, From, To } = req.body;

  // Find client by Plivo number
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('*')
    .eq('plivo_number', To)
    .single();

  if (!client) {
    return res.status(404).json({ error: 'Client not found' });
  }

  let response = '';

  switch (Digits) {
    case '1':
      // Product information
      const { data: products } = await supabaseAdmin
        .from('products')
        .select('*')
        .eq('client_id', client.id)
        .limit(5);

      if (products && products.length > 0) {
        response = `Here are our products: `;
        products.forEach((product, index) => {
          const productData = product.product_data;
          response += `${index + 1}. ${productData.name} for ${productData.price} rupees. `;
        });
      } else {
        response = 'We have various products available. Please contact us for more details.';
      }
      break;

    case '2':
      // Book appointment
      response = 'To book an appointment, please hold while we connect you to our team, or we can send you available slots via WhatsApp.';
      setTimeout(() => {
        sendAppointmentWhatsApp(client, From);
      }, 2000);
      break;

    case '3':
      // Request estimate
      response = 'For estimates, please describe your requirements to our team, or we can send you a quote via WhatsApp.';
      setTimeout(() => {
        sendEstimateWhatsApp(client, From);
      }, 2000);
      break;

    case '0':
      // Speak with someone
      response = 'Please hold while we connect you to our team. If no one is available, we will call you back shortly.';
      break;

    default:
      response = 'Thank you for your interest. We will send you more information via WhatsApp.';
  }

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Speak voice="WOMAN">${response}</Speak>
  <Pause length="2"/>
  <Speak voice="WOMAN">Thank you for calling ${client.shop_name}. Have a great day!</Speak>
</Response>`;

  res.set('Content-Type', 'text/xml');
  res.send(xml);
}));

// Send follow-up WhatsApp message
async function sendFollowUpWhatsApp(client, callerNumber) {
  try {
    const message = `Hello! Thank you for calling ${client.shop_name}. 

Here's what we offer:
${client.business_summary || 'We provide quality products and services.'}

For more information, product catalog, or to book an appointment, please reply to this message.

Best regards,
${client.shop_name} Team`;

    // Log WhatsApp message
    await supabaseAdmin
      .from('whatsapp_logs')
      .insert({
        id: uuidv4(),
        client_id: client.id,
        to_number: callerNumber,
        message_type: 'follow_up',
        content: message,
        status: 'sent',
        timestamp: new Date().toISOString()
      });

    console.log(`Follow-up WhatsApp sent to ${callerNumber} for ${client.shop_name}`);
  } catch (error) {
    console.error('Error sending follow-up WhatsApp:', error);
  }
}

// Send appointment WhatsApp message
async function sendAppointmentWhatsApp(client, callerNumber) {
  try {
    const message = `📅 *Appointment Booking - ${client.shop_name}*

Thank you for your interest in booking an appointment!

Please reply with your preferred:
- Date
- Time
- Service required

We'll confirm your appointment shortly.

Best regards,
${client.shop_name} Team`;

    await supabaseAdmin
      .from('whatsapp_logs')
      .insert({
        id: uuidv4(),
        client_id: client.id,
        to_number: callerNumber,
        message_type: 'appointment',
        content: message,
        status: 'sent',
        timestamp: new Date().toISOString()
      });

    console.log(`Appointment WhatsApp sent to ${callerNumber} for ${client.shop_name}`);
  } catch (error) {
    console.error('Error sending appointment WhatsApp:', error);
  }
}

// Send estimate WhatsApp message
async function sendEstimateWhatsApp(client, callerNumber) {
  try {
    const message = `📋 *Estimate Request - ${client.shop_name}*

Thank you for your interest in our services!

Please reply with details about:
- What you need
- Quantity required
- Any specific requirements

We'll send you a detailed estimate shortly.

Best regards,
${client.shop_name} Team`;

    await supabaseAdmin
      .from('whatsapp_logs')
      .insert({
        id: uuidv4(),
        client_id: client.id,
        to_number: callerNumber,
        message_type: 'estimate',
        content: message,
        status: 'sent',
        timestamp: new Date().toISOString()
      });

    console.log(`Estimate WhatsApp sent to ${callerNumber} for ${client.shop_name}`);
  } catch (error) {
    console.error('Error sending estimate WhatsApp:', error);
  }
}

module.exports = router; 
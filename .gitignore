# Node modules
node_modules/
backend/node_modules/
frontend/node_modules/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt/

# Bower dependency directory (https://bower.io/)
bower_components/

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
dist/
backend/dist/
frontend/dist/

# dotenv environment variables files
.env
.env.*
backend/.env
frontend/.env

# Mac system files
.DS_Store

# Windows system files
Thumbs.db

# VSCode settings
.vscode/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
yarn.lock

# Parcel-bundler cache (https://parceljs.org/)
.cache

# SvelteKit build
.svelte-kit

# Next.js build output
.next

# Nuxt.js build output
.nuxt

# Gatsby files
.cache/
public/

# Storybook build outputs
out/
storybook-static/

# JetBrains IDEs
.idea/

# Other
*.swp
*.swo 
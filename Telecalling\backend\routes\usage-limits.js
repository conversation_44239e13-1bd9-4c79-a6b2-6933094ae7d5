const express = require('express');
const router = express.Router();
const { supabaseAdmin } = require('../config/supabase');

// Get client's current usage and limits
router.get('/current', async (req, res) => {
  try {
    const clientId = req.user.id;

    const { data, error } = await supabaseAdmin
      .rpc('get_client_current_usage', { p_client_id: clientId });

    if (error) {
      console.error('Error fetching usage:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch usage data'
      });
    }

    res.json({
      success: true,
      usage: data
    });
  } catch (error) {
    console.error('Usage fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch usage data'
    });
  }
});

// Check if client can make a call
router.post('/check-call', async (req, res) => {
  try {
    const clientId = req.user.id;
    const { estimatedMinutes = 1 } = req.body;

    const { data, error } = await supabaseAdmin
      .rpc('can_client_make_call', { 
        p_client_id: clientId,
        p_estimated_minutes: estimatedMinutes
      });

    if (error) {
      console.error('Error checking call permission:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to check call permission'
      });
    }

    res.json({
      success: true,
      ...data
    });
  } catch (error) {
    console.error('Call check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check call permission'
    });
  }
});

// Check if client can send SMS
router.post('/check-sms', async (req, res) => {
  try {
    const clientId = req.user.id;

    const { data, error } = await supabaseAdmin
      .rpc('can_client_send_sms', { p_client_id: clientId });

    if (error) {
      console.error('Error checking SMS permission:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to check SMS permission'
      });
    }

    res.json({
      success: true,
      ...data
    });
  } catch (error) {
    console.error('SMS check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check SMS permission'
    });
  }
});

// Record call usage
router.post('/record-call', async (req, res) => {
  try {
    const clientId = req.user.id;
    const { durationMinutes } = req.body;

    if (!durationMinutes || durationMinutes <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Valid call duration is required'
      });
    }

    const { data, error } = await supabaseAdmin
      .rpc('record_call_usage', { 
        p_client_id: clientId,
        p_call_duration_minutes: Math.ceil(durationMinutes)
      });

    if (error) {
      console.error('Error recording call usage:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to record call usage'
      });
    }

    res.json({
      success: true,
      ...data
    });
  } catch (error) {
    console.error('Call recording error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record call usage'
    });
  }
});

// Record SMS usage
router.post('/record-sms', async (req, res) => {
  try {
    const clientId = req.user.id;
    const { smsCount = 1 } = req.body;

    const { data, error } = await supabaseAdmin
      .rpc('record_sms_usage', { 
        p_client_id: clientId,
        p_sms_count: smsCount
      });

    if (error) {
      console.error('Error recording SMS usage:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to record SMS usage'
      });
    }

    res.json({
      success: true,
      ...data
    });
  } catch (error) {
    console.error('SMS recording error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to record SMS usage'
    });
  }
});

// Get usage summary for dashboard
router.get('/summary', async (req, res) => {
  try {
    const clientId = req.user.id;

    const { data, error } = await supabaseAdmin
      .from('client_usage_summary')
      .select('*')
      .eq('client_id', clientId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching usage summary:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch usage summary'
      });
    }

    res.json({
      success: true,
      summary: data || {
        client_id: clientId,
        current_plan: 'Free',
        call_limit: '10',
        sms_limit: '5',
        call_minutes_used: 0,
        sms_count_used: 0,
        call_status: '10 remaining',
        sms_status: '5 remaining'
      }
    });
  } catch (error) {
    console.error('Usage summary error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch usage summary'
    });
  }
});

module.exports = router;

import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState(localStorage.getItem('customer_token'));

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

  // Initialize auth state
  useEffect(() => {
    if (token) {
      fetchCustomerProfile();
    } else {
      setLoading(false);
    }
  }, [token]);

  const fetchCustomerProfile = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/customers/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCustomer(data.customer);
      } else {
        // Token is invalid
        logout();
      }
    } catch (error) {
      console.error('Failed to fetch customer profile:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, otp) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/customers/verify-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, otp })
      });

      const data = await response.json();

      if (response.ok) {
        // Store token and set customer
        setToken(data.token);
        localStorage.setItem('customer_token', data.token);
        setCustomer(data.customer);
        return { success: true, ...data };
      } else {
        return { success: false, error: data.error || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  const register = async (registrationData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/customers/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(registrationData)
      });

      const data = await response.json();

      if (response.ok) {
        return { success: true, ...data };
      } else {
        return { success: false, error: data.error || 'Registration failed' };
      }
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  const verifyEmail = async (email, otp) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/customers/verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, otp })
      });

      const data = await response.json();

      if (response.ok) {
        // Store token and set customer
        setToken(data.token);
        localStorage.setItem('customer_token', data.token);
        setCustomer(data.customer);
        return { success: true, ...data };
      } else {
        return { success: false, error: data.error || 'Email verification failed' };
      }
    } catch (error) {
      console.error('Email verification error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  const requestLoginOTP = async (email) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/customers/request-login-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      });

      const data = await response.json();

      if (response.ok) {
        return { success: true, ...data };
      } else {
        return { success: false, error: data.error || 'Failed to send login code' };
      }
    } catch (error) {
      console.error('Request login OTP error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/customers/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profileData)
      });

      const data = await response.json();

      if (response.ok) {
        setCustomer(data.customer);
        return { success: true, ...data };
      } else {
        return { success: false, error: data.error || 'Profile update failed' };
      }
    } catch (error) {
      console.error('Profile update error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  const logout = () => {
    setToken(null);
    setCustomer(null);
    localStorage.removeItem('customer_token');
  };

  const validateRegistrationToken = async (token) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/customers/validate-token/${token}`);
      const data = await response.json();

      if (response.ok) {
        return { success: true, ...data };
      } else {
        return { success: false, error: data.error || 'Invalid token' };
      }
    } catch (error) {
      console.error('Token validation error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  const value = {
    customer,
    loading,
    isAuthenticated: !!customer,
    register,
    verifyEmail,
    requestLoginOTP,
    login,
    logout,
    validateRegistrationToken
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Simple test script to verify chatbot functionality
 * Run with: node test-chatbot.js
 */

require('dotenv').config();
const OpenAI = require('openai');

async function testChatbot() {
  console.log('🤖 Testing Chatbot Functionality...\n');

  // Test 1: Check OpenAI API Key
  console.log('1. Checking OpenAI API Key...');
  if (!process.env.OPENAI_API_KEY) {
    console.log('❌ OPENAI_API_KEY not found in environment variables');
    console.log('   Please add OPENAI_API_KEY=your_key_here to your .env file');
    return;
  }
  
  if (!process.env.OPENAI_API_KEY.startsWith('sk-')) {
    console.log('❌ Invalid OpenAI API key format');
    console.log('   API key should start with "sk-"');
    return;
  }
  
  console.log('✅ OpenAI API key found and formatted correctly');

  // Test 2: Test OpenAI Connection
  console.log('\n2. Testing OpenAI Connection...');
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    const testResponse = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Say "Hello, chatbot test successful!" in exactly those words.' }
      ],
      max_tokens: 50,
      temperature: 0
    });

    const response = testResponse.choices[0].message.content;
    console.log('✅ OpenAI connection successful');
    console.log(`   Response: ${response}`);
    console.log(`   Tokens used: ${testResponse.usage.total_tokens}`);

  } catch (error) {
    console.log('❌ OpenAI connection failed');
    console.log(`   Error: ${error.message}`);
    
    if (error.code === 'insufficient_quota') {
      console.log('   💡 Your OpenAI account has insufficient credits');
    } else if (error.code === 'invalid_api_key') {
      console.log('   💡 Your OpenAI API key is invalid');
    }
    return;
  }

  // Test 3: Check Database Connection and Schema
  console.log('\n3. Testing Database Connection and Schema...');
  try {
    const { supabaseAdmin } = require('./config/supabase');

    // Test clients table
    const { data: clients, error: clientsError } = await supabaseAdmin
      .from('clients')
      .select('id, user_id, business_name, shop_name')
      .limit(1);

    if (clientsError) {
      throw new Error(`Clients table error: ${clientsError.message}`);
    }

    console.log('✅ Clients table accessible');
    console.log(`   Found ${clients ? clients.length : 0} client records`);

    // Test products table structure
    const { data: products, error: productsError } = await supabaseAdmin
      .from('products')
      .select('id, product_name, product_details, alias_names')
      .limit(1);

    if (productsError) {
      console.log(`⚠️  Products table warning: ${productsError.message}`);
    } else {
      console.log('✅ Products table accessible with correct schema');
      console.log(`   Found ${products ? products.length : 0} product records`);
    }

    // Test call_logs table structure
    const { data: callLogs, error: callLogsError } = await supabaseAdmin
      .from('call_logs')
      .select('id, client_id, call_time, caller_number')
      .limit(1);

    if (callLogsError) {
      console.log(`⚠️  Call logs table warning: ${callLogsError.message}`);
    } else {
      console.log('✅ Call logs table accessible');
      console.log(`   Found ${callLogs ? callLogs.length : 0} call log records`);
    }

  } catch (error) {
    console.log('❌ Database connection failed');
    console.log(`   Error: ${error.message}`);
    return;
  }

  // Test 4: Test Chatbot Route Module
  console.log('\n4. Testing Chatbot Route Module...');
  try {
    const chatbotRoutes = require('./routes/chatbot');
    console.log('✅ Chatbot routes module loaded successfully');
  } catch (error) {
    console.log('❌ Failed to load chatbot routes module');
    console.log(`   Error: ${error.message}`);
    return;
  }

  console.log('\n🎉 All tests passed! Chatbot is ready to use.');
  console.log('\nNext steps:');
  console.log('1. Start the backend server: npm start');
  console.log('2. Start the frontend: cd ../frontend && npm start');
  console.log('3. Login to the dashboard and look for the chatbot icon in the bottom-right corner');
}

// Run the test
testChatbot().catch(error => {
  console.error('❌ Test failed with error:', error);
  process.exit(1);
});

/**
 * Simple test script to verify chatbot functionality
 * Run with: node test-chatbot.js
 */

require('dotenv').config();
const OpenAI = require('openai');

async function testChatbot() {
  console.log('🤖 Testing Chatbot Functionality...\n');

  // Test 1: Check OpenAI API Key
  console.log('1. Checking OpenAI API Key...');
  if (!process.env.OPENAI_API_KEY) {
    console.log('❌ OPENAI_API_KEY not found in environment variables');
    console.log('   Please add OPENAI_API_KEY=your_key_here to your .env file');
    return;
  }
  
  if (!process.env.OPENAI_API_KEY.startsWith('sk-')) {
    console.log('❌ Invalid OpenAI API key format');
    console.log('   API key should start with "sk-"');
    return;
  }
  
  console.log('✅ OpenAI API key found and formatted correctly');

  // Test 2: Test OpenAI Connection
  console.log('\n2. Testing OpenAI Connection...');
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    const testResponse = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Say "Hello, chatbot test successful!" in exactly those words.' }
      ],
      max_tokens: 50,
      temperature: 0
    });

    const response = testResponse.choices[0].message.content;
    console.log('✅ OpenAI connection successful');
    console.log(`   Response: ${response}`);
    console.log(`   Tokens used: ${testResponse.usage.total_tokens}`);

  } catch (error) {
    console.log('❌ OpenAI connection failed');
    console.log(`   Error: ${error.message}`);
    
    if (error.code === 'insufficient_quota') {
      console.log('   💡 Your OpenAI account has insufficient credits');
    } else if (error.code === 'invalid_api_key') {
      console.log('   💡 Your OpenAI API key is invalid');
    }
    return;
  }

  // Test 3: Check Database Connection
  console.log('\n3. Testing Database Connection...');
  try {
    const { supabaseAdmin } = require('./config/supabase');
    
    // Simple query to test connection
    const { data, error } = await supabaseAdmin
      .from('clients')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful');
    console.log(`   Found ${data ? data.length : 0} client records (limited to 1)`);

  } catch (error) {
    console.log('❌ Database connection failed');
    console.log(`   Error: ${error.message}`);
    return;
  }

  // Test 4: Test Chatbot Route Module
  console.log('\n4. Testing Chatbot Route Module...');
  try {
    const chatbotRoutes = require('./routes/chatbot');
    console.log('✅ Chatbot routes module loaded successfully');
  } catch (error) {
    console.log('❌ Failed to load chatbot routes module');
    console.log(`   Error: ${error.message}`);
    return;
  }

  console.log('\n🎉 All tests passed! Chatbot is ready to use.');
  console.log('\nNext steps:');
  console.log('1. Start the backend server: npm start');
  console.log('2. Start the frontend: cd ../frontend && npm start');
  console.log('3. Login to the dashboard and look for the chatbot icon in the bottom-right corner');
}

// Run the test
testChatbot().catch(error => {
  console.error('❌ Test failed with error:', error);
  process.exit(1);
});

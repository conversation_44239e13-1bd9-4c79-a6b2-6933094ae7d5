# 🤖 Floating Chatbot Setup Guide

## Overview
The floating chatbot is an AI-powered assistant that appears on the bottom-right corner of the client dashboard. It provides intelligent responses based on the client's business data including products, customers, call logs, appointments, and more.

## Features
- **Voice Input/Output**: Uses Web Speech API for voice recognition and text-to-speech
- **Contextual Responses**: AI understands your business data and provides relevant answers
- **Floating UI**: Non-intrusive design that doesn't interfere with dashboard functionality
- **Real-time Chat**: Instant responses with typing indicators
- **Minimizable**: Can be minimized to save screen space
- **Cost-Efficient**: Uses GPT-3.5-turbo for optimal token usage

## Setup Instructions

### 1. Environment Configuration
Add your OpenAI API key to the backend `.env` file:

```env
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```

**Important**: Replace with your actual OpenAI API key from [OpenAI Platform](https://platform.openai.com/api-keys)

### 2. Backend Setup
The chatbot backend is automatically configured with the following endpoints:

- `POST /api/chatbot/chat` - Send messages to the AI assistant
- `GET /api/chatbot/context` - Get business context summary

### 3. Frontend Integration
The chatbot is automatically integrated into the DashboardLayout component and appears on all dashboard pages.

## How It Works

### Data Collection
The chatbot fetches comprehensive business data including:
- **Client Profile**: Business name, type, contact information
- **Products**: Complete product catalog with details and aliases
- **Call Logs**: Recent call history and summaries
- **SMS Logs**: SMS communication history
- **Customers**: Customer interactions and profiles
- **Appointments**: Booking slots and schedules
- **Subscription**: Current plan and usage statistics

### AI Processing
- Uses OpenAI GPT-3.5-turbo for cost-effective responses
- Generates contextual system prompts based on business data
- Maintains conversation history for better context
- Limits token usage with smart truncation

### Voice Features
- **Voice Input**: Click the microphone button to speak your question
- **Voice Output**: AI responses are automatically spoken (can be toggled)
- **Browser Compatibility**: Works with modern browsers supporting Web Speech API

## Usage Examples

### Business Queries
- "How many products do I have?"
- "Show me recent customer calls"
- "What's my subscription status?"
- "How many appointments are booked this week?"

### Analytics Questions
- "What's my busiest day for calls?"
- "Which products are customers asking about most?"
- "How many new customers this month?"

### Operational Help
- "How do I add a new product?"
- "What's my current usage?"
- "Show me pending appointments"

## Troubleshooting

### Debugging Steps

1. **Test Backend Setup:**
   ```bash
   cd Telecalling/backend
   node test-chatbot.js
   ```

2. **Test Data Fetching (without OpenAI):**
   ```bash
   # After logging into dashboard, visit:
   http://localhost:5000/api/chatbot/debug
   ```

3. **Check Database Schema:**
   - Verify tables exist: `clients`, `products`, `call_logs`, `sms_logs`
   - Check column names match the expected schema

### Common Issues

1. **"column call_logs.call_time does not exist"**
   - Database schema mismatch
   - Run the correct migration scripts
   - Check if using `call_time` vs `call_timestamp`

2. **"Client profile not found"**
   - User ID to Client ID mapping issue
   - Verify client record exists in database
   - Check `user_id` foreign key relationship

3. **Chatbot not responding**
   - Check if OPENAI_API_KEY is set in backend .env
   - Verify OpenAI API key is valid and has credits
   - Check browser console for errors

4. **Voice features not working**
   - Ensure you're using HTTPS (required for Web Speech API)
   - Check browser permissions for microphone access
   - Verify browser supports Web Speech API

5. **Authentication errors**
   - Ensure user is logged in to dashboard
   - Check if JWT token is valid
   - Verify backend authentication middleware

### Browser Support
- **Voice Input**: Chrome, Edge, Safari (latest versions)
- **Voice Output**: All modern browsers
- **Chat Interface**: All modern browsers

## Cost Optimization
- Uses GPT-3.5-turbo (most cost-effective OpenAI model)
- Limits response tokens to 500 per message
- Maintains only last 10 messages in conversation history
- Smart context truncation to minimize prompt tokens

## Security
- All requests require valid JWT authentication
- Client data is isolated per authenticated user
- No data is stored by OpenAI (as per their API policy)
- Sensitive information is filtered from AI prompts

## Customization
The chatbot can be customized by modifying:
- `FloatingChatbot.js` - UI components and behavior
- `FloatingChatbot.css` - Styling and animations
- `chatbot.js` (backend) - AI prompts and data processing

## Support
For issues or questions about the chatbot feature, contact the development team or check the application logs for detailed error information.

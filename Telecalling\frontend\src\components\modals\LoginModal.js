import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Phone } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import loginImage from '../../assets/images/login.jpeg';
import './LoginModal.css';

const LoginModal = ({ isOpen, onClose, onSwitchToSignup, onForgotPassword }) => {
  const navigate = useNavigate();
  const { login, signInWithGoogle } = useAuth();
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [googleAuthInProgress, setGoogleAuthInProgress] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      const result = await login(formData.email, formData.password);
      
      if (result.success) {
        // Close modal and redirect to dashboard
    onClose();
    navigate('/dashboard/plan499');
      } else {
        if (result.code === 'EMAIL_NOT_VERIFIED') {
          setErrors({ 
            general: 'Please verify your email before logging in.',
            action: 'verify_email'
          });
        } else {
          setErrors({ general: result.error });
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      setErrors({ general: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = async (provider) => {
    if (provider === 'google') {
      // Prevent multiple Google auth attempts
      if (googleAuthInProgress) {
        console.log('Google auth already in progress, ignoring click');
        return;
      }

      setIsLoading(true);
      setGoogleAuthInProgress(true);

      try {
        console.log('🔄 Initiating Google sign in...');
        const result = await signInWithGoogle();
        if (result.success) {
          // Google auth will redirect to callback page
          // The callback page will handle the rest
          console.log('✅ Google sign in initiated successfully');
          // Don't reset loading state here - let the redirect happen
        } else {
          console.error('❌ Google sign in failed:', result.error);
          setErrors({ general: result.error || 'Google sign in failed' });
          setIsLoading(false);
          setGoogleAuthInProgress(false);
        }
      } catch (error) {
        console.error('❌ Google sign in error:', error);
        setErrors({ general: 'Google sign in failed. Please try again.' });
        setIsLoading(false);
        setGoogleAuthInProgress(false);
      }

      // Reset Google auth progress after 10 seconds as a safety measure
      setTimeout(() => {
        setGoogleAuthInProgress(false);
      }, 10000);
    } else {
      // TODO: Implement other social providers
      console.log(`${provider} login not implemented yet`);
    }
  };

  const handleVerifyEmail = () => {
    // Navigate to email verification with the current email
    if (onClose) onClose();
    // You might want to open email verification modal here
    // or navigate to a verification page
  };

  if (!isOpen) return null;

  return (
    <div className="login-modal-overlay" onClick={onClose}>
      <div 
        className="login-modal-container" 
        onClick={e => e.stopPropagation()}
        style={{'--modal-bg-image': `url(${loginImage})`}}
      >

        {/* Left side - Form */}
        <div className="modal-form-section">
          <div className="form-content">
            {/* Header */}
            <div className="modal-header">
              <div className="modal-logo">
                <span className="logo-text">VoiceBot Platform</span>
              </div>
              <p className="modal-tagline">Revolutionizing Communication.</p>
            </div>

            {/* Toggle buttons */}
            <div className="auth-toggle">
              <button className="toggle-btn" onClick={onSwitchToSignup}>Sign Up</button>
              <button className="toggle-btn active">Log In</button>
            </div>

            {/* Main content */}
            <div className="form-main">
              <h2 className="form-title">Welcome Back</h2>
              <p className="form-subtitle">Sign in to your VoiceBot account</p>

              {/* Social login buttons */}
              <div className="social-buttons">
                <button 
                  className="social-btn" 
                  onClick={() => handleSocialLogin('apple')}
                  disabled={isLoading}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                  </svg>
                </button>
                <button
                  className="social-btn"
                  onClick={() => handleSocialLogin('google')}
                  disabled={isLoading || googleAuthInProgress}
                  style={googleAuthInProgress ? { opacity: 0.7, cursor: 'not-allowed' } : {}}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                  </svg>
                </button>
                <button 
                  className="social-btn" 
                  onClick={() => handleSocialLogin('twitter')}
                  disabled={isLoading}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </button>
              </div>

              <div className="divider">
                <span>or</span>
              </div>

              {/* Error message */}
              {errors.general && (
                <div className="error-message">
                  {errors.general}
                  {errors.action === 'verify_email' && (
                    <button 
                      type="button" 
                      className="verify-email-btn"
                      onClick={handleVerifyEmail}
                    >
                      Verify Email Now
                    </button>
                  )}
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit} className="login-form">
                <div className="modal-form-group">
                  <input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="Email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                  />
                  <label htmlFor="email">Email</label>
                  {errors.email && <span className="field-error">{errors.email}</span>}
                </div>

                <div className="modal-form-group">
                  <input
                    type="password"
                    id="password"
                    name="password"
                    placeholder="Password"
                    value={formData.password}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                  />
                  <label htmlFor="password">Password</label>
                  {errors.password && <span className="field-error">{errors.password}</span>}
                </div>

                <div className="form-options">
                  <div className="form-check">
                    <input
                      type="checkbox"
                      id="rememberMe"
                      name="rememberMe"
                      checked={formData.rememberMe}
                      onChange={handleInputChange}
                      disabled={isLoading}
                    />
                    <label htmlFor="rememberMe">Remember me</label>
                  </div>
                  <button 
                    type="button" 
                    className="forgot-password-btn" 
                    onClick={() => {
                      console.log('Forgot password clicked!', onForgotPassword);
                      onForgotPassword && onForgotPassword();
                    }}
                    disabled={isLoading}
                  >
                    Forgot Password?
                  </button>
                </div>

                <button 
                  type="submit" 
                  className="submit-btn"
                  disabled={isLoading}
                >
                  {isLoading ? 'Signing In...' : 'Sign In'}
                </button>
              </form>

              <div className="form-footer">
                <p>Don't have an account? <button type="button" onClick={onSwitchToSignup} className="link-btn">Sign Up</button></p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Image */}
        <div className="modal-image-section">
          <img src={loginImage} alt="VoiceBot Login" className="modal-image" />
          <div className="image-overlay">
            {/* Decorative white shapes */}
            <div className="decorative-shape top-left-shape"></div>
            <div className="decorative-shape bottom-right-shape"></div>
            
            <div className="overlay-content">
              <div className="overlay-card">
                <h3>Welcome Back,<br/>Your Way!</h3>
                <p>Continue your journey with personalized communication solutions & seamless experiences.</p>
              </div>
              <div className="bottom-overlay">
                <h3>Access Your Dashboard,<br/>Beyond Boundaries!</h3>
                <p>Resume your communication revolution today!</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginModal; 
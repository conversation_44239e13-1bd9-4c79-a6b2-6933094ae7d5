const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireClient, requireAdmin, checkOwnership } = require('../middleware/auth');

const router = express.Router();

// Create client profile (onboarding)
router.post('/', requireClient, asyncHandler(async (req, res) => {
  const {
    shopName,
    businessType,
    businessSummary,
    whatsappNumber,
    region,
    features = {}
  } = req.body;

  // Validate required fields
  if (!shopName || !businessType || !whatsappNumber) {
    throw new AppError('Shop name, business type, and WhatsApp number are required', 400);
  }

  // Check if user already has a client profile
  const { data: existingClient } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (existingClient) {
    throw new AppError('Client profile already exists', 409);
  }

  const clientId = uuidv4();

  // Create client profile
  const { data: client, error: clientError } = await supabaseAdmin
    .from('clients')
    .insert({
      id: clientId,
      user_id: req.user.id,
      shop_name: shopName,
      business_type: businessType,
      business_summary: businessSummary || '',
      whatsapp_number: whatsappNumber,
      region: region || '',
      status: 'pending',
      config_completed: false,
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (clientError) {
    throw clientError;
  }

  // Create default bot features
  const { error: featuresError } = await supabaseAdmin
    .from('bot_features')
    .insert({
      id: uuidv4(),
      client_id: clientId,
      appointments_enabled: features.appointments || false,
      whatsapp_enabled: features.whatsapp || true,
      payment_enabled: features.payment || false,
      estimate_enabled: features.estimate || false,
      invoice_enabled: features.invoice || false,
      multi_language: features.multiLanguage || false,
      call_summary_enabled: features.callSummary || true,
      created_at: new Date().toISOString()
    });

  if (featuresError) {
    throw featuresError;
  }

  res.status(201).json({
    message: 'Client profile created successfully',
    client
  });
}));

// Get client profile
router.get('/profile', requireClient, asyncHandler(async (req, res) => {
  const { data: client, error } = await supabaseAdmin
    .from('clients')
    .select(`
      *,
      bot_features (*)
    `)
    .eq('user_id', req.user.id)
    .single();

  if (error) {
    throw error;
  }

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  res.json({
    client
  });
}));

// Update client profile
router.put('/profile', requireClient, asyncHandler(async (req, res) => {
  const {
    shopName,
    businessType,
    businessSummary,
    whatsappNumber,
    region
  } = req.body;

  // Get client ID
  const { data: existingClient } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!existingClient) {
    throw new AppError('Client profile not found', 404);
  }

  const updateData = {};
  if (shopName) updateData.shop_name = shopName;
  if (businessType) updateData.business_type = businessType;
  if (businessSummary !== undefined) updateData.business_summary = businessSummary;
  if (whatsappNumber) updateData.whatsapp_number = whatsappNumber;
  if (region !== undefined) updateData.region = region;

  const { data: updatedClient, error } = await supabaseAdmin
    .from('clients')
    .update(updateData)
    .eq('id', existingClient.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.json({
    message: 'Client profile updated successfully',
    client: updatedClient
  });
}));

// Get dashboard overview
router.get('/dashboard/overview', requireClient, asyncHandler(async (req, res) => {
  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const clientId = client.id;

  // Get statistics for the last 7 days
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  // Get call logs count
  const { count: totalCalls } = await supabaseAdmin
    .from('call_logs')
    .select('*', { count: 'exact', head: true })
    .eq('client_id', clientId)
    .gte('call_time', sevenDaysAgo.toISOString());

  // Get unique callers
  const { data: callLogs } = await supabaseAdmin
    .from('call_logs')
    .select('caller_number')
    .eq('client_id', clientId)
    .gte('call_time', sevenDaysAgo.toISOString());

  const uniqueCallers = new Set(callLogs?.map(log => log.caller_number) || []).size;

  // Get WhatsApp messages sent
  const { count: whatsappSent } = await supabaseAdmin
    .from('whatsapp_logs')
    .select('*', { count: 'exact', head: true })
    .eq('client_id', clientId)
    .gte('timestamp', sevenDaysAgo.toISOString());

  // Get upcoming appointments
  const { data: upcomingAppointments } = await supabaseAdmin
    .from('appointment_slots')
    .select('*')
    .eq('client_id', clientId)
    .eq('is_booked', true)
    .gte('slot_time', new Date().toISOString())
    .order('slot_time', { ascending: true })
    .limit(5);

  // Get recent call logs
  const { data: recentCalls } = await supabaseAdmin
    .from('call_logs')
    .select('*')
    .eq('client_id', clientId)
    .order('call_time', { ascending: false })
    .limit(5);

  res.json({
    overview: {
      totalCalls: totalCalls || 0,
      uniqueCallers,
      whatsappSent: whatsappSent || 0,
      upcomingAppointments: upcomingAppointments || [],
      recentCalls: recentCalls || []
    }
  });
}));

// Admin routes
// Get all clients (admin only)
router.get('/admin/all', requireAdmin, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, status, search } = req.query;
  const offset = (page - 1) * limit;

  let query = supabaseAdmin
    .from('clients')
    .select(`
      *,
      profiles!clients_user_id_fkey (
        full_name,
        email
      )
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (status) {
    query = query.eq('status', status);
  }

  if (search) {
    query = query.or(`shop_name.ilike.%${search}%,business_type.ilike.%${search}%`);
  }

  const { data: clients, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('clients')
    .select('*', { count: 'exact', head: true });

  if (status) {
    countQuery = countQuery.eq('status', status);
  }

  if (search) {
    countQuery = countQuery.or(`shop_name.ilike.%${search}%,business_type.ilike.%${search}%`);
  }

  const { count } = await countQuery;

  res.json({
    clients: clients || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Update client status (admin only)
router.put('/admin/:clientId/status', requireAdmin, asyncHandler(async (req, res) => {
  const { clientId } = req.params;
  const { status, plivoNumber } = req.body;

  if (!['pending', 'active', 'inactive'].includes(status)) {
    throw new AppError('Invalid status', 400);
  }

  const updateData = { status };
  if (plivoNumber) {
    updateData.plivo_number = plivoNumber;
  }

  const { data: updatedClient, error } = await supabaseAdmin
    .from('clients')
    .update(updateData)
    .eq('id', clientId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.json({
    message: 'Client status updated successfully',
    client: updatedClient
  });
}));

module.exports = router; 
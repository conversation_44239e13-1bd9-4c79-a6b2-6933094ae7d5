import React from 'react';
import { Link } from 'react-router-dom';
import { Users, Phone, MessageSquare, ShoppingCart, CheckCircle } from 'lucide-react';

const LandingPage = () => {
  return (
    <div className="landing-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <header style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <Users size={32} style={{ color: '#3b82f6' }} />
            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>Customer Portal</h1>
          </div>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <Link 
              to="/login" 
              style={{ 
                padding: '0.5rem 1rem', 
                backgroundColor: '#3b82f6', 
                color: 'white', 
                textDecoration: 'none', 
                borderRadius: '4px',
                fontWeight: '500'
              }}
            >
              Login
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section style={{ padding: '4rem 1rem', textAlign: 'center' }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <h2 style={{ fontSize: '3rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '1rem' }}>
            Welcome to Your Customer Portal
          </h2>
          <p style={{ fontSize: '1.25rem', color: '#6b7280', marginBottom: '2rem' }}>
            Access your orders, chat with our team, and manage your account all in one place.
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link 
              to="/register" 
              style={{ 
                padding: '0.75rem 2rem', 
                backgroundColor: '#3b82f6', 
                color: 'white', 
                textDecoration: 'none', 
                borderRadius: '8px',
                fontSize: '1.125rem',
                fontWeight: '600'
              }}
            >
              Get Started
            </Link>
            <Link 
              to="/login" 
              style={{ 
                padding: '0.75rem 2rem', 
                backgroundColor: 'white', 
                color: '#3b82f6', 
                textDecoration: 'none', 
                borderRadius: '8px',
                fontSize: '1.125rem',
                fontWeight: '600',
                border: '2px solid #3b82f6'
              }}
            >
              Sign In
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section style={{ padding: '4rem 1rem', backgroundColor: 'white' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h3 style={{ fontSize: '2rem', fontWeight: 'bold', textAlign: 'center', marginBottom: '3rem', color: '#1f2937' }}>
            Everything You Need
          </h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '2rem' }}>
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <Phone size={48} style={{ color: '#3b82f6', margin: '0 auto 1rem' }} />
              <h4 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem', color: '#1f2937' }}>Call History</h4>
              <p style={{ color: '#6b7280' }}>View all your call interactions and history with our team.</p>
            </div>
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <MessageSquare size={48} style={{ color: '#10b981', margin: '0 auto 1rem' }} />
              <h4 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem', color: '#1f2937' }}>Live Chat</h4>
              <p style={{ color: '#6b7280' }}>Chat directly with our team for instant support and assistance.</p>
            </div>
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <ShoppingCart size={48} style={{ color: '#f59e0b', margin: '0 auto 1rem' }} />
              <h4 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem', color: '#1f2937' }}>Order Management</h4>
              <p style={{ color: '#6b7280' }}>Track your orders and manage your purchases easily.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{ backgroundColor: '#1f2937', color: 'white', padding: '2rem 1rem', textAlign: 'center' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <p>&copy; 2024 Customer Portal. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;

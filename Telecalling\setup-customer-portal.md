# Customer Portal Setup Guide

## 🚀 Quick Setup Instructions

### 1. **Run Database Migration**
```sql
-- In your Supabase SQL editor, run:
\i Telecalling/backend/database/customer_portal_migration.sql
```

### 2. **Install Customer Portal Dependencies**
```bash
cd Telecalling/customer-portal
npm install
```

### 3. **Start Customer Portal (Port 4000)**
```bash
cd Telecalling/customer-portal
npm start
```

### 4. **Restart Backend Server**
```bash
cd Telecalling/backend
npm start
```

## 🎯 **What You Get**

### **Customer Portal (Port 4000)**
- **Registration Page**: `/register` or `/register/:token`
- **Email Verification**: OTP-based verification
- **Customer Dashboard**: Orders, chats, profile management
- **Business Communication**: Chat with businesses

### **Registration Link Generation**
Add this function to your client dashboard to generate registration links:

```javascript
// In your client dashboard, add this function
const generateRegistrationLink = async (phoneNumber, callLogId) => {
  try {
    const response = await api.post('/api/customers/generate-registration-link', {
      phoneNumber,
      callLogId
    });
    
    if (response.data.success) {
      const link = `http://localhost:4000/register/${response.data.token}`;
      console.log('Registration link:', link);
      // Send this link via SMS to customer
      return link;
    }
  } catch (error) {
    console.error('Failed to generate registration link:', error);
  }
};
```

## 🔄 **Customer Journey Flow**

### **1. Call Happens**
```
Customer calls business → Voice bot answers → Customer shows interest
```

### **2. Registration Link Sent**
```
Bot generates registration link → Sends via SMS → Customer clicks link
```

### **3. Customer Registration**
```
Customer fills form → Email verification → Account created → Linked to business
```

### **4. Multi-Business Support**
```
Customer calls another business → System recognizes email → No re-registration needed
```

## 📱 **Testing the System**

### **Test Registration Flow**
1. **Generate Token**: Create a registration token in database
2. **Visit Link**: `http://localhost:4000/register/your-token-here`
3. **Fill Form**: Complete registration form
4. **Verify Email**: Check email for OTP and verify
5. **Access Dashboard**: Login and see customer dashboard

### **Test Multi-Business**
1. **Register with Business A**: Complete registration
2. **Call Business B**: Use same email
3. **System Recognition**: Should link to existing customer account

## 🗄️ **Database Tables Created**

- **customers**: Customer profiles and information
- **customer_interactions**: Links customers to businesses
- **customer_registration_tokens**: Secure registration links
- **customer_orders**: Orders and appointments
- **customer_chats**: Business-customer conversations
- **chat_messages**: Individual chat messages
- **customer_email_tokens**: Email verification OTPs
- **customer_sessions**: Authentication sessions

## 🔐 **Security Features**

- **Email-based authentication** with OTP verification
- **Secure registration tokens** with expiration
- **JWT-based sessions** for customer authentication
- **Business isolation** - customers only see relevant data
- **Cross-business recognition** - one account, multiple businesses

## 🎨 **UI Features**

- **Responsive design** - works on mobile and desktop
- **Beautiful gradients** - professional look and feel
- **Form validation** - client and server-side validation
- **Loading states** - smooth user experience
- **Error handling** - clear error messages

## 🚀 **Next Steps**

1. **Run the setup commands above**
2. **Test the registration flow**
3. **Integrate with your voice bot** to send registration links
4. **Customize the UI** to match your branding
5. **Add more features** like order management, chat system

The customer portal is now ready to use! 🎉

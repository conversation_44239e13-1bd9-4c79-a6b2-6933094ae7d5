/* Plan 499 Dashboard Styles */
.plan499-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden; /* Prevent horizontal scroll */
}

body .plan499-dashboard {
  background: white !important;
}

/* Responsive Utilities */
.mobile-hidden {
  display: block;
}

.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

/* Touch-friendly elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Dashboard Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.header-content p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0.5rem 0 0 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #1e293b;
}

.user-email {
  font-size: 0.75rem;
  color: #64748b;
}

.plan-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.logout-btn {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #dc2626;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

/* Navigation */
.dashboard-nav {
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  gap: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.dashboard-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: transparent;
  border: 2px solid transparent;
  border-radius: 12px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.nav-button:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateY(-2px);
}

.nav-button.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Dashboard Content */
.dashboard-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Overview Section */
.overview-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-card.primary .stat-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card.secondary .stat-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-card.success .stat-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-content h3 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #64748b;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0.5rem 0;
}

.stat-label {
  font-size: 0.85rem;
  color: #64748b;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.chart-container {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-container h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

/* Call Logs Section */
.call-logs-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  padding: 1.5rem 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.analytics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.analytics-card {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.analytics-card:hover {
  transform: translateY(-3px);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.card-icon.success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.card-icon.danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.card-icon.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.card-content h4 {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  text-transform: uppercase;
  font-weight: 600;
}

.card-content .value {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0.5rem 0;
}

.change {
  font-size: 0.85rem;
  font-weight: 500;
}

.change.positive {
  color: #10b981;
}

.change.negative {
  color: #ef4444;
}

.calls-chart {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.calls-chart h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

/* Tables */
.calls-table, .tickets-table {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.recent-calls h3, .tickets-list h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

/* Table Container for Responsive */
.table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px; /* Minimum width for proper display */
}

thead {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

thead th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

tbody td {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  color: #1e293b;
}

tbody tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status.completed {
  color: #10b981;
}

.status.missed {
  color: #ef4444;
}

/* WhatsApp Section */
.whatsapp-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.whatsapp-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #10b981;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.whatsapp-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-item {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-item h4 {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  text-transform: uppercase;
  font-weight: 600;
}

.stat-item p {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0.5rem 0 0 0;
}

.message-interface {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.messages-list, .compose-message {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.message-item {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.message-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.message-item.received {
  border-left: 4px solid #667eea;
}

.message-item.sent {
  border-left: 4px solid #10b981;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.customer-name {
  font-weight: 600;
  color: #1e293b;
}

.message-time {
  font-size: 0.85rem;
  color: #64748b;
}

.message-text {
  color: #475569;
  margin: 0.5rem 0;
  line-height: 1.5;
}

/* Product Section */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.product-card {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.product-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.product-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.product-status.active {
  background: #dcfce7;
  color: #166534;
}

.product-status.inactive {
  background: #fee2e2;
  color: #991b1b;
}

.product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin: 0.5rem 0;
}

.product-category {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

/* Support Section */
.support-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.support-contact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-weight: 500;
}

.support-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.support-stat {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.support-stat h4 {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0;
  text-transform: uppercase;
  font-weight: 600;
}

.support-stat p {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0.5rem 0 0 0;
}

.create-ticket, .tickets-list {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.ticket-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Form Elements */
.form-input, .form-textarea, .form-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-edit, .btn-delete {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 0.9rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #e2e8f0;
  color: #334155;
}

.btn-edit {
  background: #dbeafe;
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
}

.btn-edit:hover {
  background: #bfdbfe;
}

.btn-delete {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.btn-delete:hover {
  background: #fecaca;
}

/* Status and Priority Badges */
.priority {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority.low {
  background: #f0f9ff;
  color: #0369a1;
}

.priority.medium {
  background: #fef3c7;
  color: #d97706;
}

.priority.high {
  background: #fee2e2;
  color: #dc2626;
}

.priority.critical {
  background: #fdf2f8;
  color: #be185d;
}

.ticket-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.ticket-status.open {
  background: #fee2e2;
  color: #dc2626;
}

.ticket-status.in-progress {
  background: #fef3c7;
  color: #d97706;
}

.ticket-status.resolved {
  background: #dcfce7;
  color: #166534;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-content h1 {
    font-size: 2rem;
  }

  .dashboard-nav {
    padding: 1rem;
    justify-content: flex-start;
  }

  .dashboard-content {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-section {
    grid-template-columns: 1fr;
  }

  .message-interface {
    grid-template-columns: 1fr;
  }

  .analytics-cards {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .support-stats {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .header-content h1 {
    font-size: 1.5rem;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .chart-container {
    padding: 1rem;
  }

  .nav-button {
    padding: 0.5rem 1rem;
  }
}

/* Catalog Info */
.catalog-info {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
}

.info-card h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.info-card p {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin: 0 0 1rem 0;
}

/* Enhanced Profile Section Styles */
.profile-section {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  margin: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

.profile-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.profile-section .header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.profile-section .header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.profile-section .header-text h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.profile-section .header-text p {
  font-size: 1rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

.profile-section .btn-cancel {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.profile-section .btn-cancel:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.profile-cards {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.profile-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.profile-card.main-card {
  grid-row: span 2;
}

.profile-card .card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.profile-card .card-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.profile-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* Form Styles */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.form-input, .form-textarea {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.save-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.save-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

.cancel-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Info Grid Styles */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.info-item.full-width {
  grid-column: span 2;
}

.info-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-content label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  display: block;
  margin-bottom: 0.25rem;
}

.info-content span {
  font-size: 1rem;
  color: #1f2937;
  font-weight: 500;
}

/* Settings Card Styles */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.setting-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.setting-text h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.setting-text p {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Phone Card Styles */
.phone-number-content {
  padding: 1rem 0;
}

.assigned-number-display {
  text-align: center;
}

.number-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  margin-bottom: 1.5rem;
}

.number-info {
  text-align: left;
}

.number-info .number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  display: block;
}

.number-info .status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 600;
  margin-top: 0.5rem;
}

.number-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.no-number {
  text-align: center;
  padding: 2rem;
}

.no-number-icon {
  color: #f59e0b;
  margin-bottom: 1rem;
}

.no-number-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.no-number-content p {
  color: #6b7280;
  margin: 0 0 1.5rem 0;
}

.contact-support {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* ========================================
   RESPONSIVE DESIGN - COMPREHENSIVE
   ======================================== */

/* Large Desktop (1200px+) */
@media (min-width: 1200px) {
  .dashboard-content {
    max-width: 1400px;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .charts-section {
    grid-template-columns: 2fr 1fr;
  }
}

/* Desktop (992px - 1199px) */
@media (max-width: 1199px) {
  .dashboard-header {
    padding: 1.5rem;
  }

  .header-content h1 {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .charts-section {
    grid-template-columns: 1fr 1fr;
  }
}

/* Tablet (768px - 991px) */
@media (max-width: 991px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
    text-align: center;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .dashboard-nav {
    padding: 0.5rem 1rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .nav-button {
    white-space: nowrap;
    min-width: auto;
  }

  .dashboard-content {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .charts-section {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .analytics-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .profile-cards {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-item.full-width {
    grid-column: span 1;
  }
}

/* Mobile Large (576px - 767px) */
@media (max-width: 767px) {
  .dashboard-header {
    padding: 0.75rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .header-content p {
    font-size: 0.9rem;
  }

  .user-info {
    padding: 0.25rem 0.5rem;
    gap: 0.5rem;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .user-details {
    display: none; /* Hide user details on small screens */
  }

  .plan-badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .dashboard-nav {
    padding: 0.5rem;
    gap: 0.25rem;
  }

  .nav-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .nav-button span {
    display: none; /* Hide text, show only icons */
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .analytics-cards {
    grid-template-columns: 1fr;
  }

  .analytics-card {
    padding: 1rem;
  }

  .chart-container {
    padding: 1rem;
  }

  .chart-container h3 {
    font-size: 1rem;
  }

  /* Table responsive */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  table {
    min-width: 600px;
  }

  thead th {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  tbody td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  /* Products grid */
  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-card {
    padding: 1rem;
  }

  /* Support stats */
  .support-stats {
    grid-template-columns: 1fr;
  }

  /* Forms */
  .ticket-form {
    gap: 0.75rem;
  }

  .form-group {
    margin-bottom: 0.75rem;
  }

  .form-group label {
    font-size: 0.875rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  /* Buttons */
  .btn-primary,
  .btn-secondary,
  .btn-edit,
  .btn-delete {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  /* Section headers */
  .section-header {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
    padding: 1rem;
  }

  .section-header h2 {
    font-size: 1.25rem;
  }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 575px) {
  .dashboard-header {
    padding: 0.5rem;
  }

  .header-content h1 {
    font-size: 1.25rem;
  }

  .header-actions {
    gap: 0.5rem;
  }

  .user-info {
    padding: 0.25rem;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
  }

  .plan-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
  }

  .logout-btn {
    padding: 0.25rem;
  }

  .dashboard-content {
    padding: 0.5rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .chart-container {
    padding: 0.75rem;
  }

  .analytics-card {
    padding: 0.75rem;
  }

  .product-card {
    padding: 0.75rem;
  }

  /* Ultra compact navigation */
  .nav-button {
    padding: 0.5rem;
    min-width: 44px; /* Touch target size */
  }

  /* Compact forms */
  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 0.375rem;
  }

  /* Compact buttons */
  .btn-primary,
  .btn-secondary,
  .btn-edit,
  .btn-delete {
    padding: 0.375rem 0.5rem;
    font-size: 0.7rem;
  }
}

/* Landscape phone orientation */
@media (max-width: 767px) and (orientation: landscape) {
  .dashboard-header {
    padding: 0.5rem 1rem;
  }

  .header-content h1 {
    font-size: 1.25rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .nav-button span {
    display: inline; /* Show text in landscape */
  }
}

/* Print styles */
@media print {
  .dashboard-header,
  .dashboard-nav,
  .logout-btn,
  .btn-primary,
  .btn-secondary,
  .btn-edit,
  .btn-delete {
    display: none !important;
  }

  .dashboard-content {
    padding: 0;
    max-width: none;
  }

  .stat-card,
  .chart-container,
  .analytics-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

/* Billing Section Styles */
.billing-section {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.billing-cards {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.billing-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.billing-card.current-plan {
  background: linear-gradient(135deg, #d4d7e3 0%, #ffffff 100%);
  color: white;
}

.billing-card.current-plan .card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.plan-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(228, 206, 6);
}

.plan-info h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.plan-info p {
  margin: 0;
  opacity: 0.9;
  color: black;
}

.plan-price {
  margin-left: auto;
  text-align: right;
  color: black;
}

.plan-price .price {
  font-size: 2rem;
  font-weight: 700;
  display: block;
}

.plan-price .period {
  font-size: 0.9rem;
  opacity: 0.8;
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(7, 0, 0, 0.9);
}

.renewal-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 0.9rem;
  color: black;
}

.payment-stats h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.25rem;
}

.stats-grid {
  display: grid;
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.stat-value.status.active {
  color: #10b981;
}

.stat-value.status.free {
  color: #6b7280;
}

.available-plans {
  margin-bottom: 3rem;
}

.available-plans h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.plan-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.plan-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.plan-card.current {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
}

.plan-card.popular {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.plan-header h4 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.plan-description {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.plan-features {
  margin-bottom: 1.5rem;
}

.plan-features .feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #555;
  font-size: 0.9rem;
}

.plan-features .feature svg {
  color: #10b981;
}

.plan-action {
  margin-top: auto;
}

.btn-upgrade {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-upgrade:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-current {
  width: 100%;
  background: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: default;
}

.btn-disabled {
  width: 100%;
  background: #e5e7eb;
  color: #9ca3af;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: not-allowed;
}

.payment-history {
  margin-bottom: 3rem;
}

.payment-history h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
}

.payments-table {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.payments-table table {
  width: 100%;
  border-collapse: collapse;
}

.payments-table th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.payments-table td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  color: #374151;
}

.payment-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.payment-status.paid {
  background: #dcfce7;
  color: #166534;
}

.payment-status.failed {
  background: #fee2e2;
  color: #991b1b;
}

.payment-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.payment-id {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.8rem;
  color: #6b7280;
}

.no-payments {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 16px;
  color: #6b7280;
}

.no-payments svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.upgrade-cta {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 2rem;
  color: white;
}

.cta-content {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.cta-text h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.cta-text p {
  margin: 0;
  opacity: 0.9;
}

.cta-button {
  background: white;
  color: #667eea;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* Responsive Design for Billing */
@media (max-width: 768px) {
  .billing-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
  }

  .cta-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .cta-button {
    margin-left: 0;
  }

  .payments-table {
    overflow-x: auto;
  }
}

/* Usage Limits Section Styling */
.usage-limits-section {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.usage-card {
  flex: 1 1 45%;
  background-color: #f9fafb;
  border-radius: 0;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: transform 0.3s ease;
  cursor: default;
}

.usage-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.usage-card h3 {
  font-weight: 600;
  font-size: 1.25rem;
  color: #111827;
  margin-bottom: 1rem;
}

.usage-card p {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.25rem;
}

.usage-card span {
  color: #6b7280;
  font-weight: 500;
}

.usage-icon {
  color: #3b82f6;
  margin-bottom: 1rem;
  width: 32px;
  height: 32px;
}

.usage-card .icon-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #374151;
  font-weight: 600;
  font-size: 1rem;
}

/* Product Selection Styles */
.product-card.selected {
  border: 2px solid #007bff !important;
  background: #f8f9ff !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15) !important;
}

.selection-controls {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.selection-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #007bff;
  transform: scale(1.1);
}

.selection-checkbox:hover {
  transform: scale(1.2);
}

.bulk-delete-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bulk-delete-btn:hover {
  background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.selection-mode-indicator {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  border: 1px solid #2196f3;
}
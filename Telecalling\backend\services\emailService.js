// Email Service - Uses existing Supabase edge function with Resend.com
const fetch = require('node-fetch');

class EmailService {
  constructor() {
    this.supabaseUrl = process.env.SUPABASE_URL;
    this.serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!this.supabaseUrl || !this.serviceRoleKey) {
      console.warn('⚠️ Email service not fully configured - some features may not work');
    }
  }

  // Send OTP email using existing Supabase edge function
  async sendOTPEmail(email, otp, type = 'email_verification', userName = '', businessName = '') {
    try {
      if (!this.supabaseUrl || !this.serviceRoleKey) {
        throw new Error('Email service not configured');
      }

      const endpoint = `${this.supabaseUrl.replace(/\/$/, '')}/functions/v1/send-otp-email`;
      
      console.log('📧 Sending OTP email via Supabase edge function:', {
        email,
        type,
        userName,
        businessName
      });

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.serviceRoleKey}`
        },
        body: JSON.stringify({
          email,
          otp,
          type,
          userName,
          businessName
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Supabase edge function error:', errorText);
        throw new Error(`Email sending failed: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ OTP email sent successfully:', result);
      
      return {
        success: true,
        messageId: result.emailId,
        message: 'Email sent successfully'
      };
    } catch (error) {
      console.error('❌ Email sending error:', error);
      throw error;
    }
  }

  // Generic email sending function (for custom emails)
  async sendEmail({ to, subject, html, text = null }) {
    try {
      if (!process.env.RESEND_API_KEY) {
        throw new Error('RESEND_API_KEY not configured');
      }

      console.log('📧 Sending custom email:', {
        to,
        subject: subject.substring(0, 50) + '...'
      });

      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: 'Customer Portal <<EMAIL>>',
          to: Array.isArray(to) ? to : [to],
          subject: subject,
          html: html,
          text: text || this.htmlToText(html)
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Resend API error:', errorData);
        throw new Error(`Email sending failed: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Custom email sent successfully:', result);
      
      return {
        success: true,
        messageId: result.id,
        message: 'Email sent successfully'
      };
    } catch (error) {
      console.error('❌ Custom email sending error:', error);
      throw error;
    }
  }

  // Send customer verification email
  async sendCustomerVerificationEmail(email, otp, customerName) {
    return this.sendOTPEmail(email, otp, 'email_verification', customerName, 'Customer Portal');
  }

  // Send customer login OTP
  async sendCustomerLoginOTP(email, otp, customerName) {
    return this.sendOTPEmail(email, otp, 'login_2fa', customerName, 'Customer Portal');
  }

  // Send customer password reset
  async sendCustomerPasswordReset(email, otp, customerName) {
    return this.sendOTPEmail(email, otp, 'password_reset', customerName, 'Customer Portal');
  }

  // Send support ticket notification
  async sendSupportTicketEmail(ticketData) {
    try {
      if (!this.supabaseUrl || !this.serviceRoleKey) {
        console.warn('⚠️ Support email not sent - service not configured');
        return { success: false, message: 'Email service not configured' };
      }

      const endpoint = `${this.supabaseUrl.replace(/\/$/, '')}/functions/v1/send-support-ticket-email`;
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.serviceRoleKey}`
        },
        body: JSON.stringify(ticketData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Support email error:', errorText);
        throw new Error(`Support email failed: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('❌ Support email error:', error);
      throw error;
    }
  }

  // Utility function to convert HTML to plain text (basic)
  htmlToText(html) {
    if (!html) return '';
    
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace &amp; with &
      .replace(/&lt;/g, '<') // Replace &lt; with <
      .replace(/&gt;/g, '>') // Replace &gt; with >
      .replace(/&quot;/g, '"') // Replace &quot; with "
      .replace(/&#39;/g, "'") // Replace &#39; with '
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();
  }

  // Test email configuration
  async testConfiguration() {
    const tests = {
      supabaseUrl: !!this.supabaseUrl,
      serviceRoleKey: !!this.serviceRoleKey,
      resendApiKey: !!process.env.RESEND_API_KEY
    };

    console.log('📧 Email service configuration test:', tests);
    
    return {
      configured: tests.supabaseUrl && tests.serviceRoleKey,
      resendConfigured: tests.resendApiKey,
      details: tests
    };
  }
}

// Export singleton instance
const emailService = new EmailService();

// Export both the instance and the class
module.exports = {
  sendEmail: emailService.sendEmail.bind(emailService),
  sendOTPEmail: emailService.sendOTPEmail.bind(emailService),
  sendCustomerVerificationEmail: emailService.sendCustomerVerificationEmail.bind(emailService),
  sendCustomerLoginOTP: emailService.sendCustomerLoginOTP.bind(emailService),
  sendCustomerPasswordReset: emailService.sendCustomerPasswordReset.bind(emailService),
  sendSupportTicketEmail: emailService.sendSupportTicketEmail.bind(emailService),
  testConfiguration: emailService.testConfiguration.bind(emailService),
  EmailService
};

// For backward compatibility, also export the sendEmail function as default
module.exports.default = emailService.sendEmail.bind(emailService);

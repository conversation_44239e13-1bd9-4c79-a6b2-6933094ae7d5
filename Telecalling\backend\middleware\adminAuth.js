const jwt = require('jsonwebtoken');
const { dbHelpers } = require('../config/supabase');

// Admin authentication middleware
const adminAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'No admin token provided' 
      });
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Ensure it's an admin access token
      if (decoded.type !== 'admin_access') {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Invalid admin token type' 
        });
      }
      
      // Get admin from database
      const admin = await dbHelpers.findOne('admins', { id: decoded.adminId });
      
      if (!admin || !admin.is_active) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Admin account not found or inactive' 
        });
      }
      
      // Verify session exists and is active
      const session = await dbHelpers.findOne('admin_sessions', { 
        admin_id: decoded.adminId,
        session_token: token,
        is_active: true
      });
      
      if (!session) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Invalid admin session' 
        });
      }
      
      // Check if session has expired
      if (new Date() > new Date(session.expires_at)) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Admin session expired' 
        });
      }
      
      // Add admin info to request
      req.admin = {
        id: admin.id,
        adminId: admin.admin_id,
        name: admin.name,
        email: admin.email,
        isActive: admin.is_active
      };
      
      next();
    } catch (jwtError) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Invalid admin token' 
      });
    }
  } catch (error) {
    console.error('Admin auth middleware error:', error);
    return res.status(500).json({ 
      error: 'Authentication error', 
      message: 'Internal server error' 
    });
  }
};

// Optional admin authentication (doesn't fail if no token)
const optionalAdminAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      if (decoded.type === 'admin_access') {
        const admin = await dbHelpers.findOne('admins', { id: decoded.adminId });
        
        if (admin && admin.is_active) {
          req.admin = {
            id: admin.id,
            adminId: admin.admin_id,
            name: admin.name,
            email: admin.email,
            isActive: admin.is_active
          };
        }
      }
    } catch (jwtError) {
      // Token is invalid but we continue without admin authentication
      console.log('Optional admin auth - invalid token:', jwtError.message);
    }
    
    next();
  } catch (error) {
    console.error('Optional admin auth error:', error);
    next();
  }
};

module.exports = {
  adminAuth,
  optionalAdminAuth
};

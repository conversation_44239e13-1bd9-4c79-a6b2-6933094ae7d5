# 🔑 Key-Value Pair Extraction Guide

## 🎯 **How AI Extracts Product Features**

The AI system now properly extracts **ALL** product features as key-value pairs, exactly matching your product structure requirements.

## 📊 **Example: CSV/Excel Processing**

### **Input CSV:**
```csv
Product Name,Price,Brand,Model,Color,Size,Material,Warranty
iPhone 15 Pro,999,Apple,15 Pro,Titanium Blue,6.1 inch,Titanium,1 Year
Samsung Galaxy S24,899,Samsung,Galaxy S24,Phantom Black,6.2 inch,Aluminum,2 Years
```

### **AI Extraction Result:**
```json
{
  "products": [
    {
      "name": "iPhone 15 Pro",
      "price": 999,
      "category": "Electronics",
      "features": {
        "Brand": "Apple",
        "Model": "15 Pro", 
        "Color": "Titanium Blue",
        "Size": "6.1 inch",
        "Material": "Titanium",
        "Warranty": "1 Year"
      },
      "availability": "available",
      "alias1": "Apple Phone",
      "alias2": "Latest iPhone"
    }
  ]
}
```

### **Database Storage:**
```sql
-- products table
product_name: "iPhone 15 Pro"  -- Name stored in separate column
product_details: {
  "price": 999,
  "category": "Electronics",
  "availability": "available",
  "Brand": "Apple",           -- Feature key-value pairs
  "Model": "15 Pro",
  "Color": "Titanium Blue",
  "Size": "6.1 inch",
  "Material": "Titanium",
  "Warranty": "1 Year"
}
alias_names: ["Apple Phone", "Latest iPhone"]
```

## 🖼️ **Example: Image Processing**

### **Input:** Product image of a laptop

### **AI Vision Extraction:**
```json
{
  "name": "MacBook Pro 16",
  "price": 2499,
  "category": "Computers",
  "features": {
    "Brand": "Apple",
    "Model": "MacBook Pro 16",
    "Color": "Space Gray",
    "Screen Size": "16 inch",
    "Processor": "M3 Max",
    "RAM": "32GB",
    "Storage": "1TB SSD",
    "Ports": "3x USB-C, HDMI, SD Card"
  },
  "alias1": "Apple Laptop",
  "alias2": "Mac Computer"
}
```

## 📄 **Example: PDF/Word Document**

### **Input:** Product catalog with specifications

### **AI Text Extraction:**
```json
{
  "name": "Sony WH-1000XM5",
  "price": 399,
  "category": "Audio",
  "features": {
    "Brand": "Sony",
    "Model": "WH-1000XM5",
    "Type": "Over-ear Headphones",
    "Noise Cancellation": "Active",
    "Battery Life": "30 hours",
    "Connectivity": "Bluetooth 5.2",
    "Weight": "250g",
    "Colors Available": "Black, Silver"
  },
  "alias1": "Sony Headphones",
  "alias2": "Noise Cancelling Headphones"
}
```

## 🎯 **Key Features of the System**

### **✅ Dynamic Column Detection**
- **4 columns in Excel** → **4 key-value pairs** in features
- **10 columns in CSV** → **10 key-value pairs** in features
- **Any number of features** → **Exact same number** of key-value pairs

### **✅ Smart Key Naming**
- Uses **exact column names** from spreadsheets
- **Capitalizes keys** for consistency
- **Descriptive keys** for images/documents

### **✅ Comprehensive Extraction**
- **Every visible feature** becomes a key-value pair
- **No information loss** - all details preserved
- **Structured format** ready for database storage

## 🔧 **Technical Implementation**

### **AI Prompt Enhancement:**
```
CRITICAL RULES for features:
- Extract ALL product attributes as key-value pairs
- If data has 4 columns, create 4 key-value pairs  
- If data has 10 columns, create 10 key-value pairs
- Use exact column names as keys
- For missing info, omit the key entirely
```

### **Database Structure:**
```sql
-- products table structure
product_name: VARCHAR          -- Product name stored separately
product_details: JSONB {
  "price": number,
  "category": "category",
  "availability": "status",
  -- ALL EXTRACTED FEATURES AS KEY-VALUE PAIRS --
  "Brand": "brand name",
  "Model": "model name",
  "Color": "color",
  "Size": "dimensions",
  "Material": "material type",
  "Warranty": "warranty period",
  "Features": "key features",
  -- ... any other extracted features
}
alias_names: TEXT[]            -- Smart aliases
```

## 📋 **Testing Examples**

### **Test 1: Simple CSV (4 columns)**
```csv
Name,Price,Brand,Color
iPhone,999,Apple,Blue
```
**Result:** 4 key-value pairs (Name, Price, Brand, Color)

### **Test 2: Complex Excel (8 columns)**
```csv
Product,Price,Brand,Model,Color,Size,Weight,Warranty
Laptop,1299,Dell,XPS,Silver,13inch,1.2kg,3 Years
```
**Result:** 8 key-value pairs (all columns extracted)

### **Test 3: Product Image**
**Input:** Photo of sneakers
**Result:** AI extracts visible features (Brand, Model, Color, Size, Material, etc.)

## 🎉 **Expected Results**

When you upload files now:

1. **✅ All columns/features extracted** as separate key-value pairs
2. **✅ Product details JSONB** contains all information
3. **✅ Frontend displays** all key-value pairs properly
4. **✅ Search works** with all extracted features
5. **✅ No data loss** - every detail preserved

## 🧪 **How to Test**

1. **Create test CSV** with multiple columns (Brand, Model, Color, Size, etc.)
2. **Upload via AI Upload** button in Products page
3. **Check results** - should see all columns as key-value pairs
4. **Verify in database** - product_details should contain all features
5. **Test search** - should find products by any feature

The AI now properly understands your key-value pair structure and extracts every feature as a separate key-value pair! 🚀

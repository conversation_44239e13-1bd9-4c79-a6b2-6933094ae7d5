-- Testing Mode: 4-Hour Plan Validity
-- Run this in your Supabase SQL editor to enable 4-hour testing mode

-- 1. Update existing active subscriptions to expire in 4 hours from now
UPDATE client_subscriptions 
SET 
    ends_at = NOW() + INTERVAL '4 hours',
    updated_at = NOW()
WHERE 
    status = 'active' 
    AND ends_at > NOW();

-- 2. Create a function to automatically set 4-hour validity for new subscriptions
CREATE OR REPLACE FUNCTION set_testing_subscription_duration()
RETURNS TRIGGER AS $$
BEGIN
    -- Only apply to new subscriptions (INSERT) or when status changes to 'active'
    IF (TG_OP = 'INSERT' AND NEW.status = 'active') OR 
       (TG_OP = 'UPDATE' AND OLD.status != 'active' AND NEW.status = 'active') THEN
        
        -- Set subscription to expire in 4 hours from now
        NEW.starts_at = NOW();
        NEW.ends_at = NOW() + INTERVAL '4 hours';
        NEW.updated_at = NOW();
        
        -- Log the testing mode activation
        INSERT INTO system_logs (
            log_type,
            message,
            metadata,
            created_at
        ) VALUES (
            'testing_mode',
            'Subscription set to 4-hour testing mode',
            jsonb_build_object(
                'client_id', NEW.client_id,
                'subscription_id', NEW.id,
                'plan_id', NEW.plan_id,
                'expires_at', NEW.ends_at
            ),
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. Create the trigger for testing mode
DROP TRIGGER IF EXISTS testing_mode_subscription_trigger ON client_subscriptions;
CREATE TRIGGER testing_mode_subscription_trigger
    BEFORE INSERT OR UPDATE ON client_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION set_testing_subscription_duration();

-- 4. Create system_logs table if it doesn't exist (for logging testing mode activations)
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    log_type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create a function to check and deactivate expired subscriptions
CREATE OR REPLACE FUNCTION deactivate_expired_subscriptions()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    -- Update expired subscriptions
    UPDATE client_subscriptions 
    SET 
        status = 'expired',
        updated_at = NOW()
    WHERE 
        status = 'active' 
        AND ends_at <= NOW();
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    
    -- Log the deactivation
    IF expired_count > 0 THEN
        INSERT INTO system_logs (
            log_type,
            message,
            metadata,
            created_at
        ) VALUES (
            'subscription_expiry',
            'Expired subscriptions deactivated',
            jsonb_build_object(
                'expired_count', expired_count,
                'deactivated_at', NOW()
            ),
            NOW()
        );
    END IF;
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- 6. Create a scheduled function to run every 5 minutes to check for expired subscriptions
-- Note: You'll need to set up a cron job or use Supabase's pg_cron extension for this
-- For now, you can manually run: SELECT deactivate_expired_subscriptions();

-- 7. View to easily check current subscription status
CREATE OR REPLACE VIEW subscription_status_view AS
SELECT 
    cs.id,
    cs.client_id,
    c.business_name,
    c.email as client_email,
    sp.display_name as plan_name,
    cs.status,
    cs.starts_at,
    cs.ends_at,
    CASE 
        WHEN cs.ends_at <= NOW() THEN 'EXPIRED'
        WHEN cs.ends_at <= NOW() + INTERVAL '1 hour' THEN 'EXPIRING_SOON'
        ELSE 'ACTIVE'
    END as expiry_status,
    EXTRACT(EPOCH FROM (cs.ends_at - NOW()))/3600 as hours_remaining,
    cs.created_at,
    cs.updated_at
FROM client_subscriptions cs
JOIN clients c ON cs.client_id = c.id
JOIN subscription_plans sp ON cs.plan_id = sp.id
WHERE cs.status IN ('active', 'expired')
ORDER BY cs.ends_at ASC;

-- 8. Function to manually extend a subscription for testing
CREATE OR REPLACE FUNCTION extend_subscription_for_testing(
    p_client_id UUID,
    p_hours INTEGER DEFAULT 4
)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    subscription_record RECORD;
BEGIN
    -- Update the subscription
    UPDATE client_subscriptions 
    SET 
        ends_at = NOW() + (p_hours || ' hours')::INTERVAL,
        status = 'active',
        updated_at = NOW()
    WHERE 
        client_id = p_client_id 
        AND status IN ('active', 'expired')
    RETURNING * INTO subscription_record;
    
    IF subscription_record IS NULL THEN
        result = jsonb_build_object(
            'success', false,
            'message', 'No subscription found for client'
        );
    ELSE
        result = jsonb_build_object(
            'success', true,
            'message', 'Subscription extended successfully',
            'client_id', subscription_record.client_id,
            'new_expiry', subscription_record.ends_at,
            'hours_extended', p_hours
        );
        
        -- Log the extension
        INSERT INTO system_logs (
            log_type,
            message,
            metadata,
            created_at
        ) VALUES (
            'testing_extension',
            'Subscription manually extended for testing',
            result,
            NOW()
        );
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 9. Quick commands for testing management

-- Check current subscription status:
-- SELECT * FROM subscription_status_view;

-- Manually deactivate expired subscriptions:
-- SELECT deactivate_expired_subscriptions();

-- Extend a specific client's subscription by 4 hours:
-- SELECT extend_subscription_for_testing('your-client-id-here', 4);

-- Extend all active subscriptions by 4 hours:
-- UPDATE client_subscriptions SET ends_at = NOW() + INTERVAL '4 hours' WHERE status = 'active';

-- View recent testing logs:
-- SELECT * FROM system_logs WHERE log_type IN ('testing_mode', 'subscription_expiry', 'testing_extension') ORDER BY created_at DESC LIMIT 10;

-- 10. To disable testing mode later (restore normal monthly subscriptions):
/*
-- Remove the testing trigger
DROP TRIGGER IF EXISTS testing_mode_subscription_trigger ON client_subscriptions;
DROP FUNCTION IF EXISTS set_testing_subscription_duration();

-- Update active subscriptions back to monthly
UPDATE client_subscriptions 
SET ends_at = starts_at + INTERVAL '1 month'
WHERE status = 'active';
*/

COMMENT ON FUNCTION set_testing_subscription_duration() IS 'Testing mode: Sets all new subscriptions to expire in 4 hours';
COMMENT ON FUNCTION deactivate_expired_subscriptions() IS 'Deactivates subscriptions that have expired';
COMMENT ON FUNCTION extend_subscription_for_testing(UUID, INTEGER) IS 'Manually extend a subscription for testing purposes';
COMMENT ON VIEW subscription_status_view IS 'Easy view of current subscription statuses with expiry information';

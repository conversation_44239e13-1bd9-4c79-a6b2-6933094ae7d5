-- SMS Logs PDF Storage Enhancement
-- This script adds comprehensive PDF storage support to SMS logs table
-- Date: 2025-07-28

-- Add PDF storage columns to existing sms_logs table
ALTER TABLE sms_logs 
ADD COLUMN IF NOT EXISTS pdf_attachments JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS pdf_storage_urls TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS total_pdf_attachments INTEGER DEFAULT 0;

-- Create storage bucket table for PDF files
CREATE TABLE IF NOT EXISTS sms_pdf_storage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sms_log_id UUID NOT NULL REFERENCES sms_logs(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL, -- in bytes
    file_type VARCHAR(50) DEFAULT 'application/pdf',
    storage_path TEXT NOT NULL, -- path in storage bucket
    storage_url TEXT NOT NULL, -- public URL for access
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sms_logs_client_timestamp ON sms_logs(client_id, sms_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_sms_pdf_storage_sms_log ON sms_pdf_storage(sms_log_id);
CREATE INDEX IF NOT EXISTS idx_sms_pdf_storage_client ON sms_pdf_storage(client_id, upload_timestamp DESC);

-- Update existing sms_logs to have proper structure
UPDATE sms_logs 
SET pdf_attachments = '[]'::jsonb,
    pdf_storage_urls = '{}',
    total_pdf_attachments = 0
WHERE pdf_attachments IS NULL;

-- Create view for easy SMS logs with PDF data
CREATE OR REPLACE VIEW sms_logs_with_pdfs AS
SELECT 
    sl.*,
    COALESCE(
        json_agg(
            json_build_object(
                'id', sps.id,
                'file_name', sps.file_name,
                'file_size', sps.file_size,
                'storage_url', sps.storage_url,
                'upload_timestamp', sps.upload_timestamp
            )
        ) FILTER (WHERE sps.id IS NOT NULL), 
        '[]'::json
    ) as pdf_files
FROM sms_logs sl
LEFT JOIN sms_pdf_storage sps ON sl.id = sps.sms_log_id AND sps.is_active = TRUE
GROUP BY sl.id, sl.client_id, sl.recipient_number, sl.message_content, sl.message_summary, 
         sl.file_attachments, sl.sms_timestamp, sl.status, sl.created_at, 
         sl.pdf_attachments, sl.pdf_storage_urls, sl.total_pdf_attachments;

-- Function to add PDF to SMS log
CREATE OR REPLACE FUNCTION add_pdf_to_sms_log(
    p_sms_log_id UUID,
    p_client_id UUID,
    p_file_name VARCHAR(255),
    p_file_size INTEGER,
    p_storage_path TEXT,
    p_storage_url TEXT
) RETURNS UUID AS $$
DECLARE
    pdf_id UUID;
BEGIN
    -- Insert PDF record
    INSERT INTO sms_pdf_storage (
        sms_log_id, client_id, file_name, file_size, 
        storage_path, storage_url
    ) VALUES (
        p_sms_log_id, p_client_id, p_file_name, p_file_size,
        p_storage_path, p_storage_url
    ) RETURNING id INTO pdf_id;
    
    -- Update SMS log counters
    UPDATE sms_logs 
    SET total_pdf_attachments = total_pdf_attachments + 1,
        pdf_storage_urls = array_append(pdf_storage_urls, p_storage_url),
        pdf_attachments = pdf_attachments || jsonb_build_object(
            'file_name', p_file_name,
            'file_size', p_file_size,
            'storage_url', p_storage_url,
            'upload_timestamp', NOW()
        )
    WHERE id = p_sms_log_id;
    
    RETURN pdf_id;
END;
$$ LANGUAGE plpgsql;

-- Sample data insertion function for testing
CREATE OR REPLACE FUNCTION create_sample_sms_with_pdf(
    p_client_id UUID,
    p_recipient_number VARCHAR(20),
    p_message_content TEXT,
    p_pdf_file_name VARCHAR(255) DEFAULT 'estimate.pdf'
) RETURNS UUID AS $$
DECLARE
    sms_id UUID;
    pdf_id UUID;
BEGIN
    -- Create SMS log
    INSERT INTO sms_logs (
        client_id, recipient_number, message_content, 
        message_summary, status
    ) VALUES (
        p_client_id, p_recipient_number, p_message_content,
        'Estimate sent via SMS', 'sent'
    ) RETURNING id INTO sms_id;
    
    -- Add PDF attachment
    SELECT add_pdf_to_sms_log(
        sms_id,
        p_client_id,
        p_pdf_file_name,
        245760, -- 240KB sample size
        'sms-pdfs/' || sms_id::text || '/' || p_pdf_file_name,
        'https://storage.example.com/sms-pdfs/' || sms_id::text || '/' || p_pdf_file_name
    ) INTO pdf_id;
    
    RETURN sms_id;
END;
$$ LANGUAGE plpgsql;

import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Package, Calendar, ArrowLeft, User, Phone, Mail } from 'lucide-react';

const OrderDetailsPage = () => {
  const { id } = useParams();
  
  // Mock order data - in real app, fetch based on ID
  const order = {
    id: id,
    orderNumber: 'ORD-001',
    title: 'Product Inquiry - Electronics',
    description: 'Detailed inquiry about laptop specifications, pricing, and availability. Customer interested in gaming laptops with specific requirements.',
    status: 'completed',
    date: '2024-01-15',
    completedDate: '2024-01-18',
    amount: 2500,
    products: [
      { name: 'Gaming Laptop', quantity: 1, price: 85000 },
      { name: 'Wireless Mouse', quantity: 1, price: 2500 },
      { name: 'Mechanical Keyboard', quantity: 1, price: 5000 }
    ],
    clientInfo: {
      name: 'TechStore Solutions',
      phone: '+91 9876543210',
      email: '<EMAIL>'
    },
    timeline: [
      { date: '2024-01-15', event: 'Order placed', description: 'Customer inquiry received' },
      { date: '2024-01-16', event: 'Processing', description: 'Product research and pricing' },
      { date: '2024-01-17', event: 'Quote prepared', description: 'Detailed quote sent to customer' },
      { date: '2024-01-18', event: 'Completed', description: 'Customer approved the quote' }
    ]
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return { bg: '#dcfce7', color: '#166534' };
      case 'pending':
        return { bg: '#fef3c7', color: '#92400e' };
      case 'processing':
        return { bg: '#dbeafe', color: '#1e40af' };
      default:
        return { bg: '#f3f4f6', color: '#374151' };
    }
  };

  const statusStyle = getStatusColor(order.status);

  return (
    <div className="order-details-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <header style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <Link 
              to="/orders" 
              style={{ 
                padding: '0.5rem', 
                backgroundColor: '#f3f4f6', 
                borderRadius: '4px',
                textDecoration: 'none',
                color: '#374151',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <ArrowLeft size={20} />
            </Link>
            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
              Order Details
            </h1>
          </div>
          <Link 
            to="/dashboard" 
            style={{ 
              padding: '0.5rem 1rem', 
              backgroundColor: '#3b82f6', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '4px'
            }}
          >
            Dashboard
          </Link>
        </div>
      </header>

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem 1rem' }}>
        <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '2rem' }}>
          {/* Main Content */}
          <div>
            {/* Order Header */}
            <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                <div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                    <Package size={24} style={{ color: '#3b82f6' }} />
                    <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                      {order.title}
                    </h2>
                  </div>
                  <p style={{ color: '#6b7280', fontSize: '1rem', marginBottom: '1rem' }}>{order.description}</p>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                    <span style={{ fontWeight: '500' }}>{order.orderNumber}</span>
                    <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <Calendar size={14} />
                      Placed on {new Date(order.date).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div style={{ 
                    padding: '0.5rem 1rem', 
                    borderRadius: '12px', 
                    fontSize: '0.875rem', 
                    fontWeight: '500',
                    backgroundColor: statusStyle.bg,
                    color: statusStyle.color,
                    marginBottom: '0.5rem'
                  }}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </div>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937' }}>
                    ₹{order.amount.toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Products/Services */}
            <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', marginBottom: '1rem' }}>Products & Services</h3>
              <div style={{ space: '1rem' }}>
                {order.products.map((product, index) => (
                  <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '1rem', border: '1px solid #e5e7eb', borderRadius: '4px', marginBottom: '0.5rem' }}>
                    <div>
                      <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: 0 }}>{product.name}</h4>
                      <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Quantity: {product.quantity}</p>
                    </div>
                    <div style={{ fontSize: '1rem', fontWeight: '600', color: '#1f2937' }}>
                      ₹{product.price.toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
              <div style={{ borderTop: '2px solid #e5e7eb', paddingTop: '1rem', marginTop: '1rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937' }}>Total Amount</span>
                  <span style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937' }}>
                    ₹{order.products.reduce((sum, p) => sum + p.price, 0).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem' }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', marginBottom: '1rem' }}>Order Timeline</h3>
              <div style={{ position: 'relative' }}>
                {order.timeline.map((item, index) => (
                  <div key={index} style={{ display: 'flex', gap: '1rem', marginBottom: index < order.timeline.length - 1 ? '1.5rem' : '0' }}>
                    <div style={{ position: 'relative' }}>
                      <div style={{ 
                        width: '12px', 
                        height: '12px', 
                        backgroundColor: '#3b82f6', 
                        borderRadius: '50%',
                        marginTop: '0.25rem'
                      }}></div>
                      {index < order.timeline.length - 1 && (
                        <div style={{ 
                          position: 'absolute', 
                          left: '5px', 
                          top: '12px', 
                          width: '2px', 
                          height: '1.5rem', 
                          backgroundColor: '#e5e7eb' 
                        }}></div>
                      )}
                    </div>
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.25rem' }}>
                        <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: 0 }}>{item.event}</h4>
                        <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>{new Date(item.date).toLocaleDateString()}</span>
                      </div>
                      <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div>
            {/* Client Information */}
            <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1.5rem', marginBottom: '2rem' }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937', marginBottom: '1rem' }}>Contact Information</h3>
              <div style={{ space: '1rem' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <User size={18} style={{ color: '#6b7280' }} />
                  <div>
                    <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#1f2937', margin: 0 }}>{order.clientInfo.name}</p>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <Phone size={18} style={{ color: '#6b7280' }} />
                  <div>
                    <p style={{ fontSize: '0.875rem', color: '#1f2937', margin: 0 }}>{order.clientInfo.phone}</p>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <Mail size={18} style={{ color: '#6b7280' }} />
                  <div>
                    <p style={{ fontSize: '0.875rem', color: '#1f2937', margin: 0 }}>{order.clientInfo.email}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1.5rem' }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937', marginBottom: '1rem' }}>Quick Actions</h3>
              <div style={{ space: '0.5rem' }}>
                <Link 
                  to="/chats" 
                  style={{ 
                    display: 'block',
                    width: '100%',
                    padding: '0.75rem', 
                    backgroundColor: '#3b82f6', 
                    color: 'white', 
                    textDecoration: 'none', 
                    borderRadius: '4px',
                    textAlign: 'center',
                    marginBottom: '0.5rem'
                  }}
                >
                  Start a Chat
                </Link>
                <Link 
                  to="/orders" 
                  style={{ 
                    display: 'block',
                    width: '100%',
                    padding: '0.75rem', 
                    backgroundColor: 'white', 
                    color: '#3b82f6', 
                    textDecoration: 'none', 
                    borderRadius: '4px',
                    textAlign: 'center',
                    border: '1px solid #3b82f6'
                  }}
                >
                  View All Orders
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailsPage;

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  User, 
  ShoppingCart, 
  MessageSquare, 
  Phone, 
  Calendar,
  CheckCircle,
  Clock,
  Package
} from 'lucide-react';

const DashboardPage = () => {
  const [customerData, setCustomerData] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+91 9876543210'
  });

  const [stats, setStats] = useState({
    totalOrders: 0,
    activeChats: 0,
    totalCalls: 0,
    pendingOrders: 0
  });

  const [recentOrders, setRecentOrders] = useState([]);
  const [recentChats, setRecentChats] = useState([]);

  useEffect(() => {
    // TODO: Fetch customer data and stats from API
    // For now, using mock data
    setStats({
      totalOrders: 5,
      activeChats: 2,
      totalCalls: 12,
      pendingOrders: 1
    });

    setRecentOrders([
      {
        id: '1',
        orderNumber: 'ORD-001',
        title: 'Product Inquiry',
        status: 'completed',
        date: '2024-01-15',
        amount: 2500
      },
      {
        id: '2',
        orderNumber: 'ORD-002',
        title: 'Service Request',
        status: 'pending',
        date: '2024-01-20',
        amount: 1800
      }
    ]);

    setRecentChats([
      {
        id: '1',
        title: 'Product Support',
        lastMessage: 'Thank you for your help!',
        timestamp: '2 hours ago',
        unreadCount: 0
      },
      {
        id: '2',
        title: 'Order Inquiry',
        lastMessage: 'When will my order be delivered?',
        timestamp: '1 day ago',
        unreadCount: 1
      }
    ]);
  }, []);

  return (
    <div className="dashboard-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <header style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
            Customer Dashboard
          </h1>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <span style={{ color: '#6b7280' }}>Welcome, {customerData.name}</span>
            <Link 
              to="/profile" 
              style={{ 
                padding: '0.5rem 1rem', 
                backgroundColor: '#3b82f6', 
                color: 'white', 
                textDecoration: 'none', 
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <User size={16} />
              Profile
            </Link>
          </div>
        </div>
      </header>

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem 1rem' }}>
        {/* Welcome Section */}
        <div style={{ marginBottom: '2rem' }}>
          <h2 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '0.5rem' }}>
            Welcome back, {customerData.name}!
          </h2>
          <p style={{ color: '#6b7280', fontSize: '1.125rem' }}>
            Here's what's happening with your account today.
          </p>
        </div>

        {/* Stats Cards */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Total Orders</p>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', margin: '0.5rem 0 0 0' }}>{stats.totalOrders}</p>
              </div>
              <ShoppingCart size={32} style={{ color: '#3b82f6' }} />
            </div>
          </div>

          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Active Chats</p>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', margin: '0.5rem 0 0 0' }}>{stats.activeChats}</p>
              </div>
              <MessageSquare size={32} style={{ color: '#10b981' }} />
            </div>
          </div>

          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Total Calls</p>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', margin: '0.5rem 0 0 0' }}>{stats.totalCalls}</p>
              </div>
              <Phone size={32} style={{ color: '#f59e0b' }} />
            </div>
          </div>

          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Pending Orders</p>
                <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', margin: '0.5rem 0 0 0' }}>{stats.pendingOrders}</p>
              </div>
              <Clock size={32} style={{ color: '#ef4444' }} />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginBottom: '2rem' }}>
          <Link 
            to="/orders" 
            style={{ 
              backgroundColor: 'white', 
              borderRadius: '8px', 
              padding: '1.5rem', 
              textDecoration: 'none', 
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              transition: 'transform 0.2s'
            }}
            onMouseOver={(e) => e.target.style.transform = 'translateY(-2px)'}
            onMouseOut={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <Package size={24} style={{ color: '#3b82f6' }} />
            <div>
              <h3 style={{ color: '#1f2937', fontSize: '1rem', fontWeight: '600', margin: 0 }}>View Orders</h3>
              <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Track your orders</p>
            </div>
          </Link>

          <Link 
            to="/chats" 
            style={{ 
              backgroundColor: 'white', 
              borderRadius: '8px', 
              padding: '1.5rem', 
              textDecoration: 'none', 
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              transition: 'transform 0.2s'
            }}
            onMouseOver={(e) => e.target.style.transform = 'translateY(-2px)'}
            onMouseOut={(e) => e.target.style.transform = 'translateY(0)'}
          >
            <MessageSquare size={24} style={{ color: '#10b981' }} />
            <div>
              <h3 style={{ color: '#1f2937', fontSize: '1rem', fontWeight: '600', margin: 0 }}>Chat Support</h3>
              <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>Get instant help</p>
            </div>
          </Link>
        </div>

        {/* Recent Activity */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '2rem' }}>
          {/* Recent Orders */}
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', marginBottom: '1rem' }}>Recent Orders</h3>
            {recentOrders.length > 0 ? (
              <div style={{ space: '1rem' }}>
                {recentOrders.map((order) => (
                  <div key={order.id} style={{ padding: '1rem', border: '1px solid #e5e7eb', borderRadius: '4px', marginBottom: '0.5rem' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                      <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: 0 }}>{order.title}</h4>
                      <span style={{ 
                        padding: '0.25rem 0.5rem', 
                        borderRadius: '12px', 
                        fontSize: '0.75rem', 
                        fontWeight: '500',
                        backgroundColor: order.status === 'completed' ? '#dcfce7' : '#fef3c7',
                        color: order.status === 'completed' ? '#166534' : '#92400e'
                      }}>
                        {order.status}
                      </span>
                    </div>
                    <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                      {order.orderNumber} • {order.date} • ₹{order.amount}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p style={{ color: '#6b7280', textAlign: 'center', padding: '2rem' }}>No orders yet</p>
            )}
            <Link 
              to="/orders" 
              style={{ color: '#3b82f6', textDecoration: 'none', fontSize: '0.875rem', fontWeight: '500' }}
            >
              View all orders →
            </Link>
          </div>

          {/* Recent Chats */}
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', marginBottom: '1rem' }}>Recent Chats</h3>
            {recentChats.length > 0 ? (
              <div style={{ space: '1rem' }}>
                {recentChats.map((chat) => (
                  <div key={chat.id} style={{ padding: '1rem', border: '1px solid #e5e7eb', borderRadius: '4px', marginBottom: '0.5rem' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                      <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: 0 }}>{chat.title}</h4>
                      {chat.unreadCount > 0 && (
                        <span style={{ 
                          backgroundColor: '#ef4444', 
                          color: 'white', 
                          borderRadius: '50%', 
                          width: '20px', 
                          height: '20px', 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center', 
                          fontSize: '0.75rem' 
                        }}>
                          {chat.unreadCount}
                        </span>
                      )}
                    </div>
                    <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                      {chat.lastMessage}
                    </p>
                    <p style={{ color: '#9ca3af', fontSize: '0.75rem', margin: '0.5rem 0 0 0' }}>
                      {chat.timestamp}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p style={{ color: '#6b7280', textAlign: 'center', padding: '2rem' }}>No chats yet</p>
            )}
            <Link 
              to="/chats" 
              style={{ color: '#3b82f6', textDecoration: 'none', fontSize: '0.875rem', fontWeight: '500' }}
            >
              View all chats →
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;

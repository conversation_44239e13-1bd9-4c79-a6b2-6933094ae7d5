-- Migration: Add support ticket messages table for chat functionality
-- Date: 2025-07-25

-- Create support_ticket_messages table for chat functionality
CREATE TABLE IF NOT EXISTS support_ticket_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL, -- 'client' or 'admin'
    sender_id UUID NOT NULL, -- client_id or admin_id
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text', -- text, image, file
    attachment_url TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_ticket_id ON support_ticket_messages(ticket_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_sender ON support_ticket_messages(sender_type, sender_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_created_at ON support_ticket_messages(created_at);

-- Add admin_id column to support_tickets if it doesn't exist (for proper admin assignment)
ALTER TABLE support_tickets 
ADD COLUMN IF NOT EXISTS admin_id UUID REFERENCES admins(id) ON DELETE SET NULL;

-- Create index for admin_id if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_support_tickets_admin_id ON support_tickets(admin_id);

-- Update existing tickets to assign them to admins based on client assignments
UPDATE support_tickets 
SET admin_id = (
    SELECT aca.admin_id 
    FROM admin_client_assignments aca 
    WHERE aca.client_id = support_tickets.client_id 
    LIMIT 1
)
WHERE admin_id IS NULL;

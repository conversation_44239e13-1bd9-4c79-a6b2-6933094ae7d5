const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import admin routes
const adminAuthRoutes = require('./routes/adminAuth');
const adminPanelRoutes = require('./routes/adminPanel');
const adminSupportRoutes = require('./routes/adminSupport');
const superAdminRoutes = require('./routes/superAdmin');

// Test database connection
const { dbHelpers } = require('../backend/config/supabase');
console.log('🔍 Testing database connection...');
dbHelpers.query('SELECT NOW() as current_time')
  .then(result => {
    console.log('✅ Database connected successfully:', result[0].current_time);
  })
  .catch(error => {
    console.error('❌ Database connection failed:', error.message);
  });

// Import middleware
const { errorHandler } = require('../backend/middleware/errorHandler');

const app = express();
const PORT = process.env.ADMIN_PORT || 4000;

// Security middleware
app.use(helmet());
app.use(compression());

// Rate limiting for admin panel
const adminLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests',
    message: 'Please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use(adminLimiter);

// CORS configuration for admin panel
app.use(cors({
  origin: [
    process.env.ADMIN_FRONTEND_URL || 'http://localhost:3001',
    'http://localhost:3000', // Allow admin frontend on port 3000
    'http://localhost:3001'  // Allow admin frontend on port 3001
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging
if (process.env.NODE_ENV !== 'production') {
  app.use(morgan('dev'));
}

// Root endpoint - Show available routes
app.get('/', (req, res) => {
  res.json({
    service: 'Telecalling Admin Panel API',
    version: '1.0.0',
    status: 'Running',
    port: PORT,
    timestamp: new Date().toISOString(),
    availableRoutes: {
      authentication: {
        login: 'POST /api/admin/auth/login',
        logout: 'POST /api/admin/auth/logout',
        refresh: 'POST /api/admin/auth/refresh',
        profile: 'GET /api/admin/auth/profile'
      },
      clientManagement: {
        dashboard: 'GET /api/admin/panel/dashboard',
        clients: 'GET /api/admin/panel/clients',
        clientDetails: 'GET /api/admin/panel/clients/:id',
        assignNumber: 'PUT /api/admin/panel/clients/:id/assign-number',
        removeNumber: 'DELETE /api/admin/panel/clients/:id/remove-number'
      },
      supportManagement: {
        tickets: 'GET /api/admin/support/tickets',
        ticketDetails: 'GET /api/admin/support/tickets/:id',
        respondToTicket: 'PUT /api/admin/support/tickets/:id/respond',
        supportStats: 'GET /api/admin/support/stats'
      },
      superAdminManagement: {
        dashboard: 'GET /api/admin/super/dashboard',
        admins: 'GET /api/admin/super/admins',
        createAdmin: 'POST /api/admin/super/admins',
        updateAdminStatus: 'PUT /api/admin/super/admins/:id/status',
        allClients: 'GET /api/admin/super/clients',
        unassignedClients: 'GET /api/admin/super/clients/unassigned',
        assignAdminToClients: 'POST /api/admin/super/clients/assign-admin',
        assignAdminToClient: 'POST /api/admin/super/clients/:id/assign-admin',
        removeClientAssignment: 'DELETE /api/admin/super/clients/:id/remove-admin',
        adminClients: 'GET /api/admin/super/admins/:id/clients',
        auditLog: 'GET /api/admin/super/audit-log'
      },
      utilities: {
        health: 'GET /health'
      }
    },
    defaultCredentials: {
      admin: {
        adminId: 'admin001',
        password: 'admin123',
        note: 'Regular admin credentials'
      },
      superAdmin: {
        adminId: 'superadmin001',
        password: 'superadmin123',
        note: 'Super admin credentials - can manage admins and client assignments'
      }
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Admin Panel API',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Admin API routes
app.use('/api/admin/auth', adminAuthRoutes);
app.use('/api/admin/panel', adminPanelRoutes); // Panel routes have their own auth middleware
app.use('/api/admin/support', adminSupportRoutes); // Support routes will use same auth
app.use('/api/admin/super', superAdminRoutes); // Super admin routes have their own auth middleware

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

// Global error handler
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Admin Panel Server running on port ${PORT}`);
  console.log(`📊 Admin Panel URL: http://localhost:${PORT}`);
  console.log(`🔒 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

module.exports = app;

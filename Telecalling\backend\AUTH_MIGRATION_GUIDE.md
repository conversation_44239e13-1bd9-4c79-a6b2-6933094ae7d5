# Authentication System Migration Guide

## Overview

Your authentication system has been upgraded to support both custom JWT authentication and Supabase Auth. This hybrid approach ensures backward compatibility while providing the benefits of Supabase Auth (including `auth.id` for database operations).

## What's Changed

### 1. Fixed JWT Issues
- ✅ Fixed `JWT_REFRESH_SECRET` error handling
- ✅ Added proper environment variable validation
- ✅ Enhanced error messages for debugging

### 2. Fixed Signup Flow
- ✅ Users are no longer saved to `clients` table before OTP verification
- ✅ New `pending_registrations` table for unverified signups
- ✅ Prevents duplicate email issues during multiple signup attempts
- ✅ Only creates actual user account after OTP verification

### 3. Hybrid Authentication System
- ✅ Supports both custom JWT and Supabase Auth
- ✅ Seamless migration path for existing users
- ✅ New users get both authentication methods
- ✅ Middleware automatically detects and handles both token types

### 4. Database Schema Updates
- ✅ Added `supabase_auth_id` column to `clients` table
- ✅ Added `pending_registrations` table
- ✅ Updated RLS policies for both auth methods
- ✅ Created migration utilities and status checking functions

## Database Migration

### Step 1: Run the Migration Script

Execute these SQL scripts in your Supabase SQL editor:

1. First, run the updated `auth_schema.sql`
2. Then, run the `migrate_to_supabase_auth.sql`

```sql
-- Check migration status
SELECT * FROM check_auth_compatibility();
```

### Step 2: Verify Environment Variables

Ensure your `.env` file has all required variables:

```env
# Database
DATABASE_URL=your_database_url

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# JWT (Required for custom auth)
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret

# Email
RESEND_API_KEY=your_resend_api_key
```

## How the New System Works

### Signup Flow

1. **User submits signup** → Saved to `pending_registrations` (not `clients`)
2. **OTP sent via email** → Using Supabase Edge Function
3. **User verifies OTP** → Creates both Supabase Auth user AND `clients` record
4. **Account activated** → User can now login with either method

### Login Flow

The middleware (`middleware/auth.js`) automatically handles both authentication methods:

1. **Token received** → Checks if it's a Supabase Auth token first
2. **If Supabase fails** → Falls back to JWT verification
3. **User found** → Attaches user data to `req.user` with `authId` field

### User Object Structure

```javascript
req.user = {
  id: "internal_uuid",           // Internal client ID
  authId: "supabase_auth_id",    // Use this for auth.id references
  email: "<EMAIL>",
  businessName: "Business Name",
  username: "username",
  isEmailVerified: true,
  isActive: true,
  _authMethod: "supabase" | "jwt" // Which auth method was used
}
```

## Using auth.id in Your Application

For new tables and RLS policies, use `req.user.authId`:

```sql
-- Example RLS policy
CREATE POLICY "Users can view their own data" ON your_table
  FOR SELECT USING (auth.uid()::text = user_id::text);

-- In your application
const userId = req.user.authId; // This will be the Supabase auth.id
```

## Migration Strategies

### Strategy 1: Gradual Migration (Recommended)
- Keep existing users on JWT
- New users get both auth methods
- Gradually migrate existing users using the provided functions

### Strategy 2: Force Migration
- Create Supabase Auth accounts for all existing users
- Use the `migrate_user_to_supabase_auth()` function

### Strategy 3: Parallel Systems
- Continue using both systems indefinitely
- JWT for existing features
- Supabase Auth for new features requiring `auth.id`

## Utility Functions

### Check Authentication Status
```sql
SELECT * FROM check_auth_compatibility();
```

### Migrate Specific User
```sql
SELECT migrate_user_to_supabase_auth('<EMAIL>', 'supabase_user_id');
```

### View User Auth Status
```sql
SELECT * FROM user_auth_status WHERE email = '<EMAIL>';
```

## API Endpoints

All existing API endpoints continue to work without changes:

- `POST /api/auth/signup` - Now uses pending registration flow
- `POST /api/auth/login` - Works with both auth methods
- `POST /api/auth/verify-email` - Creates both auth records
- `POST /api/auth/forgot-password` - Unchanged
- `POST /api/auth/reset-password` - Unchanged
- `POST /api/auth/refresh-token` - Enhanced error handling

## Frontend Changes Required

No immediate frontend changes are required, but you may want to:

1. Update error handling for better user experience
2. Add migration prompts for existing users
3. Use the new `authId` field for auth-dependent operations

## Testing the Migration

### Test Signup Flow
1. Create new account
2. Verify OTP
3. Check that both `clients` record and Supabase Auth user exist
4. Verify login works

### Test Existing Users
1. Ensure existing users can still login
2. Check JWT token refresh works
3. Verify protected routes still work

### Test Migration
1. Run migration script
2. Check auth compatibility status
3. Test mixed auth scenarios

## Troubleshooting

### Common Issues

**1. JWT_REFRESH_SECRET Error**
- Ensure environment variable is set
- Check for typos in variable name
- Restart server after adding variable

**2. Supabase Auth Not Working**
- Verify Supabase credentials
- Check RLS policies are correctly set
- Ensure auth schema is properly configured

**3. OTP Not Sending**
- Check Resend API key
- Verify Supabase Edge Function is deployed
- Check email function logs

**4. Database Connection Issues**
- Verify DATABASE_URL is correct
- Check database permissions
- Ensure migration scripts ran successfully

### Debug Commands

```javascript
// Check environment variables
console.log('JWT_SECRET:', !!process.env.JWT_SECRET);
console.log('JWT_REFRESH_SECRET:', !!process.env.JWT_REFRESH_SECRET);
console.log('SUPABASE_URL:', !!process.env.SUPABASE_URL);

// Check auth method in middleware
console.log('Auth method:', req.user._authMethod);
console.log('Auth ID:', req.user.authId);
```

## Benefits of the New System

1. **Backward Compatibility** - Existing users continue working
2. **Supabase Auth Integration** - New users get `auth.id` support
3. **Improved Security** - Better token handling and validation
4. **Scalability** - Can handle both auth methods simultaneously
5. **Better UX** - Fixed signup flow prevents duplicate email issues
6. **Future-Proof** - Easy migration path to full Supabase Auth

## Next Steps

1. **Deploy Migration** - Run the migration scripts
2. **Test Thoroughly** - Verify all flows work correctly
3. **Monitor Logs** - Watch for any authentication errors
4. **Plan Full Migration** - Gradually move all users to Supabase Auth
5. **Update Documentation** - Inform your team about the changes

For any issues, check the server logs and database migration status. The system is designed to be fault-tolerant and will fall back to working methods if one fails. 
const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireClient } = require('../middleware/auth');

const router = express.Router();

// Get all appointment slots for a client
router.get('/', requireClient, asyncHandler(async (req, res) => {
  const { date, status } = req.query;

  // Get client ID (req.user.id is already the client ID)
  const clientId = req.user.id;

  // Verify client exists
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('id', clientId)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  let query = supabaseAdmin
    .from('appointment_slots')
    .select('*')
    .eq('client_id', clientId)
    .order('slot_time', { ascending: true });

  if (date) {
    const startDate = new Date(date);
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);
    
    query = query.gte('slot_time', startDate.toISOString())
             .lt('slot_time', endDate.toISOString());
  }

  if (status === 'booked') {
    query = query.eq('is_booked', true);
  } else if (status === 'available') {
    query = query.eq('is_booked', false);
  }

  const { data: appointments, error } = await query;

  if (error) {
    throw error;
  }

  res.json({
    appointments: appointments || []
  });
}));

// Create new appointment slot
router.post('/slots', requireClient, asyncHandler(async (req, res) => {
  const { slotTime, duration = 30 } = req.body;

  if (!slotTime) {
    throw new AppError('Slot time is required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Check if slot already exists
  const { data: existingSlot } = await supabaseAdmin
    .from('appointment_slots')
    .select('id')
    .eq('client_id', client.id)
    .eq('slot_time', new Date(slotTime).toISOString())
    .single();

  if (existingSlot) {
    throw new AppError('Appointment slot already exists for this time', 409);
  }

  const { data: slot, error } = await supabaseAdmin
    .from('appointment_slots')
    .insert({
      id: uuidv4(),
      client_id: client.id,
      slot_time: new Date(slotTime).toISOString(),
      duration,
      is_booked: false,
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.status(201).json({
    message: 'Appointment slot created successfully',
    slot
  });
}));

// Book an appointment slot
router.post('/:slotId/book', asyncHandler(async (req, res) => {
  const { slotId } = req.params;
  const { callerNumber, callerName, notes } = req.body;

  if (!callerNumber) {
    throw new AppError('Caller number is required', 400);
  }

  // Get slot details
  const { data: slot, error: slotError } = await supabaseAdmin
    .from('appointment_slots')
    .select('*')
    .eq('id', slotId)
    .single();

  if (slotError || !slot) {
    throw new AppError('Appointment slot not found', 404);
  }

  if (slot.is_booked) {
    throw new AppError('Appointment slot is already booked', 409);
  }

  // Check if slot is in the future
  if (new Date(slot.slot_time) < new Date()) {
    throw new AppError('Cannot book past appointment slots', 400);
  }

  // Book the slot
  const { data: bookedSlot, error } = await supabaseAdmin
    .from('appointment_slots')
    .update({
      is_booked: true,
      booked_by: callerNumber,
      booked_by_name: callerName || null,
      booking_notes: notes || null,
      booked_at: new Date().toISOString()
    })
    .eq('id', slotId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.json({
    message: 'Appointment booked successfully',
    appointment: bookedSlot
  });
}));

// Cancel appointment
router.put('/:slotId/cancel', requireClient, asyncHandler(async (req, res) => {
  const { slotId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: cancelledSlot, error } = await supabaseAdmin
    .from('appointment_slots')
    .update({
      is_booked: false,
      booked_by: null,
      booked_by_name: null,
      booking_notes: null,
      booked_at: null,
      cancelled_at: new Date().toISOString()
    })
    .eq('id', slotId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!cancelledSlot) {
    throw new AppError('Appointment not found', 404);
  }

  res.json({
    message: 'Appointment cancelled successfully',
    appointment: cancelledSlot
  });
}));

// Delete appointment slot
router.delete('/:slotId', requireClient, asyncHandler(async (req, res) => {
  const { slotId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: deletedSlot, error } = await supabaseAdmin
    .from('appointment_slots')
    .delete()
    .eq('id', slotId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!deletedSlot) {
    throw new AppError('Appointment slot not found', 404);
  }

  res.json({
    message: 'Appointment slot deleted successfully'
  });
}));

// Get available slots for booking (public endpoint for voice bot)
router.get('/available/:clientId', asyncHandler(async (req, res) => {
  const { clientId } = req.params;
  const { date, limit = 10 } = req.query;

  let query = supabaseAdmin
    .from('appointment_slots')
    .select('*')
    .eq('client_id', clientId)
    .eq('is_booked', false)
    .gte('slot_time', new Date().toISOString())
    .order('slot_time', { ascending: true })
    .limit(parseInt(limit));

  if (date) {
    const startDate = new Date(date);
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);
    
    query = query.gte('slot_time', startDate.toISOString())
             .lt('slot_time', endDate.toISOString());
  }

  const { data: availableSlots, error } = await query;

  if (error) {
    throw error;
  }

  res.json({
    availableSlots: availableSlots || []
  });
}));

// Bulk create appointment slots
router.post('/bulk-create', requireClient, asyncHandler(async (req, res) => {
  const { startDate, endDate, timeSlots, excludeDates = [] } = req.body;

  if (!startDate || !endDate || !Array.isArray(timeSlots) || timeSlots.length === 0) {
    throw new AppError('Start date, end date, and time slots are required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const slotsToCreate = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Generate slots for each day
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    const dateStr = date.toISOString().split('T')[0];
    
    // Skip excluded dates
    if (excludeDates.includes(dateStr)) {
      continue;
    }

    // Create slots for each time slot
    timeSlots.forEach(timeSlot => {
      const [hours, minutes] = timeSlot.split(':');
      const slotDateTime = new Date(date);
      slotDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      // Only create future slots
      if (slotDateTime > new Date()) {
        slotsToCreate.push({
          id: uuidv4(),
          client_id: client.id,
          slot_time: slotDateTime.toISOString(),
          duration: 30,
          is_booked: false,
          created_at: new Date().toISOString()
        });
      }
    });
  }

  if (slotsToCreate.length === 0) {
    throw new AppError('No valid slots to create', 400);
  }

  const { data: createdSlots, error } = await supabaseAdmin
    .from('appointment_slots')
    .insert(slotsToCreate)
    .select();

  if (error) {
    throw error;
  }

  res.status(201).json({
    message: `${createdSlots.length} appointment slots created successfully`,
    slots: createdSlots
  });
}));

module.exports = router; 
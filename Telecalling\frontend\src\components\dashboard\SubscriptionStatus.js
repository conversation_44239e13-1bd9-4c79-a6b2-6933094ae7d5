import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Calendar, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Settings,
  RefreshCw
} from 'lucide-react';
import subscriptionAPI from '../../services/subscriptionAPI';
import PaymentModal from '../modals/PaymentModal';
import PaymentSuccessModal from '../modals/PaymentSuccessModal';

const SubscriptionStatus = () => {
  const [subscription, setSubscription] = useState(null);
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentResult, setPaymentResult] = useState(null);
  const [showCancelButton, setShowCancelButton] = useState(false);

  useEffect(() => {
    fetchSubscriptionData();
    // Show cancel button only in development
    setShowCancelButton(process.env.NODE_ENV === 'development');
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      const [subscriptionResponse, plansResponse] = await Promise.all([
        subscriptionAPI.getCurrentSubscription(),
        subscriptionAPI.getPlans()
      ]);

      if (subscriptionResponse.success) {
        setSubscription(subscriptionResponse.subscription);
      }

      if (plansResponse.success) {
        setPlans(plansResponse.plans);
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = (plan) => {
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = (result) => {
    setPaymentResult(result);
    setShowPaymentModal(false);
    setShowSuccessModal(true);
    // Refresh subscription data
    fetchSubscriptionData();
  };

  const handleCancelSubscription = async () => {
    if (!subscription?.id) return;

    if (window.confirm('Are you sure you want to cancel your subscription? This action is only available in development mode.')) {
      try {
        const result = await subscriptionAPI.cancelSubscription(subscription.id);
        if (result.success) {
          alert('Subscription cancelled successfully');
          fetchSubscriptionData();
        }
      } catch (error) {
        alert('Failed to cancel subscription: ' + error.message);
      }
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  const currentPlan = subscription?.subscription_plans;
  const isFreePlan = !subscription || currentPlan?.name === 'free';
  const isExpiringSoon = subscription?.ends_at && 
    new Date(subscription.ends_at) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

  return (
    <>
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <CreditCard className="w-5 h-5 mr-2" />
              Subscription Status
            </h3>
            <button
              onClick={fetchSubscriptionData}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              title="Refresh"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>

          {/* Current Plan */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h4 className="text-xl font-bold text-gray-900">
                  {currentPlan?.display_name || 'Free Plan'}
                </h4>
                <p className="text-gray-600">
                  {currentPlan?.description || 'Basic features for testing'}
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">
                  {currentPlan?.price ? `₹${currentPlan.price.toLocaleString()}` : 'Free'}
                </div>
                {currentPlan?.price && (
                  <div className="text-sm text-gray-500">/month</div>
                )}
              </div>
            </div>

            {/* Status Badge */}
            <div className="flex items-center space-x-2 mb-3">
              {subscription?.status === 'active' ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Active
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  Free
                </span>
              )}

              {isExpiringSoon && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  Expires Soon
                </span>
              )}
            </div>

            {/* Expiry Date */}
            {subscription?.ends_at && (
              <div className="flex items-center text-sm text-gray-600 mb-4">
                <Calendar className="w-4 h-4 mr-1" />
                Expires on {new Date(subscription.ends_at).toLocaleDateString()}
              </div>
            )}
          </div>

          {/* Plan Features */}
          {currentPlan?.features && (
            <div className="mb-6">
              <h5 className="font-medium text-gray-900 mb-2">Current Plan Features:</h5>
              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                <div>Max Calls: {currentPlan.max_calls === -1 ? 'Unlimited' : currentPlan.max_calls}</div>
                <div>Phone Numbers: {currentPlan.max_phone_numbers === -1 ? 'Unlimited' : currentPlan.max_phone_numbers}</div>
                <div>Products: {currentPlan.max_products === -1 ? 'Unlimited' : currentPlan.max_products}</div>
                <div>Support: {currentPlan.features.support || 'Email'}</div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3">
            {isFreePlan && (
              <>
                <button
                  onClick={() => {
                    const proPlan = plans.find(p => p.name === 'pro');
                    if (proPlan) handleUpgrade(proPlan);
                  }}
                  className="flex-1 btn btn-primary flex items-center justify-center"
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Upgrade to Pro
                </button>
                <a
                  href="/dashboard/payments"
                  className="btn btn-outline flex items-center justify-center"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  View All Plans
                </a>
              </>
            )}

            {!isFreePlan && (
              <>
                <button
                  onClick={() => {
                    const nextPlan = plans.find(p => p.sort_order > (currentPlan?.sort_order || 0));
                    if (nextPlan) handleUpgrade(nextPlan);
                  }}
                  className="flex-1 btn btn-outline flex items-center justify-center"
                  disabled={!plans.find(p => p.sort_order > (currentPlan?.sort_order || 0))}
                >
                  <TrendingUp className="w-4 h-4 mr-2" />
                  {plans.find(p => p.sort_order > (currentPlan?.sort_order || 0)) ? 'Upgrade Plan' : 'Current Plan'}
                </button>
                <a
                  href="/dashboard/payments"
                  className="btn btn-outline flex items-center justify-center"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Manage Billing
                </a>
              </>
            )}

            {showCancelButton && subscription?.id && (
              <button
                onClick={handleCancelSubscription}
                className="btn btn-outline text-red-600 border-red-300 hover:bg-red-50"
              >
                Cancel (Dev)
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        selectedPlan={selectedPlan}
        onSuccess={handlePaymentSuccess}
      />

      {/* Success Modal */}
      <PaymentSuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        paymentData={paymentResult}
      />
    </>
  );
};

export default SubscriptionStatus;

-- VoiceBot Platform Database Schema
-- Run this in your Supabase SQL editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('admin', 'client');
CREATE TYPE client_status AS ENUM ('pending', 'active', 'inactive', 'suspended');
CREATE TYPE call_status AS ENUM ('completed', 'missed', 'failed');
CREATE TYPE message_status AS ENUM ('sent', 'delivered', 'read', 'failed');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'expired', 'cancelled');
CREATE TYPE invoice_status AS ENUM ('draft', 'sent', 'paid', 'overdue', 'cancelled');
CREATE TYPE ticket_status AS ENUM ('open', 'in_progress', 'closed');
CREATE TYPE ticket_priority AS ENUM ('low', 'medium', 'high', 'urgent');

-- 1. Profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    full_name TEXT NOT NULL,
    role user_role NOT NULL DEFAULT 'client',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Clients table (Business information)
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    shop_name TEXT NOT NULL,
    business_type TEXT NOT NULL,
    business_summary TEXT DEFAULT '',
    whatsapp_number TEXT NOT NULL,
    region TEXT DEFAULT '',
    plivo_number TEXT UNIQUE,
    status client_status DEFAULT 'pending',
    config_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Products table (JSONB model for flexibility)
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    product_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Bot features table (Feature toggles)
CREATE TABLE bot_features (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    appointments_enabled BOOLEAN DEFAULT FALSE,
    whatsapp_enabled BOOLEAN DEFAULT TRUE,
    payment_enabled BOOLEAN DEFAULT FALSE,
    estimate_enabled BOOLEAN DEFAULT FALSE,
    invoice_enabled BOOLEAN DEFAULT FALSE,
    multi_language BOOLEAN DEFAULT FALSE,
    call_summary_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Appointment slots table
CREATE TABLE appointment_slots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    slot_time TIMESTAMP WITH TIME ZONE NOT NULL,
    duration INTEGER DEFAULT 30, -- in minutes
    is_booked BOOLEAN DEFAULT FALSE,
    booked_by TEXT,
    booked_by_name TEXT,
    booking_notes TEXT,
    booked_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Call logs table
CREATE TABLE call_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    caller_number TEXT NOT NULL,
    call_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    duration INTEGER DEFAULT 0, -- in seconds
    summary TEXT DEFAULT '',
    transcript_url TEXT,
    was_estimate_sent BOOLEAN DEFAULT FALSE,
    was_payment_sent BOOLEAN DEFAULT FALSE,
    call_status call_status DEFAULT 'completed',
    plivo_call_uuid TEXT
);

-- 7. WhatsApp logs table
CREATE TABLE whatsapp_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    to_number TEXT NOT NULL,
    message_type TEXT NOT NULL, -- estimate, reminder, invoice, etc.
    content TEXT NOT NULL,
    status message_status DEFAULT 'sent',
    whatsapp_message_id TEXT,
    error_message TEXT,
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Invoices table
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    invoice_number TEXT NOT NULL,
    to_number TEXT NOT NULL,
    customer_name TEXT,
    customer_email TEXT,
    items JSONB NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0,
    tax DECIMAL(10,2) DEFAULT 0,
    discount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    notes TEXT,
    due_date TIMESTAMP WITH TIME ZONE,
    status invoice_status DEFAULT 'draft',
    sent_at TIMESTAMP WITH TIME ZONE,
    sent_via TEXT,
    paid_at TIMESTAMP WITH TIME ZONE,
    payment_method TEXT,
    transaction_id TEXT,
    paid_amount DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Payments table
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    to_number TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    customer_name TEXT,
    customer_email TEXT,
    payment_link TEXT NOT NULL,
    payment_gateway_id TEXT,
    status payment_status DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE,
    paid_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    transaction_id TEXT,
    paid_amount DECIMAL(10,2),
    payment_method TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Plan subscriptions table
CREATE TABLE plan_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    plan_name TEXT NOT NULL, -- basic, pro, business, enterprise
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'active', -- active, cancelled, expired
    auto_renew BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. Admin actions table
CREATE TABLE admin_actions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL, -- assign_number, approve, ban, etc.
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 12. Support tickets table
CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    ticket_number TEXT UNIQUE NOT NULL,
    subject TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT DEFAULT 'general',
    status ticket_status DEFAULT 'open',
    priority ticket_priority DEFAULT 'medium',
    comments JSONB DEFAULT '[]',
    admin_response TEXT,
    resolution TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    closed_at TIMESTAMP WITH TIME ZONE,
    reopened_at TIMESTAMP WITH TIME ZONE,
    reopen_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_clients_user_id ON clients(user_id);
CREATE INDEX idx_clients_plivo_number ON clients(plivo_number);
CREATE INDEX idx_clients_status ON clients(status);

CREATE INDEX idx_products_client_id ON products(client_id);
CREATE INDEX idx_bot_features_client_id ON bot_features(client_id);

CREATE INDEX idx_appointment_slots_client_id ON appointment_slots(client_id);
CREATE INDEX idx_appointment_slots_time ON appointment_slots(slot_time);
CREATE INDEX idx_appointment_slots_booked ON appointment_slots(is_booked);

CREATE INDEX idx_call_logs_client_id ON call_logs(client_id);
CREATE INDEX idx_call_logs_time ON call_logs(call_time);
CREATE INDEX idx_call_logs_caller ON call_logs(caller_number);

CREATE INDEX idx_whatsapp_logs_client_id ON whatsapp_logs(client_id);
CREATE INDEX idx_whatsapp_logs_timestamp ON whatsapp_logs(timestamp);
CREATE INDEX idx_whatsapp_logs_status ON whatsapp_logs(status);

CREATE INDEX idx_invoices_client_id ON invoices(client_id);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_number ON invoices(invoice_number);

CREATE INDEX idx_payments_client_id ON payments(client_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_gateway_id ON payments(payment_gateway_id);

CREATE INDEX idx_support_tickets_client_id ON support_tickets(client_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);

-- Row Level Security (RLS) policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE bot_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointment_slots ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE plan_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (id = auth.uid()::uuid);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (id = auth.uid()::uuid);

-- RLS Policies for clients
CREATE POLICY "Users can view own client data" ON clients FOR SELECT USING (user_id = auth.uid()::uuid);
CREATE POLICY "Users can insert own client data" ON clients FOR INSERT WITH CHECK (user_id = auth.uid()::uuid);
CREATE POLICY "Users can update own client data" ON clients FOR UPDATE USING (user_id = auth.uid()::uuid);

-- RLS Policies for products
CREATE POLICY "Users can manage own products" ON products FOR ALL USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);

-- RLS Policies for bot_features
CREATE POLICY "Users can manage own bot features" ON bot_features FOR ALL USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);

-- RLS Policies for appointment_slots
CREATE POLICY "Users can manage own appointments" ON appointment_slots FOR ALL USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);

-- RLS Policies for call_logs
CREATE POLICY "Users can view own call logs" ON call_logs FOR SELECT USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);
CREATE POLICY "System can insert call logs" ON call_logs FOR INSERT WITH CHECK (true);

-- RLS Policies for whatsapp_logs
CREATE POLICY "Users can view own WhatsApp logs" ON whatsapp_logs FOR SELECT USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);
CREATE POLICY "System can manage WhatsApp logs" ON whatsapp_logs FOR ALL WITH CHECK (true);

-- RLS Policies for invoices
CREATE POLICY "Users can manage own invoices" ON invoices FOR ALL USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);

-- RLS Policies for payments
CREATE POLICY "Users can manage own payments" ON payments FOR ALL USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);

-- RLS Policies for plan_subscriptions
CREATE POLICY "Users can view own subscriptions" ON plan_subscriptions FOR SELECT USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);

-- RLS Policies for admin_actions (admin only)
CREATE POLICY "Admins can manage admin actions" ON admin_actions FOR ALL USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid()::uuid AND role = 'admin')
);

-- RLS Policies for support_tickets
CREATE POLICY "Users can manage own support tickets" ON support_tickets FOR ALL USING (
    client_id IN (SELECT id FROM clients WHERE user_id = auth.uid()::uuid)
);

-- Functions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers
CREATE TRIGGER update_support_tickets_updated_at 
    BEFORE UPDATE ON support_tickets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin user (change credentials in production)
INSERT INTO profiles (id, email, password, full_name, role)
VALUES (
    uuid_generate_v4(),
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/kMy.Vl.VG', -- 'admin123' hashed
    'Admin User',
    'admin'
) ON CONFLICT (email) DO NOTHING; 
-- FIXED Customer Portal System Migration
-- Run this in your Supabase SQL editor
-- This script works with existing tables and adds missing columns/tables

-- 1. First, let's check and create the customers table with all required columns
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    phone VARCHAR(20) UNIQUE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    address TEXT DEFAULT '',
    city VARCHAR(100) DEFAULT '',
    state VARCHAR(100) DEFAULT '',
    pincode VARCHAR(10) DEFAULT '',
    date_of_birth DATE,
    gender VARCHAR(10) DEFAULT '',
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_phone_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    profile_picture_url TEXT,
    preferences JSONB DEFAULT '{}',
    communication_preferences JSONB DEFAULT '{"sms": true, "email": true, "whatsapp": true}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT customers_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- 2. Add missing columns to existing customers table if they don't exist
DO $$ 
BEGIN
    -- Add is_active column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'is_active') THEN
        ALTER TABLE customers ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
    
    -- Add password_hash column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'password_hash') THEN
        ALTER TABLE customers ADD COLUMN password_hash VARCHAR(255);
    END IF;
    
    -- Add is_email_verified column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'is_email_verified') THEN
        ALTER TABLE customers ADD COLUMN is_email_verified BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add is_phone_verified column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'is_phone_verified') THEN
        ALTER TABLE customers ADD COLUMN is_phone_verified BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add communication_preferences column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'communication_preferences') THEN
        ALTER TABLE customers ADD COLUMN communication_preferences JSONB DEFAULT '{"sms": true, "email": true, "whatsapp": true}';
    END IF;
    
    -- Add last_login_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'last_login_at') THEN
        ALTER TABLE customers ADD COLUMN last_login_at TIMESTAMP WITH TIME ZONE;
    END IF;
    
    -- Add failed_login_attempts column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'failed_login_attempts') THEN
        ALTER TABLE customers ADD COLUMN failed_login_attempts INTEGER DEFAULT 0;
    END IF;
    
    -- Add locked_until column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'locked_until') THEN
        ALTER TABLE customers ADD COLUMN locked_until TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- 3. Customer-Client Interactions (Multi-client support)
CREATE TABLE IF NOT EXISTS customer_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    call_log_id UUID REFERENCES call_logs(id) ON DELETE SET NULL,
    interaction_type VARCHAR(50) NOT NULL DEFAULT 'call',
    interaction_source VARCHAR(100),
    first_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    interaction_count INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'active',
    notes TEXT,
    conversation_summary TEXT,
    product_interests JSONB DEFAULT '[]',
    estimated_value DECIMAL(10,2) DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(customer_id, client_id)
);

-- 4. Enhanced Registration Tokens with conversation data
CREATE TABLE IF NOT EXISTS customer_registration_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token VARCHAR(255) UNIQUE NOT NULL,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    call_log_id UUID REFERENCES call_logs(id) ON DELETE SET NULL,
    phone_number VARCHAR(20),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP WITH TIME ZONE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    customer_name VARCHAR(255),
    product_interests JSONB DEFAULT '[]',
    estimated_amount DECIMAL(10,2) DEFAULT 0,
    conversation_summary TEXT,
    conversation_data JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Customer Orders/Inquiries
CREATE TABLE IF NOT EXISTS customer_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    interaction_id UUID REFERENCES customer_interactions(id) ON DELETE SET NULL,
    call_log_id UUID REFERENCES call_logs(id) ON DELETE SET NULL,
    order_number VARCHAR(100) UNIQUE NOT NULL,
    order_type VARCHAR(50) NOT NULL DEFAULT 'inquiry',
    title VARCHAR(255) NOT NULL,
    description TEXT,
    products JSONB DEFAULT '[]',
    total_amount DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(50) DEFAULT 'pending',
    scheduled_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    payment_status VARCHAR(50) DEFAULT 'pending',
    payment_details JSONB DEFAULT '{}',
    delivery_address TEXT,
    special_instructions TEXT,
    timeline JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Customer-Client Chat System
CREATE TABLE IF NOT EXISTS customer_chats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    interaction_id UUID REFERENCES customer_interactions(id) ON DELETE SET NULL,
    chat_title VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_preview TEXT,
    unread_count_customer INTEGER DEFAULT 0,
    unread_count_client INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(customer_id, client_id)
);

-- 7. Chat Messages
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chat_id UUID NOT NULL REFERENCES customer_chats(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL,
    sender_id UUID NOT NULL,
    sender_name VARCHAR(255),
    message_type VARCHAR(50) DEFAULT 'text',
    content TEXT NOT NULL,
    attachments JSONB DEFAULT '[]',
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    reply_to_message_id UUID REFERENCES chat_messages(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Customer Email Tokens (OTP system)
CREATE TABLE IF NOT EXISTS customer_email_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(6) NOT NULL,
    token_type VARCHAR(50) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Customer Sessions
CREATE TABLE IF NOT EXISTS customer_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Add missing columns to call_logs if they don't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'call_logs' AND column_name = 'customer_id') THEN
        ALTER TABLE call_logs ADD COLUMN customer_id UUID REFERENCES customers(id) ON DELETE SET NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'call_logs' AND column_name = 'bot_conversation_data') THEN
        ALTER TABLE call_logs ADD COLUMN bot_conversation_data JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'call_logs' AND column_name = 'customer_interest_level') THEN
        ALTER TABLE call_logs ADD COLUMN customer_interest_level INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'call_logs' AND column_name = 'follow_up_required') THEN
        ALTER TABLE call_logs ADD COLUMN follow_up_required BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- 11. Create indexes for performance (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_customer_interactions_customer_id ON customer_interactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_client_id ON customer_interactions(client_id);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_active ON customer_interactions(status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_customer_registration_tokens_token ON customer_registration_tokens(token);
CREATE INDEX IF NOT EXISTS idx_customer_registration_tokens_phone ON customer_registration_tokens(phone_number);
CREATE INDEX IF NOT EXISTS idx_customer_registration_tokens_unused ON customer_registration_tokens(is_used) WHERE is_used = false;

CREATE INDEX IF NOT EXISTS idx_customer_orders_customer_id ON customer_orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_orders_client_id ON customer_orders(client_id);
CREATE INDEX IF NOT EXISTS idx_customer_orders_status ON customer_orders(status);

CREATE INDEX IF NOT EXISTS idx_customer_chats_customer_id ON customer_chats(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_chats_client_id ON customer_chats(client_id);
CREATE INDEX IF NOT EXISTS idx_customer_chats_active ON customer_chats(status) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_id ON chat_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_unread ON chat_messages(is_read) WHERE is_read = false;

CREATE INDEX IF NOT EXISTS idx_customer_email_tokens_email ON customer_email_tokens(email);
CREATE INDEX IF NOT EXISTS idx_customer_email_tokens_unused ON customer_email_tokens(is_used) WHERE is_used = false;

CREATE INDEX IF NOT EXISTS idx_customer_sessions_customer_id ON customer_sessions(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_sessions_active ON customer_sessions(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_call_logs_customer_id ON call_logs(customer_id);

-- 12. Create or replace the update trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 13. Create triggers for updated_at timestamps (drop if exists first)
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at 
    BEFORE UPDATE ON customers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_interactions_updated_at ON customer_interactions;
CREATE TRIGGER update_customer_interactions_updated_at 
    BEFORE UPDATE ON customer_interactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_orders_updated_at ON customer_orders;
CREATE TRIGGER update_customer_orders_updated_at 
    BEFORE UPDATE ON customer_orders 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_chats_updated_at ON customer_chats;
CREATE TRIGGER update_customer_chats_updated_at 
    BEFORE UPDATE ON customer_chats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 14. Insert some sample data for testing (optional)
-- Uncomment if you want sample data
/*
INSERT INTO customers (name, email, phone, is_email_verified, is_active) VALUES
('John Doe', '<EMAIL>', '+91 9876543210', true, true),
('Jane Smith', '<EMAIL>', '+91 9876543211', true, true)
ON CONFLICT (email) DO NOTHING;
*/

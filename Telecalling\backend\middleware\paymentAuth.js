const { supabaseAdmin } = require('../config/supabase');
const razorpayService = require('../services/razorpayService');

/**
 * Middleware to check if user has an active subscription
 */
const requireActiveSubscription = async (req, res, next) => {
  try {
    const clientId = req.user.authId;

    // Get client's current subscription
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select(`
        *,
        current_plan:subscription_plans!current_plan_id (*)
      `)
      .eq('id', clientId)
      .single();

    if (clientError || !client) {
      return res.status(404).json({
        success: false,
        error: 'Client not found'
      });
    }

    // Check if subscription is active
    if (client.subscription_status !== 'active') {
      return res.status(403).json({
        success: false,
        error: 'Active subscription required',
        code: 'SUBSCRIPTION_REQUIRED',
        currentPlan: client.current_plan
      });
    }

    // Check if subscription has expired
    if (client.subscription_ends_at && new Date(client.subscription_ends_at) < new Date()) {
      // Update status to expired
      await supabaseAdmin
        .from('clients')
        .update({
          subscription_status: 'expired',
          updated_at: new Date().toISOString()
        })
        .eq('id', clientId);

      return res.status(403).json({
        success: false,
        error: 'Subscription has expired',
        code: 'SUBSCRIPTION_EXPIRED',
        expiredAt: client.subscription_ends_at
      });
    }

    // Attach subscription info to request
    req.subscription = {
      plan: client.current_plan,
      status: client.subscription_status,
      endsAt: client.subscription_ends_at
    };

    next();
  } catch (error) {
    console.error('Subscription check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify subscription status'
    });
  }
};

/**
 * Middleware to check plan limits
 */
const checkPlanLimits = (resource) => {
  return async (req, res, next) => {
    try {
      const clientId = req.user.authId;
      const subscription = req.subscription;

      if (!subscription || !subscription.plan) {
        return res.status(403).json({
          success: false,
          error: 'No active subscription plan found'
        });
      }

      const plan = subscription.plan;
      let currentUsage = 0;
      let limit = 0;

      switch (resource) {
        case 'calls':
          // Get current month's call count
          const startOfMonth = new Date();
          startOfMonth.setDate(1);
          startOfMonth.setHours(0, 0, 0, 0);

          const { data: calls } = await supabaseAdmin
            .from('call_logs')
            .select('id')
            .eq('client_id', clientId)
            .gte('created_at', startOfMonth.toISOString());

          currentUsage = calls?.length || 0;
          limit = plan.max_calls;
          break;

        case 'products':
          const { data: products } = await supabaseAdmin
            .from('products')
            .select('id')
            .eq('client_id', clientId);

          currentUsage = products?.length || 0;
          limit = plan.max_products;
          break;

        case 'phone_numbers':
          // This would depend on how you track phone numbers
          // For now, assume 1 phone number per client
          currentUsage = 1;
          limit = plan.max_phone_numbers;
          break;

        default:
          return next(); // No limit check for unknown resources
      }

      // Check if limit is exceeded (-1 means unlimited)
      if (limit !== -1 && currentUsage >= limit) {
        return res.status(403).json({
          success: false,
          error: `${resource} limit exceeded`,
          code: 'PLAN_LIMIT_EXCEEDED',
          currentUsage,
          limit,
          planName: plan.display_name
        });
      }

      // Attach usage info to request
      req.usage = {
        ...req.usage,
        [resource]: {
          current: currentUsage,
          limit: limit
        }
      };

      next();
    } catch (error) {
      console.error('Plan limit check error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check plan limits'
      });
    }
  };
};

/**
 * Middleware to validate Razorpay webhook signature
 */
const validateWebhookSignature = (req, res, next) => {
  try {
    const signature = req.headers['x-razorpay-signature'];
    const body = req.body;

    if (!signature) {
      return res.status(400).json({
        success: false,
        error: 'Missing webhook signature'
      });
    }

    const isValid = razorpayService.verifyWebhookSignature(body, signature);

    if (!isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid webhook signature'
      });
    }

    next();
  } catch (error) {
    console.error('Webhook signature validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate webhook signature'
    });
  }
};

/**
 * Middleware to check if feature is available in current plan
 */
const requirePlanFeature = (feature) => {
  return (req, res, next) => {
    try {
      const subscription = req.subscription;

      if (!subscription || !subscription.plan) {
        return res.status(403).json({
          success: false,
          error: 'No active subscription plan found'
        });
      }

      const plan = subscription.plan;
      const features = plan.features || {};

      // Check if feature is available in the plan
      if (!features[feature]) {
        return res.status(403).json({
          success: false,
          error: `Feature '${feature}' not available in your current plan`,
          code: 'FEATURE_NOT_AVAILABLE',
          currentPlan: plan.display_name,
          requiredFeature: feature
        });
      }

      next();
    } catch (error) {
      console.error('Feature check error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check plan features'
      });
    }
  };
};

/**
 * Middleware to log payment-related activities
 */
const logPaymentActivity = (activity) => {
  return (req, res, next) => {
    // Log the activity (you can extend this to save to database)
    console.log(`Payment Activity: ${activity}`, {
      clientId: req.user?.authId,
      timestamp: new Date().toISOString(),
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      body: req.body
    });

    next();
  };
};

module.exports = {
  requireActiveSubscription,
  checkPlanLimits,
  validateWebhookSignature,
  requirePlanFeature,
  logPaymentActivity
};

#!/usr/bin/env node

/**
 * Test AI Upload Route
 */

const fetch = require('node-fetch');

async function testAIRoute() {
  try {
    console.log('🧪 Testing AI upload route...');
    
    // Test if route exists
    const response = await fetch('http://localhost:5000/api/products/supported-types');
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ AI route is working!');
      console.log('📋 Supported types:', data.types);
    } else {
      console.log('❌ Route not found or error:', response.status);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAIRoute();

import React, { useState, useEffect } from 'react';
import { 
  Crown, 
  TrendingUp, 
  Calendar, 
  CheckCircle,
  Clock,
  Zap,
  ArrowRight,
  CreditCard
} from 'lucide-react';
import { Link } from 'react-router-dom';
import subscriptionAPI from '../../services/subscriptionAPI';
import PaymentModal from '../modals/PaymentModal';
import PaymentSuccessModal from '../modals/PaymentSuccessModal';

const PaymentCard = () => {
  const [subscription, setSubscription] = useState(null);
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentResult, setPaymentResult] = useState(null);

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      const [subscriptionResponse, plansResponse] = await Promise.all([
        subscriptionAPI.getCurrentSubscription(),
        subscriptionAPI.getPlans()
      ]);

      if (subscriptionResponse.success) {
        setSubscription(subscriptionResponse.subscription);
      }

      if (plansResponse.success) {
        setPlans(plansResponse.plans);
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = (plan) => {
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = (result) => {
    setPaymentResult(result);
    setShowPaymentModal(false);
    setShowSuccessModal(true);
    fetchSubscriptionData();
  };

  if (loading) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="animate-pulse">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-6 w-6 bg-gray-200 rounded"></div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-5 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const currentPlan = subscription?.subscription_plans;
  const isFreePlan = !subscription || currentPlan?.name === 'free';
  const nextPlan = plans.find(p => p.sort_order > (currentPlan?.sort_order || 0));

  // Get the appropriate plan to suggest for upgrade
  const suggestedPlan = isFreePlan 
    ? plans.find(p => p.name === 'pro') 
    : nextPlan;

  return (
    <>
      {/* Current Plan Card */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {isFreePlan ? (
                <Clock className="h-6 w-6 text-gray-600" />
              ) : (
                <Crown className="h-6 w-6 text-blue-600" />
              )}
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Current Plan</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {currentPlan?.display_name || 'Free Plan'}
                </dd>
              </dl>
            </div>
            <div className="flex-shrink-0">
              <div className="text-right">
                <div className="text-sm font-medium text-gray-900">
                  {currentPlan?.price ? `₹${currentPlan.price.toLocaleString()}/mo` : 'Free'}
                </div>
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  subscription?.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {subscription?.status === 'active' ? (
                    <>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Active
                    </>
                  ) : (
                    <>
                      <Clock className="w-3 h-3 mr-1" />
                      Free
                    </>
                  )}
                </span>
              </div>
            </div>
          </div>
          
          {/* Plan Details */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Calls: {currentPlan?.max_calls === -1 ? 'Unlimited' : (currentPlan?.max_calls || 10)}/month</span>
              <span>Products: {currentPlan?.max_products === -1 ? 'Unlimited' : (currentPlan?.max_products || 5)}</span>
            </div>
            {subscription?.ends_at && (
              <div className="mt-2 flex items-center text-xs text-gray-500">
                <Calendar className="w-3 h-3 mr-1" />
                Renews on {new Date(subscription.ends_at).toLocaleDateString()}
              </div>
            )}
          </div>
        </div>
        
        <div className="bg-gray-50 px-5 py-3">
          <div className="flex items-center justify-between">
            <Link to="/dashboard/payments" className="text-blue-600 hover:text-blue-900 text-sm">
              Manage billing
            </Link>
            {suggestedPlan && (
              <button
                onClick={() => handleUpgrade(suggestedPlan)}
                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-900"
              >
                {isFreePlan ? 'Upgrade to Pro' : 'Upgrade Plan'}
                <ArrowRight className="w-3 h-3 ml-1" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Upgrade Suggestion Card (only for free users) */}
      {isFreePlan && suggestedPlan && (
        <div className="bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden shadow rounded-lg text-white">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-blue-100 truncate">Recommended Upgrade</dt>
                  <dd className="text-lg font-medium text-white">
                    {suggestedPlan.display_name}
                  </dd>
                </dl>
              </div>
              <div className="flex-shrink-0">
                <div className="text-right">
                  <div className="text-lg font-bold text-white">
                    ₹{suggestedPlan.price.toLocaleString()}
                  </div>
                  <div className="text-xs text-blue-200">per month</div>
                </div>
              </div>
            </div>
            
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm text-blue-100">
                <span>{suggestedPlan.max_calls} calls/month</span>
                <span>Advanced features</span>
                <span>Priority support</span>
              </div>
            </div>
          </div>
          
          <div className="bg-black bg-opacity-10 px-5 py-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-100">
                🚀 Scale your business today
              </span>
              <button
                onClick={() => handleUpgrade(suggestedPlan)}
                className="inline-flex items-center px-3 py-1 bg-white text-blue-600 text-sm font-medium rounded-md hover:bg-gray-100 transition-colors"
              >
                <TrendingUp className="w-3 h-3 mr-1" />
                Upgrade Now
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modals */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        selectedPlan={selectedPlan}
        onSuccess={handlePaymentSuccess}
      />
      
      <PaymentSuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        paymentData={paymentResult}
      />
    </>
  );
};

export default PaymentCard;

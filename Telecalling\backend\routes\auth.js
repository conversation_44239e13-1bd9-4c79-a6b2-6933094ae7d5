const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const { db<PERSON>el<PERSON>, supabaseAuthHelpers } = require('../config/supabase');
const { auth } = require('../middleware/auth');
const rateLimit = require('express-rate-limit');
const router = express.Router();

// Rate limiting
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: { error: 'Too many authentication attempts, please try again later' }
});

const otpLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // limit each IP to 3 OTP requests per windowMs
  message: { error: 'Too many OTP requests, please try again later' }
});

// Helper function to generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Helper function to generate JWT tokens with unique identifiers
const generateTokens = (userId) => {
  // Check if required JWT secrets are available
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET environment variable is not set');
  }

  if (!process.env.JWT_REFRESH_SECRET) {
    throw new Error('JWT_REFRESH_SECRET environment variable is not set');
  }

  // Add unique components to prevent duplicate tokens
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);
  const sessionId = `${timestamp}_${randomId}`;

  const accessToken = jwt.sign(
    {
      userId,
      type: 'access',
      sessionId: sessionId,
      iat: Math.floor(timestamp / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn: '30d', noTimestamp: false } // MNC-style: 30 days persistent session
  );

  const refreshToken = jwt.sign(
    {
      userId,
      type: 'refresh',
      sessionId: sessionId,
      iat: Math.floor(timestamp / 1000)
    },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: '90d', noTimestamp: false } // MNC-style: 90 days refresh token
  );

  return { accessToken, refreshToken };
};

// Helper function to call Supabase edge function for sending emails
const sendOTPEmail = async (email, otp, type, userName, businessName) => {
  try {
    console.log('🔍 Email sending debug:');
    console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Set' : 'Missing');
    console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing');
    console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? 'Set' : 'Missing');
    
    // Check if required environment variables are set
    if (!process.env.SUPABASE_URL) {
      throw new Error('SUPABASE_URL environment variable is not set');
    }
    
    if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is not set');
    }

    const supabaseUrl = process.env.SUPABASE_URL.replace(/\/$/, ''); // Remove trailing slash
    const endpoint = `${supabaseUrl}/functions/v1/send-otp-email`;
    
    console.log('📧 Sending OTP email to:', email);
    console.log('📧 Endpoint:', endpoint);
    console.log('📧 OTP Type:', type);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        email,
        otp,
        type,
        userName,
        businessName
      })
    });

    console.log('📧 Response status:', response.status);
    console.log('📧 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('📧 Error response:', errorText);
      
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (e) {
        errorData = { error: errorText };
      }
      
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('📧 Email sent successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Error sending OTP email:', error.message);
    console.error('❌ Full error:', error);
    throw new Error(`Failed to send email: ${error.message}`);
  }
};

// Helper function to clean up expired OTPs
const cleanupExpiredOTPs = async () => {
  try {
    await dbHelpers.query(`
      DELETE FROM otp_verifications 
      WHERE expires_at < NOW() AND used_at IS NULL
    `);
  } catch (error) {
    console.error('Error cleaning up expired OTPs:', error);
  }
};

// 1. SIGNUP ROUTE
router.post('/signup', authLimiter, async (req, res) => {
  try {
    const { email, password, businessName, username } = req.body;

    // Debug environment variables
    console.log('🔍 Environment check:');
    console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
    console.log('SUPABASE_URL exists:', !!process.env.SUPABASE_URL);
    console.log('JWT_SECRET exists:', !!process.env.JWT_SECRET);
    
    // Validation
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
  }

    if (password.length < 8) {
      return res.status(400).json({ error: 'Password must be at least 8 characters long' });
  }

  // Check if user already exists in clients table
    console.log('📍 Checking if user exists with email:', email);
    const existingUser = await dbHelpers.findOne('clients', { email });
  if (existingUser) {
      return res.status(409).json({ error: 'User already exists with this email' });
  }

    // Check if there's a pending registration for this email
    const existingPending = await dbHelpers.findOne('pending_registrations', { email });
    if (existingPending) {
      // Delete the old pending registration to allow new signup
      await dbHelpers.delete('pending_registrations', { email });
  }

  // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create pending registration (not a full user yet)
    const pendingRegistration = await dbHelpers.insert('pending_registrations', {
      id: uuidv4(),
      email,
      password_hash: passwordHash,
      business_name: businessName || null,
      username: username || null,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      created_at: new Date()
    });

    // Generate OTP for email verification
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await dbHelpers.insert('otp_verifications', {
      id: uuidv4(),
      pending_registration_id: pendingRegistration.id,
      email,
      otp_code: otp,
      otp_type: 'email_verification',
      expires_at: expiresAt,
      created_at: new Date()
    });

    // Send OTP email
    try {
      await sendOTPEmail(email, otp, 'email_verification', username, businessName);
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError);
      // Don't fail the signup if email fails - user can request resend
    }

      res.status(201).json({
      success: true,
      message: 'Registration initiated successfully. Please check your email for verification code to complete your account creation.',
      pendingRegistration: {
        id: pendingRegistration.id,
        email: pendingRegistration.email,
        businessName: pendingRegistration.business_name,
        username: pendingRegistration.username
      }
    });

  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 2. LOGIN ROUTE
router.post('/login', authLimiter, async (req, res) => {
  try {
  const { email, password } = req.body;

  if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
  }

    // Find user
    const user = await dbHelpers.findOne('clients', { email });
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if account is locked
    if (user.locked_until && new Date() < new Date(user.locked_until)) {
      return res.status(423).json({ error: 'Account is temporarily locked due to multiple failed login attempts' });
    }

        // Check if account is active
    if (!user.is_active) {
      return res.status(403).json({ 
        error: 'Account is not activated. Please verify your email first.',
        code: 'ACCOUNT_NOT_ACTIVATED'
      });
    }

  // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
  if (!isValidPassword) {
      // Increment failed login attempts
      const failedAttempts = (user.failed_login_attempts || 0) + 1;
      const updateData = { failed_login_attempts: failedAttempts };
      
      // Lock account after 5 failed attempts
      if (failedAttempts >= 5) {
        updateData.locked_until = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      }
      
      await dbHelpers.update('clients', updateData, { id: user.id });
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if email is verified
    if (!user.is_email_verified) {
      return res.status(403).json({ 
        error: 'Email not verified', 
        code: 'EMAIL_NOT_VERIFIED',
        userId: user.id 
      });
    }

    // Reset failed login attempts and update last login
    await dbHelpers.update('clients', {
      failed_login_attempts: 0,
      locked_until: null,
      last_login_at: new Date()
    }, { id: user.id });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Store session
    await dbHelpers.insert('login_sessions', {
      id: uuidv4(),
      client_id: user.id,
      session_token: accessToken,
      refresh_token: refreshToken,
      expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days - MNC persistent session
      ip_address: req.ip,
      user_agent: req.headers['user-agent'],
      created_at: new Date()
    });

  res.json({
      success: true,
    message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        businessName: user.business_name,
        username: user.username,
        isEmailVerified: user.is_email_verified
      },
      tokens: {
        accessToken,
        refreshToken
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 3. VERIFY EMAIL OTP ROUTE
router.post('/verify-email', otpLimiter, async (req, res) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({ error: 'Email and OTP are required' });
    }

    // Clean up expired OTPs
    await cleanupExpiredOTPs();

    // Find OTP verification record
    const otpRecord = await dbHelpers.query(`
      SELECT ov.*, pr.* FROM otp_verifications ov
      LEFT JOIN pending_registrations pr ON ov.pending_registration_id = pr.id
      WHERE ov.email = $1 AND ov.otp_code = $2 AND ov.otp_type = 'email_verification' 
      AND ov.expires_at > NOW() AND ov.used_at IS NULL
      ORDER BY ov.created_at DESC
      LIMIT 1
    `, [email, otp]);

    if (!otpRecord || otpRecord.length === 0) {
      return res.status(400).json({ error: 'Invalid or expired OTP' });
    }

    const otp_verification = otpRecord[0];

    // Check attempts
    if (otp_verification.attempts >= otp_verification.max_attempts) {
      return res.status(429).json({ error: 'Maximum OTP attempts exceeded' });
    }

    // Mark OTP as used
    await dbHelpers.update('otp_verifications', {
      used_at: new Date(),
      attempts: otp_verification.attempts + 1
    }, { id: otp_verification.id });

    let user;

    // Check if this is for a pending registration or existing user
    if (otp_verification.pending_registration_id) {
      // This is a new registration - create both Supabase Auth user and client record
      let supabaseUser = null;
      
      try {
        // Create user in Supabase Auth
        supabaseUser = await supabaseAuthHelpers.createUser(
          otp_verification.email,
          'temp_password_will_be_reset', // Temporary password
          {
            username: otp_verification.username,
            business_name: otp_verification.business_name
          }
        );

        // Mark the Supabase user as email verified
        await supabaseAuthHelpers.updateUser(supabaseUser.id, {
          email_confirm: true
        });
      } catch (supabaseError) {
        console.error('Failed to create Supabase Auth user:', supabaseError);
        // Continue with custom auth if Supabase Auth fails
      }

      // Create the actual user account in our clients table
      user = await dbHelpers.insert('clients', {
        id: uuidv4(),
        supabase_auth_id: supabaseUser?.id || null,
        email: otp_verification.email,
        password_hash: otp_verification.password_hash,
        business_name: otp_verification.business_name,
        username: otp_verification.username,
        is_email_verified: true,
        is_active: true,
        email_verified_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      });

      // Delete the pending registration
      await dbHelpers.delete('pending_registrations', { id: otp_verification.pending_registration_id });
    } else {
      // This is for an existing user - just update verification status
    await dbHelpers.update('clients', {
      is_email_verified: true,
        is_active: true,
      email_verified_at: new Date()
    }, { email });

      user = await dbHelpers.findOne('clients', { email });
    }

  res.json({
      success: true,
      message: 'Email verified successfully',
      user: {
        id: user.id,
        email: user.email,
        businessName: user.business_name,
        username: user.username,
        isEmailVerified: user.is_email_verified
      }
    });

  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 4. RESEND EMAIL VERIFICATION OTP
router.post('/resend-verification', otpLimiter, async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Check if there's a pending registration
    const pendingRegistration = await dbHelpers.findOne('pending_registrations', { email });
    
    // Check if there's an existing user
    const user = await dbHelpers.findOne('clients', { email });
    
    if (!pendingRegistration && !user) {
      return res.status(404).json({ error: 'No registration found for this email. Please sign up first.' });
    }

    if (user && user.is_email_verified && user.is_active) {
      return res.status(400).json({ error: 'Email is already verified and account is active' });
    }

    // Generate new OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    const otpData = {
      id: uuidv4(),
      email,
      otp_code: otp,
      otp_type: 'email_verification',
      expires_at: expiresAt,
      created_at: new Date()
    };

    // Add appropriate ID based on whether it's pending or existing user
    if (pendingRegistration) {
      otpData.pending_registration_id = pendingRegistration.id;
    } else {
      otpData.client_id = user.id;
    }

    await dbHelpers.insert('otp_verifications', otpData);

    // Send OTP email
    const userName = pendingRegistration ? pendingRegistration.username : user.username;
    const businessName = pendingRegistration ? pendingRegistration.business_name : user.business_name;
    await sendOTPEmail(email, otp, 'email_verification', userName, businessName);

  res.json({
      success: true,
      message: 'Verification code sent successfully'
    });

  } catch (error) {
    console.error('Resend verification error:', error);
    res.status(500).json({ error: 'Failed to send verification code' });
  }
});

// 5. FORGOT PASSWORD ROUTE
router.post('/forgot-password', otpLimiter, async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Find user
    const user = await dbHelpers.findOne('clients', { email });
    if (!user) {
      // Don't reveal if user exists or not
      return res.json({
        success: true,
        message: 'If the email exists, you will receive a password reset code'
      });
    }

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    await dbHelpers.insert('otp_verifications', {
      id: uuidv4(),
      client_id: user.id,
      email,
      otp_code: otp,
      otp_type: 'password_reset',
      expires_at: expiresAt,
      created_at: new Date()
    });

    // Send OTP email
    await sendOTPEmail(email, otp, 'password_reset', user.username, user.business_name);

    res.json({
      success: true,
      message: 'Password reset code sent successfully'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ error: 'Failed to send password reset code' });
  }
});

// 6. RESET PASSWORD ROUTE
router.post('/reset-password', authLimiter, async (req, res) => {
  try {
    const { email, otp, newPassword } = req.body;

    if (!email || !otp || !newPassword) {
      return res.status(400).json({ error: 'Email, OTP, and new password are required' });
  }

    if (newPassword.length < 8) {
      return res.status(400).json({ error: 'Password must be at least 8 characters long' });
  }

    // Clean up expired OTPs
    await cleanupExpiredOTPs();

    // Find OTP verification record
    const otpRecord = await dbHelpers.query(`
      SELECT * FROM otp_verifications 
      WHERE email = $1 AND otp_code = $2 AND otp_type = 'password_reset' 
      AND expires_at > NOW() AND used_at IS NULL
      ORDER BY created_at DESC
      LIMIT 1
    `, [email, otp]);

    if (!otpRecord || otpRecord.length === 0) {
      return res.status(400).json({ error: 'Invalid or expired OTP' });
    }

    const otp_verification = otpRecord[0];

    // Check attempts
    if (otp_verification.attempts >= otp_verification.max_attempts) {
      return res.status(429).json({ error: 'Maximum OTP attempts exceeded' });
    }

    // Mark OTP as used
    await dbHelpers.update('otp_verifications', {
      used_at: new Date(),
      attempts: otp_verification.attempts + 1
    }, { id: otp_verification.id });

  // Hash new password
    const passwordHash = await bcrypt.hash(newPassword, 12);

    // Update user password
    await dbHelpers.update('clients', {
      password_hash: passwordHash,
      failed_login_attempts: 0,
      locked_until: null,
      updated_at: new Date()
    }, { email });

    // Invalidate all existing sessions
    await dbHelpers.update('login_sessions', {
      is_active: false
    }, { client_id: otp_verification.client_id });

    res.json({
      success: true,
      message: 'Password reset successfully'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 7. REFRESH TOKEN ROUTE
router.post('/refresh-token', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ error: 'Refresh token is required' });
    }

    // Check if JWT_REFRESH_SECRET is available
    if (!process.env.JWT_REFRESH_SECRET) {
      console.error('JWT_REFRESH_SECRET is not set');
      return res.status(500).json({ error: 'Authentication service not configured' });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
    if (decoded.type !== 'refresh') {
      return res.status(401).json({ error: 'Invalid token type' });
    }

    // Find session
    const session = await dbHelpers.findOne('login_sessions', {
      refresh_token: refreshToken,
      is_active: true
    });

    if (!session || new Date() > new Date(session.expires_at)) {
      return res.status(401).json({ error: 'Invalid or expired refresh token' });
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(decoded.userId);

    // Update session
    await dbHelpers.update('login_sessions', {
      session_token: accessToken,
      refresh_token: newRefreshToken,
      last_accessed_at: new Date()
    }, { id: session.id });

    res.json({
      success: true,
      tokens: {
        accessToken,
        refreshToken: newRefreshToken
      }
    });

  } catch (error) {
    console.error('Refresh token error:', error);
    res.status(401).json({ error: 'Invalid refresh token' });
  }
});

// 8. LOGOUT ROUTE
router.post('/logout', auth, async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      // Invalidate specific session
      await dbHelpers.update('login_sessions', {
        is_active: false
      }, { refresh_token: refreshToken });
    } else {
      // Invalidate all sessions for the user
      await dbHelpers.update('login_sessions', {
        is_active: false
      }, { client_id: req.user.id });
  }

  res.json({
      success: true,
      message: 'Logged out successfully'
  });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 9. GET CURRENT USER ROUTE
router.get('/me', auth, async (req, res) => {
  try {
    const user = await dbHelpers.findOne('clients', { id: req.user.id });
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

  res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        businessName: user.business_name,
        username: user.username,
        phone: user.phone,
        isEmailVerified: user.is_email_verified,
        isActive: user.is_active,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at
      }
    });

  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 10. UPDATE PROFILE ROUTE
router.put('/profile', auth, async (req, res) => {
  try {
    const { businessName, username, phone } = req.body;

    const updateData = {
      updated_at: new Date()
    };

    if (businessName !== undefined) updateData.business_name = businessName;
    if (username !== undefined) updateData.username = username;
    if (phone !== undefined) updateData.phone = phone;

    const updatedUser = await dbHelpers.update('clients', updateData, { id: req.user.id });

    if (!updatedUser || updatedUser.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

  res.json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: updatedUser[0].id,
        email: updatedUser[0].email,
        businessName: updatedUser[0].business_name,
        username: updatedUser[0].username,
        phone: updatedUser[0].phone,
        isEmailVerified: updatedUser[0].is_email_verified
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// In-memory cache to prevent duplicate Google OAuth requests
const googleAuthCache = new Map();

// 7. GOOGLE OAUTH ROUTE
router.post('/google', authLimiter, async (req, res) => {
  try {
    const { supabaseUser, accessToken: supabaseAccessToken, refreshToken: supabaseRefreshToken } = req.body;

    if (!supabaseUser || !supabaseUser.email) {
      return res.status(400).json({ error: 'Invalid Google user data' });
    }

    const userEmail = supabaseUser.email;
    const cacheKey = `google_auth_${userEmail}_${req.ip}`;

    // Check if this request is already being processed
    if (googleAuthCache.has(cacheKey)) {
      const cacheData = googleAuthCache.get(cacheKey);
      const now = Date.now();

      // If the request was made less than 2 seconds ago, reject it
      if (now - cacheData.timestamp < 2000) {
        console.log('⚠️ Duplicate Google OAuth request detected for:', userEmail);
        return res.status(429).json({
          error: 'Request already in progress',
          message: 'Please wait for the current authentication to complete'
        });
      }
    }

    // Mark this request as being processed
    googleAuthCache.set(cacheKey, {
      timestamp: Date.now(),
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });

    // Auto-cleanup after 10 seconds
    setTimeout(() => {
      googleAuthCache.delete(cacheKey);
    }, 10000);

    console.log('🔍 Processing Google OAuth for:', userEmail);

    // Check if user already exists in our clients table
    let user = await dbHelpers.findOne('clients', { email: supabaseUser.email });

    if (user) {
      // User exists, update their Supabase auth ID if not set
      if (!user.supabase_auth_id) {
        await dbHelpers.update('clients', {
          supabase_auth_id: supabaseUser.id,
          updated_at: new Date()
        }, { id: user.id });

        user.supabase_auth_id = supabaseUser.id;
      }

      // Update last login
      await dbHelpers.update('clients', {
        last_login_at: new Date()
      }, { id: user.id });

      console.log('✅ Existing user logged in via Google:', user.email);
    } else {
      // Create new user from Google data
      const userData = {
        id: uuidv4(),
        supabase_auth_id: supabaseUser.id,
        email: supabaseUser.email,
        username: supabaseUser.user_metadata?.full_name || supabaseUser.email.split('@')[0],
        business_name: null, // Can be filled later
        is_email_verified: true, // Google emails are pre-verified
        is_active: true,
        email_verified_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: new Date(),
        metadata: {
          auth_provider: 'google',
          google_user_metadata: supabaseUser.user_metadata
        }
      };

      user = await dbHelpers.insert('clients', userData);
      console.log('✅ New user created via Google:', user.email);
    }

    // Generate our custom JWT tokens for consistency
    const { accessToken: customAccessToken, refreshToken: customRefreshToken } = generateTokens(user.id);

    // Clean up old sessions for this user to prevent accumulation
    try {
      await dbHelpers.query(`
        DELETE FROM login_sessions
        WHERE client_id = $1 AND (
          expires_at < NOW() OR
          created_at < NOW() - INTERVAL '1 hour'
        )
      `, [user.id]);
    } catch (cleanupError) {
      console.log('⚠️ Session cleanup warning:', cleanupError.message);
    }

    // Store session with retry logic for duplicates
    let sessionStored = false;
    let attempts = 0;
    const maxAttempts = 3;

    while (!sessionStored && attempts < maxAttempts) {
      try {
        await dbHelpers.insert('login_sessions', {
          id: uuidv4(),
          client_id: user.id,
          session_token: customAccessToken,
          refresh_token: customRefreshToken,
          expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days - MNC persistent session
          ip_address: req.ip,
          user_agent: req.headers['user-agent'],
          created_at: new Date()
        });
        sessionStored = true;
      } catch (sessionError) {
        attempts++;
        if (sessionError.code === '23505') {
          // Duplicate token - regenerate and try again
          console.log(`⚠️ Duplicate token detected, regenerating (attempt ${attempts})`);
          const newTokens = generateTokens(user.id);
          customAccessToken = newTokens.accessToken;
          customRefreshToken = newTokens.refreshToken;
        } else {
          throw sessionError;
        }
      }
    }

    if (!sessionStored) {
      throw new Error('Failed to store session after multiple attempts');
    }

    // Return user data and tokens
    res.json({
      success: true,
      message: 'Google authentication successful',
      user: {
        id: user.id,
        email: user.email,
        businessName: user.business_name,
        username: user.username,
        phone: user.phone,
        isEmailVerified: user.is_email_verified,
        isActive: user.is_active,
        createdAt: user.created_at,
        lastLoginAt: user.last_login_at,
        authProvider: 'google'
      },
      tokens: {
        accessToken: customAccessToken,
        refreshToken: customRefreshToken
      }
    });

  } catch (error) {
    console.error('Google OAuth error:', error);

    // Handle specific error types
    if (error.code === '23505') {
      return res.status(409).json({
        error: 'Concurrent authentication detected',
        message: 'Please try again in a moment'
      });
    }

    res.status(500).json({ error: 'Google authentication failed' });
  } finally {
    // Always clean up the cache entry
    if (req.body?.supabaseUser?.email) {
      const cleanupCacheKey = `google_auth_${req.body.supabaseUser.email}_${req.ip}`;
      googleAuthCache.delete(cleanupCacheKey);
    }
  }
});

// 8. ADMIN LOGIN ROUTE
router.post('/admin/login', authLimiter, async (req, res) => {
  try {
    const { adminId, password } = req.body;

    if (!adminId || !password) {
      return res.status(400).json({
        error: 'Missing credentials',
        message: 'Admin ID and password are required'
      });
    }

    console.log('🔍 Processing admin login for:', adminId);

    // Find admin by admin_id
    const admin = await dbHelpers.findOne('admins', { admin_id: adminId });

    if (!admin) {
      console.log('❌ Admin not found:', adminId);
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Admin ID or password is incorrect'
      });
    }

    if (!admin.is_active) {
      console.log('❌ Admin account is inactive:', adminId);
      return res.status(401).json({
        error: 'Account inactive',
        message: 'Admin account is deactivated'
      });
    }

    // Simple password verification - direct comparison
    const isPasswordValid = password === admin.password_hash;
    console.log('🔐 Simple password authentication for:', admin.admin_id);

    if (!isPasswordValid) {
      console.log('❌ Invalid password for admin:', adminId);
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Admin ID or password is incorrect'
      });
    }

    // Generate admin JWT tokens
    const adminTokens = generateAdminTokens(admin.id);

    // Store admin session
    await dbHelpers.insert('admin_sessions', {
      id: uuidv4(),
      admin_id: admin.id,
      session_token: adminTokens.accessToken,
      refresh_token: adminTokens.refreshToken,
      expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days - MNC persistent admin session
      ip_address: req.ip,
      user_agent: req.headers['user-agent'],
      created_at: new Date()
    });

    console.log('✅ Admin logged in successfully:', adminId);

    res.json({
      message: 'Admin login successful',
      admin: {
        id: admin.id,
        adminId: admin.admin_id,
        name: admin.name,
        email: admin.email
      },
      tokens: adminTokens
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during admin login'
    });
  }
});

// Helper function to generate admin JWT tokens
const generateAdminTokens = (adminId) => {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);
  const sessionId = `admin_${timestamp}_${randomId}`;

  const accessToken = jwt.sign(
    {
      adminId,
      type: 'admin_access',
      sessionId: sessionId,
      iat: Math.floor(timestamp / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn: '30d', noTimestamp: false } // MNC-style: 30 days for admin sessions
  );

  const refreshToken = jwt.sign(
    {
      adminId,
      type: 'admin_refresh',
      sessionId: sessionId,
      iat: Math.floor(timestamp / 1000)
    },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: '90d', noTimestamp: false } // MNC-style: 90 days for admin refresh
  );

  return { accessToken, refreshToken };
};

// SESSION MANAGEMENT ROUTES (MNC-Style)
const SessionManager = require('../middleware/sessionManager');

// Get all active sessions for current user
router.get('/sessions', auth, async (req, res) => {
  try {
    console.log('📱 Getting user sessions for:', req.user.id);

    const sessions = await SessionManager.getUserSessions(req.user.id);

    // Mark current session
    const currentToken = req.headers.authorization?.replace('Bearer ', '');
    if (currentToken) {
      const decoded = jwt.decode(currentToken);
      if (decoded && decoded.sessionId) {
        sessions.forEach(session => {
          if (session.sessionId === decoded.sessionId) {
            session.isCurrent = true;
          }
        });
      }
    }

    res.json({
      success: true,
      sessions: sessions,
      totalSessions: sessions.length
    });

  } catch (error) {
    console.error('❌ Get sessions error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get sessions'
    });
  }
});

// Logout from specific session
router.post('/logout-session', auth, async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    console.log('🔒 Logging out from session:', sessionId);

    const success = await SessionManager.invalidateSession(sessionId);

    if (success) {
      res.json({
        success: true,
        message: 'Session logged out successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to logout session'
      });
    }

  } catch (error) {
    console.error('❌ Logout session error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to logout session'
    });
  }
});

// Logout from all other sessions (keep current)
router.post('/logout-all-others', auth, async (req, res) => {
  try {
    console.log('🔒 Logging out from all other sessions for user:', req.user.id);

    // Get current session ID
    const currentToken = req.headers.authorization?.replace('Bearer ', '');
    let currentSessionId = null;

    if (currentToken) {
      const decoded = jwt.decode(currentToken);
      currentSessionId = decoded?.sessionId;
    }

    const success = await SessionManager.invalidateAllUserSessions(req.user.id, currentSessionId);

    if (success) {
      res.json({
        success: true,
        message: 'Logged out from all other sessions successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to logout from other sessions'
      });
    }

  } catch (error) {
    console.error('❌ Logout all others error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to logout from other sessions'
    });
  }
});

// Logout from all sessions (including current)
router.post('/logout-all', auth, async (req, res) => {
  try {
    console.log('🔒 Logging out from all sessions for user:', req.user.id);

    const success = await SessionManager.invalidateAllUserSessions(req.user.id);

    if (success) {
      res.json({
        success: true,
        message: 'Logged out from all sessions successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to logout from all sessions'
      });
    }

  } catch (error) {
    console.error('❌ Logout all sessions error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to logout from all sessions'
    });
  }
});

module.exports = router;
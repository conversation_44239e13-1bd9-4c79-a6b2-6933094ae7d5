-- Clients table with authentication fields
CREATE TABLE IF NOT EXISTS clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supabase_auth_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- Link to <PERSON><PERSON>base Auth
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255), -- Made optional for Supabase Auth users
    business_name VA<PERSON><PERSON><PERSON>(255),
    username VA<PERSON><PERSON><PERSON>(255),
    phone VARCHAR(20),
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Profile information
    profile_picture_url TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    
    -- Business information
    business_type VARCHAR(100),
    business_address TEXT,
    business_phone VARCHAR(20),
    website_url TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT clients_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Pending registrations table (users waiting for email verification)
CREATE TABLE IF NOT EXISTS pending_registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    business_name VARCHAR(255),
    username VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT pending_registrations_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- OTP verification table
CREATE TABLE IF NOT EXISTS otp_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    pending_registration_id UUID REFERENCES pending_registrations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    otp_code VARCHAR(6) NOT NULL,
    otp_type VARCHAR(50) NOT NULL, -- 'email_verification', 'password_reset', 'login_2fa'
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT otp_verifications_otp_code_check CHECK (otp_code ~ '^[0-9]{6}$')
);

-- Password reset tokens table
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Login sessions table (for session management)
CREATE TABLE IF NOT EXISTS login_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_email_verified ON clients(email, is_email_verified);
CREATE INDEX IF NOT EXISTS idx_clients_active ON clients(is_active);
CREATE INDEX IF NOT EXISTS idx_clients_created_at ON clients(created_at);

CREATE INDEX IF NOT EXISTS idx_pending_registrations_email ON pending_registrations(email);
CREATE INDEX IF NOT EXISTS idx_pending_registrations_expires_at ON pending_registrations(expires_at);

CREATE INDEX IF NOT EXISTS idx_otp_verifications_client_id ON otp_verifications(client_id);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_email ON otp_verifications(email);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_type ON otp_verifications(otp_type);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_expires_at ON otp_verifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_used_at ON otp_verifications(used_at);

CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_client_id ON password_reset_tokens(client_id);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON password_reset_tokens(token);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at);

CREATE INDEX IF NOT EXISTS idx_login_sessions_client_id ON login_sessions(client_id);
CREATE INDEX IF NOT EXISTS idx_login_sessions_session_token ON login_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_login_sessions_active ON login_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_login_sessions_expires_at ON login_sessions(expires_at);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_clients_updated_at 
    BEFORE UPDATE ON clients 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired OTPs and tokens
CREATE OR REPLACE FUNCTION cleanup_expired_auth_data()
RETURNS void AS $$
BEGIN
    -- Clean up expired pending registrations
    DELETE FROM pending_registrations 
    WHERE expires_at < NOW();
    
    -- Clean up expired OTPs
    DELETE FROM otp_verifications 
    WHERE expires_at < NOW() - INTERVAL '24 hours';
    
    -- Clean up expired password reset tokens
    DELETE FROM password_reset_tokens 
    WHERE expires_at < NOW() - INTERVAL '24 hours';
    
    -- Clean up expired login sessions
    DELETE FROM login_sessions 
    WHERE expires_at < NOW() - INTERVAL '7 days';
END;
$$ language 'plpgsql';

-- Enable Row Level Security (RLS) for better security
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE otp_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_reset_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for clients table
CREATE POLICY "Users can view their own profile" ON clients
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update their own profile" ON clients
    FOR UPDATE USING (auth.uid()::text = id::text);

-- RLS Policies for otp_verifications table
CREATE POLICY "Users can view their own OTP verifications" ON otp_verifications
    FOR SELECT USING (auth.uid()::text = client_id::text);

CREATE POLICY "Users can insert their own OTP verifications" ON otp_verifications
    FOR INSERT WITH CHECK (auth.uid()::text = client_id::text);

-- RLS Policies for password_reset_tokens table
CREATE POLICY "Users can view their own password reset tokens" ON password_reset_tokens
    FOR SELECT USING (auth.uid()::text = client_id::text);

CREATE POLICY "Users can insert their own password reset tokens" ON password_reset_tokens
    FOR INSERT WITH CHECK (auth.uid()::text = client_id::text);

-- RLS Policies for login_sessions table
CREATE POLICY "Users can view their own login sessions" ON login_sessions
    FOR SELECT USING (auth.uid()::text = client_id::text);

CREATE POLICY "Users can insert their own login sessions" ON login_sessions
    FOR INSERT WITH CHECK (auth.uid()::text = client_id::text);

CREATE POLICY "Users can update their own login sessions" ON login_sessions
    FOR UPDATE USING (auth.uid()::text = client_id::text);

-- Create a function to generate secure random OTP
CREATE OR REPLACE FUNCTION generate_otp()
RETURNS VARCHAR(6) AS $$
BEGIN
    RETURN lpad(floor(random() * 1000000)::text, 6, '0');
END;
$$ language 'plpgsql';

-- Create a function to hash passwords (for reference, actual hashing should be done in application)
CREATE OR REPLACE FUNCTION hash_password(password text)
RETURNS text AS $$
BEGIN
    -- This is a placeholder - actual password hashing should be done in the application
    -- using bcrypt or similar secure hashing algorithms
    RETURN password;
END;
$$ language 'plpgsql';

-- Insert sample data (optional, for testing)
-- INSERT INTO clients (email, password_hash, business_name, username, is_email_verified)
-- VALUES 
--     ('<EMAIL>', '$2b$10$example_hash', 'Test Business', 'testuser', true);

-- Useful queries for monitoring
-- SELECT * FROM clients WHERE created_at > NOW() - INTERVAL '1 day';
-- SELECT * FROM otp_verifications WHERE created_at > NOW() - INTERVAL '1 hour';
-- SELECT * FROM login_sessions WHERE is_active = true; 
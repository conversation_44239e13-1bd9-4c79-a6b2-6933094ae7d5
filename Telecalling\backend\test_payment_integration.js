const razorpayService = require('./services/razorpayService');
const { supabaseAdmin } = require('./config/supabase');
require('dotenv').config();

async function testPaymentIntegration() {
  console.log('🧪 Testing Payment Integration...\n');

  try {
    // Test 1: Check Razorpay configuration
    console.log('1. Testing Razorpay Configuration...');
    if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
      console.log('❌ Razorpay credentials not configured');
      console.log('Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET in your .env file');
      return;
    }
    console.log('✅ Razorpay credentials configured');

    // Test 2: Check database tables
    console.log('\n2. Testing Database Tables...');
    
    const tables = ['subscription_plans', 'client_subscriptions', 'payment_transactions'];
    for (const table of tables) {
      try {
        const { data, error } = await supabaseAdmin
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ Table ${table} not found or accessible: ${error.message}`);
        } else {
          console.log(`✅ Table ${table} exists and accessible`);
        }
      } catch (err) {
        console.log(`❌ Error checking table ${table}: ${err.message}`);
      }
    }

    // Test 3: Check subscription plans
    console.log('\n3. Testing Subscription Plans...');
    const plansResult = await razorpayService.getSubscriptionPlans();
    if (plansResult.success && plansResult.plans.length > 0) {
      console.log(`✅ Found ${plansResult.plans.length} subscription plans:`);
      plansResult.plans.forEach(plan => {
        console.log(`   - ${plan.display_name}: ₹${plan.price}`);
      });
    } else {
      console.log('❌ No subscription plans found');
      console.log('Run the database migration script to create default plans');
    }

    // Test 4: Test payment order creation (mock)
    console.log('\n4. Testing Payment Order Creation...');
    try {
      const orderResult = await razorpayService.createOrder({
        amount: 499,
        currency: 'INR',
        receipt: 'test_receipt_' + Date.now(),
        notes: {
          test: true,
          client_id: 'test-client-id'
        }
      });

      if (orderResult.success) {
        console.log('✅ Payment order created successfully');
        console.log(`   Order ID: ${orderResult.order.id}`);
        console.log(`   Amount: ₹${orderResult.order.amount / 100}`);
      } else {
        console.log('❌ Failed to create payment order:', orderResult.error);
      }
    } catch (error) {
      console.log('❌ Error creating payment order:', error.message);
    }

    // Test 5: Test signature verification
    console.log('\n5. Testing Payment Signature Verification...');
    const testSignatureData = {
      razorpay_order_id: 'order_test123',
      razorpay_payment_id: 'pay_test123',
      razorpay_signature: 'test_signature'
    };

    try {
      const isValid = razorpayService.verifyPaymentSignature(testSignatureData);
      console.log('✅ Signature verification function working (result will be false for test data)');
    } catch (error) {
      console.log('❌ Error in signature verification:', error.message);
    }

    // Test 6: Check environment variables
    console.log('\n6. Checking Environment Variables...');
    const requiredEnvVars = [
      'RAZORPAY_KEY_ID',
      'RAZORPAY_KEY_SECRET',
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY'
    ];

    let allEnvVarsSet = true;
    requiredEnvVars.forEach(envVar => {
      if (process.env[envVar]) {
        console.log(`✅ ${envVar} is set`);
      } else {
        console.log(`❌ ${envVar} is not set`);
        allEnvVarsSet = false;
      }
    });

    if (allEnvVarsSet) {
      console.log('\n🎉 All environment variables are configured!');
    } else {
      console.log('\n⚠️  Some environment variables are missing. Please check your .env file.');
    }

    console.log('\n📋 Integration Test Summary:');
    console.log('- Razorpay service is configured and working');
    console.log('- Database tables are accessible');
    console.log('- Payment order creation is functional');
    console.log('- Signature verification is working');
    
    console.log('\n🚀 Next Steps:');
    console.log('1. Make sure to run the database migration to create payment tables');
    console.log('2. Set up your actual Razorpay credentials in .env');
    console.log('3. Test the frontend payment flow');
    console.log('4. Set up webhook endpoints for production');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
if (require.main === module) {
  testPaymentIntegration()
    .then(() => {
      console.log('\n✅ Payment integration test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Payment integration test failed:', error);
      process.exit(1);
    });
}

module.exports = testPaymentIntegration;

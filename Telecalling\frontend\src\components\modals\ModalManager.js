import React, { useState } from 'react';
import LoginModal from './LoginModal';
import SignupModal from './SignupModal';
import EmailVerificationModal from './EmailVerificationModal';
import ForgotPasswordModal from './ForgotPasswordModal';
import ResetPasswordModal from './ResetPasswordModal';

const ModalManager = () => {
  const [currentModal, setCurrentModal] = useState(null);
  const [userEmail, setUserEmail] = useState('');

  const openModal = (modalType, email = '') => {
    setCurrentModal(modalType);
    if (email) setUserEmail(email);
  };

  const closeModal = () => {
    setCurrentModal(null);
    setUserEmail('');
  };

  const handleModalNavigation = {
    // Login Modal Navigation
    onSwitchToSignup: () => {
      console.log('Switching to signup');
      openModal('signup');
    },
    onForgotPassword: () => {
      console.log('Opening forgot password');
      openModal('forgotPassword');
    },

    // Signup Modal Navigation
    onSwitchToLogin: () => {
      console.log('Switching to login');
      openModal('login');
    },
    onEmailVerification: (email) => {
      console.log('Opening email verification for:', email);
      openModal('emailVerification', email);
    },

    // Email Verification Navigation
    onVerifySuccess: () => {
      console.log('Email verified successfully!');
      alert('Email verified successfully!');
      openModal('login');
    },

    // Forgot Password Navigation
    onBackToLogin: () => {
      console.log('Going back to login');
      openModal('login');
    },
    onResetEmailSent: (email) => {
      console.log('Reset email sent to:', email);
      setUserEmail(email);
    },
    onResetPassword: () => {
      console.log('Opening reset password');
      openModal('resetPassword');
    },

    // Reset Password Navigation
    onPasswordReset: () => {
      console.log('Password reset successfully!');
      alert('Password reset successfully!');
      openModal('login');
    }
  };

  return (
    <div>
      {/* Demo buttons to test the modals */}
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>Modal Navigation Demo</h2>
        <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button onClick={() => openModal('login')}>Open Login</button>
          <button onClick={() => openModal('signup')}>Open Signup</button>
          <button onClick={() => openModal('emailVerification', '<EMAIL>')}>Open Email Verification</button>
          <button onClick={() => openModal('forgotPassword')}>Open Forgot Password</button>
          <button onClick={() => openModal('resetPassword')}>Open Reset Password</button>
        </div>
      </div>

      {/* All Modals */}
      <LoginModal
        isOpen={currentModal === 'login'}
        onClose={closeModal}
        onSwitchToSignup={handleModalNavigation.onSwitchToSignup}
        onForgotPassword={handleModalNavigation.onForgotPassword}
      />

      <SignupModal
        isOpen={currentModal === 'signup'}
        onClose={closeModal}
        onSwitchToLogin={handleModalNavigation.onSwitchToLogin}
        onEmailVerification={handleModalNavigation.onEmailVerification}
      />

      <EmailVerificationModal
        isOpen={currentModal === 'emailVerification'}
        onClose={closeModal}
        onVerifySuccess={handleModalNavigation.onVerifySuccess}
        userEmail={userEmail}
      />

      <ForgotPasswordModal
        isOpen={currentModal === 'forgotPassword'}
        onClose={closeModal}
        onBackToLogin={handleModalNavigation.onBackToLogin}
        onResetEmailSent={handleModalNavigation.onResetEmailSent}
        onResetPassword={handleModalNavigation.onResetPassword}
      />

      <ResetPasswordModal
        isOpen={currentModal === 'resetPassword'}
        onClose={closeModal}
        onPasswordReset={handleModalNavigation.onPasswordReset}
        onBackToLogin={handleModalNavigation.onBackToLogin}
        resetToken="sample-reset-token"
      />
    </div>
  );
};

export default ModalManager; 
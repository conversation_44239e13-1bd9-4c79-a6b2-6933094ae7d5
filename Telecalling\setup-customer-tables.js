const { supabaseAdmin } = require('./backend/config/supabase');
const fs = require('fs');
const path = require('path');

async function setupCustomerTables() {
  try {
    console.log('🚀 Setting up customer portal tables...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'backend/database/customer_portal_migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
          const { error } = await supabaseAdmin.rpc('exec_sql', { sql: statement });
          
          if (error) {
            console.error(`❌ Error in statement ${i + 1}:`, error);
          } else {
            console.log(`✅ Statement ${i + 1} executed successfully`);
          }
        } catch (err) {
          console.error(`❌ Exception in statement ${i + 1}:`, err.message);
        }
      }
    }
    
    // Test if tables were created successfully
    console.log('\n🔍 Verifying table creation...');
    
    const tables = ['customers', 'customer_interactions', 'customer_registration_tokens', 
                   'customer_orders', 'customer_chats', 'chat_messages'];
    
    for (const table of tables) {
      try {
        const { error } = await supabaseAdmin.from(table).select('count', { count: 'exact', head: true });
        if (error) {
          console.log(`❌ Table '${table}' not accessible:`, error.message);
        } else {
          console.log(`✅ Table '${table}' exists and accessible`);
        }
      } catch (err) {
        console.log(`❌ Table '${table}' check failed:`, err.message);
      }
    }
    
    console.log('\n🎉 Customer portal setup completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Start the customer portal: cd customer-portal && npm start');
    console.log('2. Add customer management to client dashboard');
    console.log('3. Test customer registration flow');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

setupCustomerTables();

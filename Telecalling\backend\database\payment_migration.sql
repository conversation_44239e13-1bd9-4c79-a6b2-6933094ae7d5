-- Payment Integration Migration
-- Run this file to add payment functionality to existing database
-- Safe to run multiple times - uses IF NOT EXISTS and ALTER TABLE IF NOT EXISTS

-- 1. Create Subscription Plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VA<PERSON>HAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    billing_period VARCHAR(20) DEFAULT 'monthly', -- monthly, yearly
    features JSONB DEFAULT '{}'::jsonb,
    max_calls INTEGER,
    max_phone_numbers INTEGER DEFAULT 1,
    max_products INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create Client Subscriptions table
CREATE TABLE IF NOT EXISTS client_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    razorpay_subscription_id VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired, pending
    starts_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ends_at TIMESTAMP WITH TIME ZONE,
    auto_renew BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create Payment Transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES client_subscriptions(id),
    razorpay_order_id VARCHAR(255) NOT NULL,
    razorpay_payment_id VARCHAR(255),
    razorpay_signature VARCHAR(500),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(20) DEFAULT 'created', -- created, paid, failed, cancelled
    payment_method VARCHAR(50),
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Add payment-related columns to existing clients table (safe ALTER commands)
DO $$ 
BEGIN
    -- Add current_plan_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'clients' AND column_name = 'current_plan_id') THEN
        ALTER TABLE clients ADD COLUMN current_plan_id UUID REFERENCES subscription_plans(id);
    END IF;
    
    -- Add subscription_status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'clients' AND column_name = 'subscription_status') THEN
        ALTER TABLE clients ADD COLUMN subscription_status VARCHAR(20) DEFAULT 'free';
    END IF;
    
    -- Add subscription_ends_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'clients' AND column_name = 'subscription_ends_at') THEN
        ALTER TABLE clients ADD COLUMN subscription_ends_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- 5. Insert default subscription plans (only if they don't exist)
INSERT INTO subscription_plans (name, display_name, description, price, features, max_calls, max_phone_numbers, max_products, sort_order) 
VALUES
    ('free', 'Free', 'Basic features for testing', 0.00, '{"support": "email", "analytics": "basic"}', 10, 1, 5, 0),
    ('basic', 'Basic', 'Perfect for small businesses starting with voice automation', 499.00, '{"support": "email", "analytics": "basic", "whatsapp": "basic"}', 100, 1, 10, 1),
    ('pro', 'Pro', 'Ideal for growing businesses with higher call volumes', 999.00, '{"support": "priority", "analytics": "advanced", "whatsapp": "advanced", "appointments": true, "invoices": true, "payment_links": true}', 500, 1, 50, 2),
    ('business', 'Business', 'For established businesses needing comprehensive automation', 1999.00, '{"support": "dedicated", "analytics": "advanced", "whatsapp": "full", "appointments": true, "invoices": true, "payment_links": true, "api_access": true, "custom_responses": true}', 1500, 2, -1, 3),
    ('enterprise', 'Enterprise', 'Tailored solutions for large-scale operations', 0.00, '{"support": "24x7", "analytics": "enterprise", "whatsapp": "white_label", "appointments": true, "invoices": true, "payment_links": true, "api_access": true, "custom_responses": true, "multi_location": true}', -1, -1, -1, 4)
ON CONFLICT (name) DO NOTHING;

-- 6. Create indexes for better performance (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_client_subscriptions_client_id ON client_subscriptions(client_id);
CREATE INDEX IF NOT EXISTS idx_client_subscriptions_status ON client_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_client_id ON payment_transactions(client_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_razorpay_order_id ON payment_transactions(razorpay_order_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_clients_current_plan_id ON clients(current_plan_id);

-- 7. Enable Row Level Security for new tables
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS Policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Anyone can view subscription plans" ON subscription_plans;
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON client_subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON client_subscriptions;
DROP POLICY IF EXISTS "Users can view their own payment transactions" ON payment_transactions;
DROP POLICY IF EXISTS "Users can insert their own payment transactions" ON payment_transactions;

-- RLS Policies for subscription_plans (public read access)
CREATE POLICY "Anyone can view subscription plans" ON subscription_plans
    FOR SELECT USING (is_active = true);

-- RLS Policies for client_subscriptions
CREATE POLICY "Users can view their own subscriptions" ON client_subscriptions
    FOR SELECT USING (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

CREATE POLICY "Users can update their own subscriptions" ON client_subscriptions
    FOR UPDATE USING (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

-- RLS Policies for payment_transactions
CREATE POLICY "Users can view their own payment transactions" ON payment_transactions
    FOR SELECT USING (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

CREATE POLICY "Users can insert their own payment transactions" ON payment_transactions
    FOR INSERT WITH CHECK (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

-- 9. Grant necessary permissions for new tables
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON subscription_plans TO anon, authenticated;
GRANT ALL ON client_subscriptions TO anon, authenticated;
GRANT ALL ON payment_transactions TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- 10. Update existing clients to have free plan (only if they don't have a plan)
DO $$
DECLARE
    free_plan_id UUID;
BEGIN
    -- Get the free plan ID
    SELECT id INTO free_plan_id FROM subscription_plans WHERE name = 'free';
    
    -- Update clients without a current plan
    IF free_plan_id IS NOT NULL THEN
        UPDATE clients 
        SET current_plan_id = free_plan_id,
            subscription_status = 'free'
        WHERE current_plan_id IS NULL;
    END IF;
END $$;

-- Success message
SELECT 'Payment integration migration completed successfully!' as status,
       'Tables created: subscription_plans, client_subscriptions, payment_transactions' as tables_created,
       'Columns added to clients: current_plan_id, subscription_status, subscription_ends_at' as columns_added,
       (SELECT COUNT(*) FROM subscription_plans) as plans_created;

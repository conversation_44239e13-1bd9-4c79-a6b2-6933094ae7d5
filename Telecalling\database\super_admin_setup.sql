-- =====================================================
-- SUPER ADMIN SETUP SQL QUERIES
-- =====================================================
-- Execute these queries in Supabase SQL Editor to set up super admin functionality

-- 1. Add role column to existing admins table (if not already added)
ALTER TABLE admins ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin'));

-- 2. Update existing admins to have 'admin' role
UPDATE admins SET role = 'admin' WHERE role IS NULL;

-- 3. Create super admin account
-- Password: superadmin123 (simple password, no hashing)
INSERT INTO admins (admin_id, password_hash, name, email, role)
VALUES (
    'superadmin001',
    'superadmin123', -- Simple password, no bcrypt hashing
    'Super Administrator',
    '<EMAIL>',
    'super_admin'
) ON CONFLICT (admin_id) DO NOTHING;

-- 4. Create admin_assignments table for super admin to manage admin-client relationships
CREATE TABLE IF NOT EXISTS admin_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    super_admin_id UUID REFERENCES admins(id) ON DELETE CASCADE,
    admin_id UUID REFERENCES admins(id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by VARCHAR(50), -- admin_id of who made the assignment
    notes TEXT,
    UNIQUE(admin_id, client_id) -- Each admin-client pair can only exist once
);

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_admin_assignments_super_admin ON admin_assignments(super_admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_assignments_admin ON admin_assignments(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_assignments_client ON admin_assignments(client_id);
CREATE INDEX IF NOT EXISTS idx_admins_role ON admins(role);

-- 6. Create view for super admin dashboard
CREATE OR REPLACE VIEW super_admin_dashboard AS
SELECT 
    (SELECT COUNT(*) FROM admins WHERE role = 'admin' AND is_active = true) as total_admins,
    (SELECT COUNT(*) FROM clients WHERE is_active = true) as total_clients,
    (SELECT COUNT(*) FROM admin_client_assignments) as total_assignments,
    (SELECT COUNT(*) FROM clients WHERE assigned_plivo_number IS NOT NULL) as clients_with_numbers,
    (SELECT COUNT(*) FROM support_tickets WHERE status = 'open') as open_tickets,
    (SELECT COUNT(*) FROM call_logs WHERE DATE(call_timestamp) = CURRENT_DATE) as today_calls,
    (SELECT COUNT(*) FROM sms_logs WHERE DATE(sms_timestamp) = CURRENT_DATE) as today_sms;

-- 7. Function to get admin workload (number of assigned clients)
CREATE OR REPLACE FUNCTION get_admin_workload()
RETURNS TABLE (
    admin_id UUID,
    admin_name VARCHAR(255),
    admin_email VARCHAR(255),
    assigned_clients_count BIGINT,
    available_slots INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        a.name,
        a.email,
        COALESCE(ac.client_count, 0) as assigned_clients_count,
        (250 - COALESCE(ac.client_count, 0))::INTEGER as available_slots
    FROM admins a
    LEFT JOIN (
        SELECT 
            admin_id, 
            COUNT(*) as client_count
        FROM admin_client_assignments 
        GROUP BY admin_id
    ) ac ON a.id = ac.admin_id
    WHERE a.role = 'admin' AND a.is_active = true
    ORDER BY assigned_clients_count ASC;
END;
$$ LANGUAGE plpgsql;

-- 8. Function to get unassigned clients
CREATE OR REPLACE FUNCTION get_unassigned_clients()
RETURNS TABLE (
    client_id UUID,
    username VARCHAR(255),
    email VARCHAR(255),
    business_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.username,
        c.email,
        c.business_name,
        c.created_at
    FROM clients c
    LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
    WHERE aca.client_id IS NULL AND c.is_active = true
    ORDER BY c.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 9. Function to bulk assign clients to admin
CREATE OR REPLACE FUNCTION bulk_assign_clients_to_admin(
    p_admin_id UUID,
    p_client_ids UUID[],
    p_assigned_by VARCHAR(50)
)
RETURNS TABLE (
    success BOOLEAN,
    message TEXT,
    assigned_count INTEGER
) AS $$
DECLARE
    v_admin_exists BOOLEAN;
    v_current_assignments INTEGER;
    v_available_slots INTEGER;
    v_clients_to_assign INTEGER;
    v_assigned_count INTEGER := 0;
    client_id UUID;
BEGIN
    -- Check if admin exists and is active
    SELECT EXISTS(SELECT 1 FROM admins WHERE id = p_admin_id AND role = 'admin' AND is_active = true)
    INTO v_admin_exists;
    
    IF NOT v_admin_exists THEN
        RETURN QUERY SELECT false, 'Admin not found or inactive', 0;
        RETURN;
    END IF;
    
    -- Get current assignments count
    SELECT COUNT(*) INTO v_current_assignments
    FROM admin_client_assignments 
    WHERE admin_id = p_admin_id;
    
    v_available_slots := 250 - v_current_assignments;
    v_clients_to_assign := array_length(p_client_ids, 1);
    
    -- Check if admin has enough slots
    IF v_clients_to_assign > v_available_slots THEN
        RETURN QUERY SELECT false, 
            format('Admin only has %s available slots but trying to assign %s clients', 
                   v_available_slots, v_clients_to_assign), 0;
        RETURN;
    END IF;
    
    -- Assign clients
    FOREACH client_id IN ARRAY p_client_ids
    LOOP
        -- Check if client exists and is not already assigned
        IF EXISTS(SELECT 1 FROM clients WHERE id = client_id AND is_active = true) AND
           NOT EXISTS(SELECT 1 FROM admin_client_assignments WHERE client_id = client_id) THEN
            
            INSERT INTO admin_client_assignments (admin_id, client_id, assigned_at)
            VALUES (p_admin_id, client_id, NOW());
            
            v_assigned_count := v_assigned_count + 1;
        END IF;
    END LOOP;
    
    RETURN QUERY SELECT true, 
        format('Successfully assigned %s clients to admin', v_assigned_count), 
        v_assigned_count;
END;
$$ LANGUAGE plpgsql;

-- 10. Create audit log for super admin actions
CREATE TABLE IF NOT EXISTS super_admin_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    super_admin_id UUID REFERENCES admins(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'assign_clients', 'create_admin', 'deactivate_admin', etc.
    target_admin_id UUID REFERENCES admins(id) ON DELETE SET NULL,
    target_client_ids UUID[],
    action_details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_super_admin_audit_log_super_admin ON super_admin_audit_log(super_admin_id);
CREATE INDEX IF NOT EXISTS idx_super_admin_audit_log_action_type ON super_admin_audit_log(action_type);
CREATE INDEX IF NOT EXISTS idx_super_admin_audit_log_created_at ON super_admin_audit_log(created_at);

-- Success message
SELECT 'Super Admin setup completed successfully!' as status,
       'Default super admin credentials: superadmin001 / superadmin123' as credentials,
       'Please change the default password in production!' as warning;

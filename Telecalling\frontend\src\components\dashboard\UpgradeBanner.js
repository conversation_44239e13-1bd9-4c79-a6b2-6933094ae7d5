import React, { useState, useEffect } from 'react';
import { X, Crown, TrendingUp, CheckCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import subscriptionAPI from '../../services/subscriptionAPI';

const UpgradeBanner = () => {
  const [subscription, setSubscription] = useState(null);
  const [isVisible, setIsVisible] = useState(true);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSubscription();
  }, []);

  const fetchSubscription = async () => {
    try {
      const response = await subscriptionAPI.getCurrentSubscription();
      if (response.success) {
        setSubscription(response.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  // Don't show banner if user has active subscription or if dismissed
  if (loading || !isVisible || (subscription && subscription.status === 'active')) {
    return null;
  }

  const currentPlan = subscription?.subscription_plans;
  const isFreePlan = !subscription || currentPlan?.name === 'free';

  // Only show for free users
  if (!isFreePlan) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-6 mb-6 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white rounded-full"></div>
        <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-white rounded-full"></div>
      </div>

      {/* Close Button */}
      <button
        onClick={() => setIsVisible(false)}
        className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors"
      >
        <X className="w-5 h-5" />
      </button>

      <div className="relative">
        <div className="flex items-start space-x-4">
          {/* Icon */}
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
              <Crown className="w-6 h-6 text-white" />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1">
            <h3 className="text-xl font-bold mb-2">
              🚀 Unlock the Full Power of VoiceBot!
            </h3>
            <p className="text-blue-100 mb-4">
              You're currently on the Free plan. Upgrade to access advanced features and scale your business.
            </p>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-300" />
                <span className="text-sm">More calls per month</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-300" />
                <span className="text-sm">Advanced analytics</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-300" />
                <span className="text-sm">Priority support</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-300" />
                <span className="text-sm">WhatsApp integration</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <Link
                to="/dashboard/payments"
                className="inline-flex items-center justify-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                View Plans & Pricing
              </Link>
              <Link
                to="/dashboard/payments"
                className="inline-flex items-center justify-center px-6 py-3 bg-transparent border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
              >
                <Crown className="w-4 h-4 mr-2" />
                Upgrade to Pro
              </Link>
            </div>
          </div>
        </div>

        {/* Special Offer Badge */}
        <div className="absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold transform rotate-12">
          Limited Time!
        </div>
      </div>
    </div>
  );
};

export default UpgradeBanner;

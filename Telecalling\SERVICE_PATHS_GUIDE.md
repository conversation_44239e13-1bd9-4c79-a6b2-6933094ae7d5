# 🌐 Complete Service Paths Guide

## 🚀 **Server Overview**

### **Client Server (Port 5000)**
- **URL**: `http://localhost:5000`
- **Purpose**: Client-facing APIs for dashboard, authentication, and services
- **Frontend**: `http://localhost:3000` (React app)

### **Admin Panel Server (Port 4000)**
- **URL**: `http://localhost:4000`
- **Purpose**: Admin-only APIs for client management and support
- **Frontend**: `http://localhost:3001` (To be created)

---

## 🔍 **Service Discovery Endpoints**

### **Client Server Root**
```
GET http://localhost:5000/
```
**Response**: Complete list of all available client API routes

### **Admin Panel Root**
```
GET http://localhost:4000/
```
**Response**: Complete list of all available admin API routes

### **Health Check Endpoints**
```
GET http://localhost:5000/health
GET http://localhost:4000/health
```

---

## 🔐 **Client Server APIs (Port 5000)**

### **Authentication Services**
```
POST http://localhost:5000/api/auth/signup
POST http://localhost:5000/api/auth/login
POST http://localhost:5000/api/auth/google
POST http://localhost:5000/api/auth/forgot-password
POST http://localhost:5000/api/auth/reset-password
```

### **Profile Management**
```
GET    http://localhost:5000/api/profile
PUT    http://localhost:5000/api/profile
POST   http://localhost:5000/api/profile/reset-password
```

### **Product Catalog**
```
GET    http://localhost:5000/api/products
GET    http://localhost:5000/api/products/:id
POST   http://localhost:5000/api/products
PUT    http://localhost:5000/api/products/:id
DELETE http://localhost:5000/api/products/:id
POST   http://localhost:5000/api/products/bulk-upload
```

### **Analytics & Logs**
```
GET http://localhost:5000/api/analytics/call-logs
GET http://localhost:5000/api/analytics/sms-logs
GET http://localhost:5000/api/analytics/usage-stats
GET http://localhost:5000/api/analytics/dashboard-overview
```

### **Support System**
```
GET  http://localhost:5000/api/support
POST http://localhost:5000/api/support
GET  http://localhost:5000/api/support/:id
```

---

## 🔧 **Admin Panel APIs (Port 4000)**

### **Admin Authentication**
```
POST http://localhost:4000/api/admin/auth/login
POST http://localhost:4000/api/admin/auth/logout
POST http://localhost:4000/api/admin/auth/refresh
GET  http://localhost:4000/api/admin/auth/profile
```

### **Client Management**
```
GET    http://localhost:4000/api/admin/panel/dashboard
GET    http://localhost:4000/api/admin/panel/clients
GET    http://localhost:4000/api/admin/panel/clients/:id
PUT    http://localhost:4000/api/admin/panel/clients/:id/assign-number
DELETE http://localhost:4000/api/admin/panel/clients/:id/remove-number
```

### **Support Management**
```
GET http://localhost:4000/api/admin/support/tickets
GET http://localhost:4000/api/admin/support/tickets/:id
PUT http://localhost:4000/api/admin/support/tickets/:id/respond
GET http://localhost:4000/api/admin/support/stats
```

---

## 🧪 **Testing the Services**

### **1. Test Client Server**
```bash
# Check if server is running
curl http://localhost:5000/

# Test health
curl http://localhost:5000/health

# Test client login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### **2. Test Admin Panel**
```bash
# Check if admin server is running
curl http://localhost:4000/

# Test health
curl http://localhost:4000/health

# Test admin login
curl -X POST http://localhost:4000/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{"adminId": "admin001", "password": "admin123"}'
```

---

## 🎯 **Frontend Access Points**

### **Client Dashboard**
- **URL**: `http://localhost:3000`
- **Login Page**: `http://localhost:3000/`
- **Dashboard**: `http://localhost:3000/dashboard/plan499` (after login)
- **Features**:
  - Profile management
  - Product catalog
  - Call/SMS logs
  - Usage analytics
  - Support tickets

### **Admin Panel Frontend** (To be created)
- **URL**: `http://localhost:3001`
- **Login**: Admin ID/Password authentication
- **Features**:
  - Client management (max 250 per admin)
  - Plivo number assignment
  - Support ticket management
  - Analytics dashboard

---

## 📊 **Service Features by Endpoint**

### **Client Profile Service** (`/api/profile`)
- ✅ Business information management
- ✅ Contact details (mobile, email, WhatsApp)
- ✅ Password reset functionality
- ✅ Assigned phone number display

### **Product Catalog Service** (`/api/products`)
- ✅ JSONB-based flexible product details
- ✅ Alias names for local language support
- ✅ File upload for bulk import (AI-ready)
- ✅ CRUD operations with validation

### **Analytics Service** (`/api/analytics`)
- ✅ Call logs with pagination and filters
- ✅ SMS logs with content tracking
- ✅ Usage statistics for billing
- ✅ Dashboard overview with real-time data

### **Support Service** (`/api/support`)
- ✅ Ticket creation with urgency levels
- ✅ Email <NAME_EMAIL>
- ✅ Status tracking (open, in-progress, resolved)
- ✅ Client-admin communication

### **Admin Panel Services** (`/api/admin/*`)
- ✅ Client management (max 250 per admin)
- ✅ Plivo number assignment/removal
- ✅ Support ticket management
- ✅ Usage analytics for assigned clients

---

## 🔒 **Authentication Requirements**

### **Client APIs** (Port 5000)
```bash
# Add to request headers
Authorization: Bearer YOUR_CLIENT_JWT_TOKEN
```

### **Admin APIs** (Port 4000)
```bash
# Add to request headers
Authorization: Bearer YOUR_ADMIN_JWT_TOKEN
```

### **Public Endpoints** (No auth required)
- `GET /` (both servers)
- `GET /health` (both servers)
- `POST /api/auth/*` (client server)
- `POST /api/admin/auth/login` (admin server)

---

## 🚀 **Quick Start Commands**

### **Start Both Servers**
```bash
# Terminal 1: Client Server
cd Telecalling/backend
npm start

# Terminal 2: Admin Panel
cd Telecalling/admin-panel
npm start

# Terminal 3: Frontend
cd Telecalling/frontend
npm start
```

### **Test All Services**
```bash
# Test client server
curl http://localhost:5000/

# Test admin panel
curl http://localhost:4000/

# Test frontend
open http://localhost:3000
```

---

## 📱 **Service Status Dashboard**

Visit these URLs in your browser to see all available services:

1. **Client API Overview**: `http://localhost:5000/`
2. **Admin Panel Overview**: `http://localhost:4000/`
3. **Client Dashboard**: `http://localhost:3000/`
4. **Health Checks**: 
   - `http://localhost:5000/health`
   - `http://localhost:4000/health`

---

## 🔍 **Troubleshooting Service Access**

### **Common Issues:**

1. **Port already in use**
   ```bash
   # Kill processes on ports
   lsof -ti:5000 | xargs kill -9
   lsof -ti:4000 | xargs kill -9
   lsof -ti:3000 | xargs kill -9
   ```

2. **CORS errors**
   - Client server allows `http://localhost:3000`
   - Admin server allows `http://localhost:3001`

3. **Database connection**
   - Check Supabase credentials in `.env` files
   - Ensure schema is properly executed

4. **Authentication errors**
   - Verify JWT secrets are set
   - Check token expiry times

---

## ✅ **Service Verification Checklist**

- [ ] Client server responds at `http://localhost:5000/`
- [ ] Admin panel responds at `http://localhost:4000/`
- [ ] Frontend loads at `http://localhost:3000/`
- [ ] Health checks pass for both servers
- [ ] Admin login works with `admin001/admin123`
- [ ] Client authentication works
- [ ] Database connections are successful
- [ ] All API endpoints return proper responses

All services are now properly configured and accessible! 🎉

const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireClient } = require('../middleware/auth');

const router = express.Router();

// Get all payments for a client
router.get('/', requireClient, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, status, startDate, endDate } = req.query;
  const offset = (page - 1) * limit;

  // Get client ID (req.user.id is already the client ID)
  const clientId = req.user.id;

  // Verify client exists
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('id', clientId)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  let query = supabaseAdmin
    .from('payments')
    .select('*')
    .eq('client_id', clientId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (status) {
    query = query.eq('status', status);
  }

  if (startDate) {
    query = query.gte('created_at', new Date(startDate).toISOString());
  }

  if (endDate) {
    query = query.lte('created_at', new Date(endDate).toISOString());
  }

  const { data: payments, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('payments')
    .select('*', { count: 'exact', head: true })
    .eq('client_id', clientId);

  if (status) {
    countQuery = countQuery.eq('status', status);
  }

  if (startDate) {
    countQuery = countQuery.gte('created_at', new Date(startDate).toISOString());
  }

  if (endDate) {
    countQuery = countQuery.lte('created_at', new Date(endDate).toISOString());
  }

  const { count } = await countQuery;

  res.json({
    payments: payments || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Create payment link
router.post('/create-link', requireClient, asyncHandler(async (req, res) => {
  const {
    toNumber,
    amount,
    description,
    customerName,
    customerEmail,
    expiryHours = 24
  } = req.body;

  if (!toNumber || !amount) {
    throw new AppError('Customer number and amount are required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('*')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  try {
    // Generate payment link via Razorpay or other payment gateway
    const paymentLink = await generatePaymentLink({
      amount: parseFloat(amount),
      description,
      customerName,
      customerEmail,
      expiryHours
    });

    // Store payment record
    const { data: payment, error } = await supabaseAdmin
      .from('payments')
      .insert({
        id: uuidv4(),
        client_id: clientId,
        to_number: toNumber,
        amount: parseFloat(amount),
        description: description || null,
        customer_name: customerName || null,
        customer_email: customerEmail || null,
        payment_link: paymentLink.url,
        payment_gateway_id: paymentLink.id,
        status: 'pending',
        expires_at: new Date(Date.now() + expiryHours * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      message: 'Payment link created successfully',
      payment,
      paymentLink: paymentLink.url
    });
  } catch (error) {
    throw new AppError('Failed to create payment link: ' + error.message, 500);
  }
}));

// Get single payment
router.get('/:paymentId', requireClient, asyncHandler(async (req, res) => {
  const { paymentId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: payment, error } = await supabaseAdmin
    .from('payments')
    .select('*')
    .eq('id', paymentId)
    .eq('client_id', clientId)
    .single();

  if (error) {
    throw error;
  }

  if (!payment) {
    throw new AppError('Payment not found', 404);
  }

  res.json({
    payment
  });
}));

// Update payment status
router.put('/:paymentId/status', requireClient, asyncHandler(async (req, res) => {
  const { paymentId } = req.params;
  const { status, transactionId, paidAmount, paymentMethod } = req.body;

  if (!['pending', 'paid', 'failed', 'expired'].includes(status)) {
    throw new AppError('Invalid status', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const updateData = { status };
  
  if (status === 'paid') {
    updateData.paid_at = new Date().toISOString();
    if (transactionId) updateData.transaction_id = transactionId;
    if (paidAmount) updateData.paid_amount = parseFloat(paidAmount);
    if (paymentMethod) updateData.payment_method = paymentMethod;
  }

  const { data: updatedPayment, error } = await supabaseAdmin
    .from('payments')
    .update(updateData)
    .eq('id', paymentId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!updatedPayment) {
    throw new AppError('Payment not found', 404);
  }

  res.json({
    message: 'Payment status updated successfully',
    payment: updatedPayment
  });
}));

// Razorpay webhook (for payment status updates)
router.post('/razorpay-webhook', asyncHandler(async (req, res) => {
  const { event, payload } = req.body;

  if (event === 'payment_link.paid') {
    const paymentLinkId = payload.payment_link.entity.id;
    const paymentId = payload.payment.entity.id;
    const amount = payload.payment.entity.amount / 100; // Convert from paise to rupees

    // Update payment status in database
    await supabaseAdmin
      .from('payments')
      .update({
        status: 'paid',
        paid_at: new Date().toISOString(),
        transaction_id: paymentId,
        paid_amount: amount,
        payment_method: payload.payment.entity.method
      })
      .eq('payment_gateway_id', paymentLinkId);
  } else if (event === 'payment_link.expired') {
    const paymentLinkId = payload.payment_link.entity.id;

    // Update payment status to expired
    await supabaseAdmin
      .from('payments')
      .update({
        status: 'expired'
      })
      .eq('payment_gateway_id', paymentLinkId);
  }

  res.status(200).json({ success: true });
}));

// Resend payment link
router.post('/:paymentId/resend', requireClient, asyncHandler(async (req, res) => {
  const { paymentId } = req.params;
  const { method = 'whatsapp' } = req.body;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('*')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Get payment
  const { data: payment, error: paymentError } = await supabaseAdmin
    .from('payments')
    .select('*')
    .eq('id', paymentId)
    .eq('client_id', client.id)
    .single();

  if (paymentError || !payment) {
    throw new AppError('Payment not found', 404);
  }

  if (payment.status !== 'pending') {
    throw new AppError('Can only resend pending payments', 400);
  }

  try {
    if (method === 'whatsapp') {
      // Send payment link via WhatsApp
      const message = formatPaymentMessage(payment, client);
      // Implementation would call WhatsApp API
    } else if (method === 'email' && payment.customer_email) {
      // Send payment link via email
      // Implementation would call email service
    }

    res.json({
      message: `Payment link resent successfully via ${method}`
    });
  } catch (error) {
    throw new AppError('Failed to resend payment link: ' + error.message, 500);
  }
}));

// Cancel payment
router.post('/:paymentId/cancel', requireClient, asyncHandler(async (req, res) => {
  const { paymentId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: cancelledPayment, error } = await supabaseAdmin
    .from('payments')
    .update({
      status: 'cancelled',
      cancelled_at: new Date().toISOString()
    })
    .eq('id', paymentId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!cancelledPayment) {
    throw new AppError('Payment not found', 404);
  }

  res.json({
    message: 'Payment cancelled successfully',
    payment: cancelledPayment
  });
}));

// Get payment statistics
router.get('/stats/overview', requireClient, asyncHandler(async (req, res) => {
  const { period = '30d' } = req.query;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(startDate.getDate() - 90);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }

  const { data: payments, error } = await supabaseAdmin
    .from('payments')
    .select('*')
    .eq('client_id', client.id)
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString());

  if (error) {
    throw error;
  }

  const paymentList = payments || [];

  // Calculate statistics
  const totalPayments = paymentList.length;
  const successfulPayments = paymentList.filter(p => p.status === 'paid').length;
  const pendingPayments = paymentList.filter(p => p.status === 'pending').length;
  const failedPayments = paymentList.filter(p => p.status === 'failed').length;
  const totalRevenue = paymentList
    .filter(p => p.status === 'paid')
    .reduce((sum, p) => sum + (p.paid_amount || p.amount), 0);
  const pendingAmount = paymentList
    .filter(p => p.status === 'pending')
    .reduce((sum, p) => sum + p.amount, 0);

  res.json({
    stats: {
      totalPayments,
      successfulPayments,
      pendingPayments,
      failedPayments,
      totalRevenue,
      pendingAmount,
      successRate: totalPayments > 0 ? (successfulPayments / totalPayments * 100).toFixed(1) : 0
    }
  });
}));

// Helper function to generate payment link
async function generatePaymentLink({ amount, description, customerName, customerEmail, expiryHours }) {
  // This is a mock implementation
  // In production, you would integrate with Razorpay, Stripe, or other payment gateways
  
  const paymentLinkId = 'plink_' + Math.random().toString(36).substr(2, 9);
  const paymentUrl = `https://razorpay.com/payment-link/${paymentLinkId}`;
  
  return {
    id: paymentLinkId,
    url: paymentUrl,
    amount,
    description,
    customerName,
    customerEmail,
    expiryHours
  };
}

// Helper function to format payment message
function formatPaymentMessage(payment, client) {
  let message = `💳 *Payment Request from ${client.shop_name}*\n\n`;
  message += `Amount: ₹${payment.amount}\n`;
  
  if (payment.description) {
    message += `Description: ${payment.description}\n`;
  }
  
  if (payment.customer_name) {
    message += `Customer: ${payment.customer_name}\n`;
  }
  
  message += `\n🔗 Pay now: ${payment.payment_link}\n\n`;
  
  if (payment.expires_at) {
    message += `⏰ Link expires: ${new Date(payment.expires_at).toLocaleString()}\n\n`;
  }
  
  message += `Thank you for your business!`;
  
  return message;
}

module.exports = router; 
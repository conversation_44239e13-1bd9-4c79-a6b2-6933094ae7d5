# Bot Integration Developer Guide

This comprehensive guide provides all the necessary information for bot developers to integrate with the Telecalling system database and services.

## Table of Contents
1. [Database Schema Overview](#database-schema-overview)
2. [Product Access](#product-access)
3. [Call Logs Storage](#call-logs-storage)
4. [SMS Logs & PDF Storage](#sms-logs--pdf-storage)
5. [Subscription Status Checking](#subscription-status-checking)
6. [Usage Tracking](#usage-tracking)
7. [API Endpoints](#api-endpoints)
8. [Code Examples](#code-examples)

## Database Schema Overview

### Key Tables for Bot Integration:
- `clients` - Client information and Plivo number assignments
- `products` - Product catalog with JSONB data structure
- `call_logs` - Call records and transcripts
- `sms_logs` - SMS records with PDF attachments
- `sms_pdf_storage` - PDF file storage bucket
- `client_subscriptions` - Active subscription status
- `subscription_plans` - Plan details and limits
- `client_usage_tracking` - Monthly usage tracking

## Product Access

### Fetching Client Products
```sql
-- Get all products for a client by Plivo number
SELECT p.id, p.product_data, p.created_at
FROM products p
JOIN clients c ON p.client_id = c.id
WHERE c.plivo_number = '+1234567890'
AND c.status = 'active';

-- Product data structure (JSONB):
{
  "name": "Product Name",
  "price": 299.99,
  "category": "Electronics",
  "description": "Product description",
  "alias_names": ["Alias 1", "Alias 2"],
  "custom_field_1": "value1",
  "custom_field_2": "value2"
}
```

### Get Client ID from Phone Number
```sql
SELECT id, shop_name, business_type, status
FROM clients 
WHERE plivo_number = '+1234567890';
```

## Call Logs Storage

### Storing Call Data
```sql
-- Insert call log
INSERT INTO call_logs (
    client_id,
    caller_number,
    call_time,
    duration,
    summary,
    transcript_url,
    was_estimate_sent,
    was_payment_sent,
    call_status,
    plivo_call_uuid
) VALUES (
    $1, -- client_id (UUID)
    $2, -- caller_number (TEXT)
    NOW(), -- call_time
    $3, -- duration in seconds (INTEGER)
    $4, -- summary (TEXT)
    $5, -- transcript_url (TEXT, optional)
    $6, -- was_estimate_sent (BOOLEAN)
    $7, -- was_payment_sent (BOOLEAN)
    'completed', -- call_status
    $8  -- plivo_call_uuid (TEXT)
) RETURNING id;
```

### Call Status Options:
- `completed` - Call completed successfully
- `failed` - Call failed
- `missed` - Call was missed
- `busy` - Line was busy
- `no_answer` - No answer

## SMS Logs & PDF Storage

### Storing SMS with PDF Attachments
```sql
-- 1. Insert SMS log
INSERT INTO sms_logs (
    client_id,
    recipient_number,
    message_content,
    message_summary,
    status
) VALUES (
    $1, -- client_id
    $2, -- recipient_number
    $3, -- message_content
    $4, -- message_summary
    'sent'
) RETURNING id;

-- 2. Store PDF attachment using the storage bucket function
SELECT add_pdf_to_sms_storage_bucket(
    $1, -- sms_log_id
    $2, -- client_id
    $3, -- file_name
    $4, -- file_size (bytes)
    $5  -- storage_url (bucket path auto-generated)
);
```

### PDF Storage Bucket Structure
- **Bucket Name**: `sms-pdfs`
- **Storage Path**: `sms-pdfs/{client_id}/{sms_log_id}/{filename}`
- **Public URL**: `https://your-storage-domain.com/sms-pdfs/{client_id}/{sms_log_id}/{filename}`
- **File Types**: PDF files only
- **Max Size**: 10MB per file
- **Organization**: Files organized by client and SMS log for easy management

### Retrieving SMS Logs with PDFs
```sql
-- Get SMS logs with PDF attachments
SELECT * FROM sms_logs_with_pdfs 
WHERE client_id = $1 
ORDER BY sms_timestamp DESC 
LIMIT 50;
```

## Subscription Status Checking

### Check Active Subscription
```sql
-- Check if client has active subscription
SELECT 
    c.id,
    c.shop_name,
    cs.status as subscription_status,
    cs.starts_at,
    cs.ends_at,
    sp.name as plan_name,
    sp.max_calls,
    sp.features
FROM clients c
LEFT JOIN client_subscriptions cs ON c.id = cs.client_id 
    AND cs.status = 'active'
    AND (cs.ends_at IS NULL OR cs.ends_at > NOW())
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
WHERE c.plivo_number = $1;
```

### Subscription Status Values:
- `active` - Subscription is active
- `expired` - Subscription has expired
- `cancelled` - Subscription was cancelled
- `pending` - Payment pending

### Plan Limits Check
```sql
-- Get current month usage vs limits
SELECT 
    sp.name as plan_name,
    sp.max_calls as call_limit,
    COALESCE(cut.call_minutes_used, 0) as minutes_used,
    COALESCE(cut.sms_count_used, 0) as sms_used,
    -- Calculate remaining limits
    (sp.max_calls - COALESCE(cut.call_minutes_used, 0)) as remaining_minutes
FROM clients c
JOIN client_subscriptions cs ON c.id = cs.client_id AND cs.status = 'active'
JOIN subscription_plans sp ON cs.plan_id = sp.id
LEFT JOIN client_usage_tracking cut ON c.id = cut.client_id 
    AND cut.usage_month = DATE_TRUNC('month', CURRENT_DATE)
WHERE c.plivo_number = $1;
```

## Usage Tracking

### Update Usage After Call/SMS
```sql
-- Update or insert usage tracking
INSERT INTO client_usage_tracking (
    client_id,
    usage_month,
    call_minutes_used,
    sms_count_used
) VALUES (
    $1, -- client_id
    DATE_TRUNC('month', CURRENT_DATE), -- current month
    $2, -- minutes to add
    $3  -- sms count to add
)
ON CONFLICT (client_id, usage_month)
DO UPDATE SET
    call_minutes_used = client_usage_tracking.call_minutes_used + EXCLUDED.call_minutes_used,
    sms_count_used = client_usage_tracking.sms_count_used + EXCLUDED.sms_count_used,
    last_updated = NOW();
```

## API Endpoints

### Base URL: `http://localhost:5000/api`

#### Authentication
All API calls require authentication token in header:
```
Authorization: Bearer <token>
```

#### Key Endpoints for Bot:
- `GET /products` - Get client products
- `POST /analytics/call-logs` - Store call log
- `POST /analytics/sms-logs` - Store SMS log
- `GET /subscriptions/current` - Check subscription status
- `POST /usage/update` - Update usage tracking

## Code Examples

### Node.js Integration Example
```javascript
const { Pool } = require('pg');

class BotDatabaseService {
    constructor(connectionString) {
        this.pool = new Pool({ connectionString });
    }

    // Get client by phone number
    async getClientByPhone(plivoNumber) {
        const query = `
            SELECT id, shop_name, business_type, status
            FROM clients 
            WHERE plivo_number = $1 AND status = 'active'
        `;
        const result = await this.pool.query(query, [plivoNumber]);
        return result.rows[0];
    }

    // Get client products
    async getClientProducts(clientId) {
        const query = `
            SELECT id, product_data, created_at
            FROM products 
            WHERE client_id = $1
            ORDER BY created_at DESC
        `;
        const result = await this.pool.query(query, [clientId]);
        return result.rows;
    }

    // Check subscription status
    async checkSubscriptionStatus(clientId) {
        const query = `
            SELECT 
                cs.status,
                cs.ends_at,
                sp.name as plan_name,
                sp.max_calls,
                sp.features
            FROM client_subscriptions cs
            JOIN subscription_plans sp ON cs.plan_id = sp.id
            WHERE cs.client_id = $1 
            AND cs.status = 'active'
            AND (cs.ends_at IS NULL OR cs.ends_at > NOW())
        `;
        const result = await this.pool.query(query, [clientId]);
        return result.rows[0];
    }

    // Store call log
    async storeCallLog(callData) {
        const query = `
            INSERT INTO call_logs (
                client_id, caller_number, duration, summary,
                transcript_url, was_estimate_sent, call_status, plivo_call_uuid
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
        `;
        const values = [
            callData.clientId,
            callData.callerNumber,
            callData.duration,
            callData.summary,
            callData.transcriptUrl,
            callData.wasEstimateSent,
            callData.status,
            callData.plivoCallUuid
        ];
        const result = await this.pool.query(query, values);
        return result.rows[0].id;
    }

    // Store SMS with PDF
    async storeSmsWithPdf(smsData, pdfData) {
        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');
            
            // Insert SMS log
            const smsQuery = `
                INSERT INTO sms_logs (client_id, recipient_number, message_content, status)
                VALUES ($1, $2, $3, 'sent')
                RETURNING id
            `;
            const smsResult = await client.query(smsQuery, [
                smsData.clientId,
                smsData.recipientNumber,
                smsData.messageContent
            ]);
            const smsLogId = smsResult.rows[0].id;

            // Add PDF if provided
            if (pdfData) {
                const pdfQuery = `
                    SELECT add_pdf_to_sms_storage_bucket($1, $2, $3, $4, $5)
                `;
                await client.query(pdfQuery, [
                    smsLogId,
                    smsData.clientId,
                    pdfData.fileName,
                    pdfData.fileSize,
                    pdfData.storageUrl
                ]);
            }

            await client.query('COMMIT');
            return smsLogId;
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    }

    // Update usage tracking
    async updateUsage(clientId, callMinutes = 0, smsCount = 0) {
        const query = `
            INSERT INTO client_usage_tracking (client_id, usage_month, call_minutes_used, sms_count_used)
            VALUES ($1, DATE_TRUNC('month', CURRENT_DATE), $2, $3)
            ON CONFLICT (client_id, usage_month)
            DO UPDATE SET
                call_minutes_used = client_usage_tracking.call_minutes_used + EXCLUDED.call_minutes_used,
                sms_count_used = client_usage_tracking.sms_count_used + EXCLUDED.sms_count_used,
                last_updated = NOW()
        `;
        await this.pool.query(query, [clientId, callMinutes, smsCount]);
    }
}

module.exports = BotDatabaseService;
```

### Environment Variables Required:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
STORAGE_BUCKET_URL=https://your-storage-domain.com
```

### Error Handling:
- Always check subscription status before processing calls/SMS
- Handle database connection errors gracefully
- Log all bot activities for debugging
- Validate client exists before storing data

### Support:
For technical support, contact: <EMAIL>

# VoiceBot Platform - Telecalling Application

A comprehensive telecalling platform with VoiceBot integration, built with React frontend and Express.js backend.

## 🏗️ Project Structure

```
Telecalling/
├── backend/          # Express.js API server
├── frontend/         # React web application
└── README.md         # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Supabase account and project

### 1. Backend Setup

```bash
cd backend
npm install
cp env.example .env
# Edit .env with your configuration
npm run dev
```

### 2. Frontend Setup

```bash
cd frontend
npm install
npm start
```

### 3. Access the Application

- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Health Check: http://localhost:5000/health

## 📋 Environment Setup

### Backend Environment Variables

Copy `backend/env.example` to `backend/.env` and configure:

**Required Variables:**
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_ANON_KEY` - Supabase anon public key
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key
- `JWT_SECRET` - Secret key for JWT tokens

**Optional Variables:**
- `PORT` - Backend port (default: 5000)
- `PLIVO_AUTH_ID` - For voice calling features
- `PLIVO_AUTH_TOKEN` - For voice calling features
- `WHATSAPP_*` - For WhatsApp integration
- `RAZORPAY_*` - For payment processing

## 🛠️ Development

### Start Development Servers

**Backend (with auto-reload):**
```bash
cd backend
npm run dev
```

**Frontend:**
```bash
cd frontend
npm start
```

### Production Build

**Frontend:**
```bash
cd frontend
npm run build
```

**Backend:**
```bash
cd backend
npm start
```

## 📚 API Documentation

The backend provides the following API endpoints:

- `/api/auth` - Authentication (login, signup, verification)
- `/api/clients` - Client management
- `/api/products` - Product catalog
- `/api/appointments` - Appointment scheduling
- `/api/call-logs` - Call history and logs
- `/api/whatsapp` - WhatsApp integration
- `/api/invoices` - Invoice management
- `/api/payments` - Payment processing
- `/api/admin` - Admin operations
- `/api/support` - Support tickets

## 🔧 Troubleshooting

### Backend Won't Start

1. **Check environment variables:**
   ```bash
   cd backend
   ls -la .env  # Should exist
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Verify Supabase connection:**
   - Check SUPABASE_URL format: `https://your-project.supabase.co`
   - Verify keys in Supabase dashboard

4. **Check port availability:**
   ```bash
   netstat -an | grep 5000  # Should be empty
   ```

### Frontend Issues

1. **Proxy errors:** Ensure backend is running on port 5000
2. **Dependencies:** Run `npm install` in frontend directory
3. **Port conflicts:** Default port 3000 should be available

### Common Issues

- **CORS errors:** Check backend CORS configuration in `server.js`
- **Database errors:** Verify Supabase credentials and table setup
- **Authentication issues:** Check JWT_SECRET configuration

## 🗃️ Database Setup

The project uses Supabase as the database. See `backend/database/schema.sql` for the database schema.

## 📱 Features

- **Authentication:** Login, signup, email verification
- **Client Management:** Add, edit, delete client records
- **Appointment Scheduling:** Calendar integration
- **Voice Calling:** Plivo integration for telecalling
- **WhatsApp Integration:** Business messaging
- **Payment Processing:** Razorpay integration
- **Admin Dashboard:** User and system management
- **Support System:** Ticket management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - See LICENSE file for details 
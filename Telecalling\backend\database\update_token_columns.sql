-- Update token columns to TEXT type to handle longer JWT tokens
-- Run this in Supabase SQL Editor immediately

-- 1. Update session_token column to TEXT type
ALTER TABLE login_sessions 
ALTER COLUMN session_token TYPE TEXT;

-- 2. Update refresh_token column to TEXT type
ALTER TABLE login_sessions 
ALTER COLUMN refresh_token TYPE TEXT;

-- 3. Update password_reset_tokens table if it exists
ALTER TABLE IF EXISTS password_reset_tokens 
ALTER COLUMN token TYPE TEXT;

-- 4. Remove unique constraint and add a new one with a hash
-- First drop the existing constraint
ALTER TABLE login_sessions 
DROP CONSTRAINT IF EXISTS login_sessions_session_token_key;

-- 5. Add a new unique constraint using a hash function
-- This ensures uniqueness while handling longer tokens
ALTER TABLE login_sessions 
ADD CONSTRAINT login_sessions_session_token_key 
UNIQUE (md5(session_token));

-- 6. Clean up any duplicate sessions
DELETE FROM login_sessions
WHERE id IN (
    SELECT id
    FROM (
        SELECT id,
               ROW_NUMBER() OVER (PARTITION BY client_id, session_token ORDER BY created_at DESC) as row_num
        FROM login_sessions
    ) t
    WHERE t.row_num > 1
);

-- Success message
SELECT 'Token columns updated successfully!' as status;

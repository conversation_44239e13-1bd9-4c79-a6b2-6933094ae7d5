require('dotenv').config();
const fetch = require('node-fetch');

async function debugAdminSupport() {
  try {
    console.log('🔍 Debugging Admin Support API...\n');

    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await fetch('http://localhost:4000/api/admin/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        adminId: 'admin001',
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      console.error('❌ Login failed:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful:', {
      adminId: loginData.admin.adminId,
      name: loginData.admin.name,
      role: loginData.admin.role
    });

    const token = loginData.tokens.accessToken;

    // Step 2: Test support tickets API
    console.log('\n2. Fetching support tickets...');
    const ticketsResponse = await fetch('http://localhost:4000/api/admin/support/tickets', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!ticketsResponse.ok) {
      const errorData = await ticketsResponse.json();
      console.error('❌ Failed to fetch tickets:', errorData);
      return;
    }

    const ticketsData = await ticketsResponse.json();
    console.log('✅ Support tickets response:', {
      message: ticketsData.message,
      ticketCount: ticketsData.tickets?.length || 0,
      pagination: ticketsData.pagination
    });

    if (ticketsData.tickets && ticketsData.tickets.length > 0) {
      console.log('\n📋 Tickets found:');
      ticketsData.tickets.forEach((ticket, index) => {
        console.log(`  ${index + 1}. Subject: ${ticket.subject}`);
        console.log(`     Client: ${ticket.clientName} (${ticket.clientEmail})`);
        console.log(`     Status: ${ticket.status}`);
        console.log(`     Priority: ${ticket.urgencyLevel}`);
        console.log(`     Created: ${new Date(ticket.createdAt).toLocaleString()}`);
        console.log('');
      });
    } else {
      console.log('❌ No tickets found for this admin');
    }

    // Step 3: Test support stats API
    console.log('\n3. Fetching support stats...');
    const statsResponse = await fetch('http://localhost:4000/api/admin/support/stats', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ Support stats:', statsData.stats);
    } else {
      const errorData = await statsResponse.json();
      console.error('❌ Failed to fetch stats:', errorData);
    }

    console.log('\n✅ Admin support API debug completed!');

  } catch (error) {
    console.error('❌ Error debugging admin support:', error);
  }
}

debugAdminSupport();

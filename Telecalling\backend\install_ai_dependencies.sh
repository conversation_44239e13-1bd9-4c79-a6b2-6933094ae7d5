#!/bin/bash

echo "🚀 Installing AI Product Upload Dependencies..."

# Core OpenAI and file processing packages
npm install openai@^4.0.0
npm install multer@^1.4.5-lts.1
npm install xlsx@^0.18.5
npm install csv-parser@^3.0.0
npm install mammoth@^1.6.0
npm install pdf-parse@^1.1.1

echo "✅ All dependencies installed successfully!"
echo ""
echo "📋 Installed packages:"
echo "  - openai: OpenAI API integration"
echo "  - multer: File upload handling"
echo "  - xlsx: Excel file processing"
echo "  - csv-parser: CSV file processing"
echo "  - mammoth: Word document processing"
echo "  - pdf-parse: PDF file processing"
echo ""
echo "🔑 Next step: Add your OpenAI API key to .env file:"
echo "   OPENAI_API_KEY=your_openai_api_key_here"

-- SQL script to update admin password to plain text for simple authentication
-- Run this in your Supabase SQL editor or database client

-- First, let's check if the admin exists
SELECT * FROM admins WHERE admin_id = 'admin001';

-- Update the admin password to plain text 'admin123'
-- Since we're not using JWT anymore, we don't need bcrypt hashing
UPDATE admins 
SET password_hash = 'admin123',
    updated_at = NOW()
WHERE admin_id = 'admin001';

-- If the admin doesn't exist, create it
INSERT INTO admins (
    id,
    admin_id,
    name,
    email,
    password_hash,
    is_active,
    created_at,
    updated_at
) 
SELECT 
    gen_random_uuid(),
    'admin001',
    'System Administrator',
    '<EMAIL>',
    'admin123',
    true,
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM admins WHERE admin_id = 'admin001'
);

-- Verify the update
SELECT admin_id, name, email, password_hash, is_active FROM admins WHERE admin_id = 'admin001';

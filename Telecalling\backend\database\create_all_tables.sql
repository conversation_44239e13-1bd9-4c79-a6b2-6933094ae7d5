-- Complete Supabase Database Schema for Telecalling Authentication System
-- Run this in your Supabase SQL Editor

-- 1. Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 2. Clients table with authentication fields
CREATE TABLE IF NOT EXISTS clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supabase_auth_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- Link to Supabase Auth
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255), -- Made optional for Supabase Auth users
    business_name <PERSON><PERSON><PERSON><PERSON>(255),
    username <PERSON><PERSON><PERSON><PERSON>(255),
    phone VARCHAR(20),
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Profile information
    profile_picture_url TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    
    -- Business information
    business_type VARCHAR(100),
    business_address TEXT,
    business_phone VARCHAR(20),
    website_url TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'::jsonb,
    
    CONSTRAINT clients_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- 3. Pending registrations table (users waiting for email verification)
CREATE TABLE IF NOT EXISTS pending_registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    business_name VARCHAR(255),
    username VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT pending_registrations_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- 4. OTP verification table
CREATE TABLE IF NOT EXISTS otp_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    pending_registration_id UUID REFERENCES pending_registrations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    otp_code VARCHAR(6) NOT NULL,
    otp_type VARCHAR(50) NOT NULL, -- 'email_verification', 'password_reset', 'login_2fa'
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT otp_verifications_otp_code_check CHECK (otp_code ~ '^[0-9]{6}$')
);

-- 5. Password reset tokens table (legacy support)
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Login sessions table (for session management)
CREATE TABLE IF NOT EXISTS login_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE
);

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_supabase_auth_id ON clients(supabase_auth_id);
CREATE INDEX IF NOT EXISTS idx_clients_is_active ON clients(is_active);
CREATE INDEX IF NOT EXISTS idx_clients_is_email_verified ON clients(is_email_verified);

CREATE INDEX IF NOT EXISTS idx_pending_registrations_email ON pending_registrations(email);
CREATE INDEX IF NOT EXISTS idx_pending_registrations_expires_at ON pending_registrations(expires_at);

CREATE INDEX IF NOT EXISTS idx_otp_verifications_email ON otp_verifications(email);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_otp_code ON otp_verifications(otp_code);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_expires_at ON otp_verifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_client_id ON otp_verifications(client_id);
CREATE INDEX IF NOT EXISTS idx_otp_verifications_pending_registration_id ON otp_verifications(pending_registration_id);

CREATE INDEX IF NOT EXISTS idx_login_sessions_client_id ON login_sessions(client_id);
CREATE INDEX IF NOT EXISTS idx_login_sessions_session_token ON login_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_login_sessions_refresh_token ON login_sessions(refresh_token);
CREATE INDEX IF NOT EXISTS idx_login_sessions_expires_at ON login_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_login_sessions_is_active ON login_sessions(is_active);

-- 8. Create triggers for updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Cleanup function for expired records
CREATE OR REPLACE FUNCTION cleanup_expired_records()
RETURNS void AS $$
BEGIN
    -- Clean up expired pending registrations
    DELETE FROM pending_registrations WHERE expires_at < NOW();
    
    -- Clean up expired OTP verifications
    DELETE FROM otp_verifications WHERE expires_at < NOW();
    
    -- Clean up expired password reset tokens
    DELETE FROM password_reset_tokens WHERE expires_at < NOW();
    
    -- Clean up expired login sessions
    DELETE FROM login_sessions WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- 10. Enable Row Level Security (RLS) for better security
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE otp_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE password_reset_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_sessions ENABLE ROW LEVEL SECURITY;

-- 11. RLS Policies for clients table
CREATE POLICY "Users can view their own profile" ON clients
    FOR SELECT USING (auth.uid()::text = supabase_auth_id::text OR auth.uid()::text = id::text);

CREATE POLICY "Users can update their own profile" ON clients
    FOR UPDATE USING (auth.uid()::text = supabase_auth_id::text OR auth.uid()::text = id::text);

-- 12. RLS Policies for otp_verifications table
CREATE POLICY "Users can view their own OTP verifications" ON otp_verifications
    FOR SELECT USING (auth.uid()::text = client_id::text);

-- 13. RLS Policies for login_sessions table
CREATE POLICY "Users can view their own sessions" ON login_sessions
    FOR SELECT USING (auth.uid()::text = client_id::text);

CREATE POLICY "Users can update their own sessions" ON login_sessions
    FOR UPDATE USING (auth.uid()::text = client_id::text);

-- 14. Create a view for user authentication status
CREATE OR REPLACE VIEW user_auth_status AS
SELECT 
    c.id,
    c.email,
    c.username,
    c.business_name,
    c.is_email_verified,
    c.is_active,
    c.supabase_auth_id,
    CASE 
        WHEN c.supabase_auth_id IS NOT NULL THEN 'supabase'
        ELSE 'jwt'
    END as auth_method,
    c.created_at,
    c.last_login_at,
    c.failed_login_attempts,
    c.locked_until
FROM clients c;

-- 15. Function to migrate user to Supabase Auth
CREATE OR REPLACE FUNCTION migrate_user_to_supabase_auth(
    user_email TEXT,
    supabase_user_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE clients 
    SET supabase_auth_id = supabase_user_id,
        updated_at = NOW()
    WHERE email = user_email;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 16. Subscription Plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    billing_period VARCHAR(20) DEFAULT 'monthly', -- monthly, yearly
    features JSONB DEFAULT '{}'::jsonb,
    max_calls INTEGER,
    max_phone_numbers INTEGER DEFAULT 1,
    max_products INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 17. Client Subscriptions table
CREATE TABLE IF NOT EXISTS client_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    razorpay_subscription_id VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired, pending
    starts_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ends_at TIMESTAMP WITH TIME ZONE,
    auto_renew BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 18. Payment Transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES client_subscriptions(id),
    razorpay_order_id VARCHAR(255) NOT NULL,
    razorpay_payment_id VARCHAR(255),
    razorpay_signature VARCHAR(500),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(20) DEFAULT 'created', -- created, paid, failed, cancelled
    payment_method VARCHAR(50),
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add payment-related columns to clients table
ALTER TABLE clients ADD COLUMN IF NOT EXISTS current_plan_id UUID REFERENCES subscription_plans(id);
ALTER TABLE clients ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(20) DEFAULT 'free';
ALTER TABLE clients ADD COLUMN IF NOT EXISTS subscription_ends_at TIMESTAMP WITH TIME ZONE;

-- Insert default subscription plans
INSERT INTO subscription_plans (name, display_name, description, price, features, max_calls, max_phone_numbers, max_products, sort_order) VALUES
('free', 'Free', 'Basic features for testing', 0.00, '{"support": "email", "analytics": "basic"}', 10, 1, 5, 0),
('basic', 'Basic', 'Perfect for small businesses starting with voice automation', 499.00, '{"support": "email", "analytics": "basic", "whatsapp": "basic"}', 100, 1, 10, 1),
('pro', 'Pro', 'Ideal for growing businesses with higher call volumes', 999.00, '{"support": "priority", "analytics": "advanced", "whatsapp": "advanced", "appointments": true, "invoices": true, "payment_links": true}', 500, 1, 50, 2),
('business', 'Business', 'For established businesses needing comprehensive automation', 1999.00, '{"support": "dedicated", "analytics": "advanced", "whatsapp": "full", "appointments": true, "invoices": true, "payment_links": true, "api_access": true, "custom_responses": true}', 1500, 2, -1, 3),
('enterprise', 'Enterprise', 'Tailored solutions for large-scale operations', 0.00, '{"support": "24x7", "analytics": "enterprise", "whatsapp": "white_label", "appointments": true, "invoices": true, "payment_links": true, "api_access": true, "custom_responses": true, "multi_location": true}', -1, -1, -1, 4)
ON CONFLICT (name) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_client_subscriptions_client_id ON client_subscriptions(client_id);
CREATE INDEX IF NOT EXISTS idx_client_subscriptions_status ON client_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_client_id ON payment_transactions(client_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_razorpay_order_id ON payment_transactions(razorpay_order_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_clients_current_plan_id ON clients(current_plan_id);

-- Enable Row Level Security for new tables
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE client_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for subscription_plans (public read access)
CREATE POLICY "Anyone can view subscription plans" ON subscription_plans
    FOR SELECT USING (is_active = true);

-- RLS Policies for client_subscriptions
CREATE POLICY "Users can view their own subscriptions" ON client_subscriptions
    FOR SELECT USING (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

CREATE POLICY "Users can update their own subscriptions" ON client_subscriptions
    FOR UPDATE USING (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

-- RLS Policies for payment_transactions
CREATE POLICY "Users can view their own payment transactions" ON payment_transactions
    FOR SELECT USING (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

CREATE POLICY "Users can insert their own payment transactions" ON payment_transactions
    FOR INSERT WITH CHECK (auth.uid()::text = (SELECT id::text FROM clients WHERE id = client_id));

-- 19. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- 17. Insert some test data (optional - remove in production)
-- INSERT INTO clients (email, password_hash, business_name, username, is_email_verified, is_active)
-- VALUES ('<EMAIL>', '$2b$12$test_hash', 'Test Business', 'testuser', true, true);

-- Success message
SELECT 'All tables and functions created successfully!' as status;

const express = require('express');
const router = express.Router();
const razorpayService = require('../services/razorpayService');
const { authenticate } = require('../middleware/auth');
const { supabaseAdmin } = require('../config/supabase');

// Note: Sessions are now persistent (90 days) - no extension needed

// Enhanced authentication middleware for payment operations
const authenticateForPayment = async (req, res, next) => {
  try {
    console.log('🔐 Enhanced payment authentication started');

    // Try normal authentication first
    await new Promise((resolve, reject) => {
      authenticate(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log('✅ Payment auth: Token authentication successful');
    next();

  } catch (authError) {
    console.log('⚠️ Token auth failed during payment, attempting recovery');

    // If token auth fails, try to recover client from transaction
    const { razorpay_order_id } = req.body;

    if (!razorpay_order_id) {
      console.error('❌ Payment auth failed: No token and no order ID');
      return res.status(401).json({
        success: false,
        error: 'Authentication failed. Please login again and retry payment.'
      });
    }

    try {
      // Find the transaction to get client ID
      const { data: transaction, error: txnError } = await supabaseAdmin
        .from('payment_transactions')
        .select('client_id, metadata')
        .eq('razorpay_order_id', razorpay_order_id)
        .single();

      if (txnError || !transaction) {
        console.error('❌ Transaction not found for order:', razorpay_order_id);
        return res.status(401).json({
          success: false,
          error: 'Payment session expired. Please try again.'
        });
      }

      // Verify the client still exists
      const { data: client, error: clientError } = await supabaseAdmin
        .from('clients')
        .select('id, email, business_name')
        .eq('id', transaction.client_id)
        .single();

      if (clientError || !client) {
        console.error('❌ Client not found for transaction');
        return res.status(401).json({
          success: false,
          error: 'Invalid payment session. Please contact support.'
        });
      }

      // Set user in request for downstream processing
      req.user = {
        id: client.id,
        email: client.email,
        business_name: client.business_name,
        authMethod: 'transaction_recovery'
      };

      console.log('✅ Payment auth: Client recovered from transaction');
      next();

    } catch (recoveryError) {
      console.error('❌ Payment auth recovery failed:', recoveryError);
      return res.status(401).json({
        success: false,
        error: 'Authentication failed. Please login and retry payment.'
      });
    }
  }
};

// Get all subscription plans
router.get('/plans', async (req, res) => {
  try {
    const result = await razorpayService.getSubscriptionPlans();
    
    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }

    res.json({
      success: true,
      plans: result.plans
    });
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch subscription plans'
    });
  }
});

// Get client's current subscription
router.get('/current', authenticate, async (req, res) => {
  try {
    const clientId = req.user.id;
    const result = await razorpayService.getClientSubscription(clientId);
    
    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }

    res.json({
      success: true,
      subscription: result.subscription
    });
  } catch (error) {
    console.error('Error fetching current subscription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch current subscription'
    });
  }
});

// Create payment order for subscription
router.post('/create-order', authenticate, async (req, res) => {
  try {
    const { planId, billingPeriod = 'monthly' } = req.body;
    const clientId = req.user.id; // Use the actual client ID from database

    console.log('Create order request:', {
      planId,
      billingPeriod,
      clientId,
      userEmail: req.user.email
    });

    if (!planId) {
      return res.status(400).json({
        success: false,
        error: 'Plan ID is required'
      });
    }

    // Check if client exists in clients table
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id, email, username')
      .eq('id', clientId)
      .single();

    console.log('Client lookup result:', { client, clientError });

    if (clientError || !client) {
      console.error('Client not found:', { clientId, clientError });
      return res.status(404).json({
        success: false,
        error: 'Client not found. Please ensure you are properly logged in.'
      });
    }

    // Get plan details
    const { data: plan, error: planError } = await supabaseAdmin
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .eq('is_active', true)
      .single();

    if (planError || !plan) {
      return res.status(404).json({
        success: false,
        error: 'Plan not found'
      });
    }

    // Calculate amount based on billing period
    let amount = plan.price;
    if (billingPeriod === 'yearly') {
      amount = amount * 12 * 0.9; // 10% discount for yearly
    }

    // Create Razorpay order with short receipt (max 40 chars)
    const timestamp = Date.now().toString().slice(-8); // Last 8 digits
    const clientShort = clientId.slice(-8); // Last 8 chars of client ID
    const receipt = `sub_${clientShort}_${timestamp}`; // Format: sub_12345678_87654321 (max 24 chars)

    console.log('Creating Razorpay order:', {
      amount,
      currency: plan.currency,
      receipt,
      receiptLength: receipt.length,
      clientId,
      planId
    });

    const orderResult = await razorpayService.createOrder({
      amount,
      currency: plan.currency,
      receipt,
      notes: {
        client_id: clientId,
        plan_id: planId,
        plan_name: plan.name,
        billing_period: billingPeriod
      }
    });

    if (!orderResult.success) {
      return res.status(500).json({
        success: false,
        error: orderResult.error
      });
    }

    // Create payment transaction record
    console.log('Creating payment transaction for client:', clientId);
    const transactionResult = await razorpayService.createPaymentTransaction({
      clientId,
      razorpayOrderId: orderResult.order.id,
      amount,
      currency: plan.currency,
      description: `${plan.display_name} - ${billingPeriod} subscription`,
      metadata: {
        plan_id: planId,
        plan_name: plan.name,
        billing_period: billingPeriod
      }
    });

    console.log('Transaction creation result:', transactionResult);

    if (!transactionResult.success) {
      console.error('Failed to create payment transaction:', transactionResult.error);
      return res.status(500).json({
        success: false,
        error: `Failed to create payment transaction: ${transactionResult.error}`
      });
    }

    res.json({
      success: true,
      order: orderResult.order,
      transaction: transactionResult.transaction,
      plan: {
        id: plan.id,
        name: plan.display_name,
        amount,
        currency: plan.currency
      }
    });
  } catch (error) {
    console.error('Error creating payment order:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create payment order'
    });
  }
});

// Verify payment and activate subscription with enhanced authentication
router.post('/verify-payment', authenticateForPayment, async (req, res) => {
  try {
    console.log('🔍 Payment verification started');
    console.log('👤 Auth method:', req.user.authMethod || 'token');

    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      plan_id,
      billing_period = 'monthly'
    } = req.body;

    const clientId = req.user.id;
    console.log('💳 Processing payment for client:', clientId);

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature || !plan_id) {
      return res.status(400).json({
        success: false,
        error: 'Missing required payment verification data'
      });
    }

    // Verify payment signature
    const isValidSignature = razorpayService.verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature
    });

    if (!isValidSignature) {
      // Update transaction as failed
      await razorpayService.updatePaymentTransaction({
        razorpayOrderId: razorpay_order_id,
        status: 'failed',
        metadata: { error: 'Invalid signature' }
      });

      return res.status(400).json({
        success: false,
        error: 'Invalid payment signature'
      });
    }

    // Update payment transaction as paid
    const updateResult = await razorpayService.updatePaymentTransaction({
      razorpayOrderId: razorpay_order_id,
      razorpayPaymentId: razorpay_payment_id,
      razorpaySignature: razorpay_signature,
      status: 'paid'
    });

    if (!updateResult.success) {
      return res.status(500).json({
        success: false,
        error: updateResult.error
      });
    }

    // Create/activate subscription with rollback capability
    console.log('🔄 Creating subscription for client:', clientId);
    let subscriptionResult;

    try {
      subscriptionResult = await razorpayService.createClientSubscription({
        clientId,
        planId: plan_id,
        transactionId: updateResult.transaction.id,
        billingPeriod: billing_period
      });

      if (!subscriptionResult.success) {
        console.error('❌ Subscription creation failed:', subscriptionResult.error);

        // Rollback transaction status to failed
        await supabaseAdmin
          .from('payment_transactions')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString()
          })
          .eq('id', updateResult.transaction.id);

        return res.status(500).json({
          success: false,
          error: subscriptionResult.error || 'Failed to activate subscription'
        });
      }

      console.log('✅ Subscription created successfully');

    } catch (subscriptionError) {
      console.error('❌ Subscription creation error:', subscriptionError);

      // Rollback transaction status
      try {
        await supabaseAdmin
          .from('payment_transactions')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString()
          })
          .eq('id', updateResult.transaction.id);
      } catch (rollbackError) {
        console.error('❌ Transaction rollback failed:', rollbackError);
      }

      return res.status(500).json({
        success: false,
        error: 'Payment processed but subscription activation failed. Please contact support.'
      });
    }

    res.json({
      success: true,
      message: 'Payment verified and subscription activated successfully',
      subscription: subscriptionResult.subscription,
      transaction: updateResult.transaction
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify payment'
    });
  }
});

// Get payment history
router.get('/payments', authenticate, async (req, res) => {
  try {
    const clientId = req.user.id;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { data, error } = await supabaseAdmin
      .from('payment_transactions')
      .select(`
        *,
        client_subscriptions (
          subscription_plans (name, display_name)
        )
      `)
      .eq('client_id', clientId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    res.json({
      success: true,
      payments: data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: data.length === parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching payment history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch payment history'
    });
  }
});

// Razorpay webhook handler
router.post('/webhook', async (req, res) => {
  try {
    const signature = req.headers['x-razorpay-signature'];
    const body = JSON.stringify(req.body);

    // Verify webhook signature
    const isValidSignature = razorpayService.verifyWebhookSignature(body, signature);

    if (!isValidSignature) {
      return res.status(400).json({
        success: false,
        error: 'Invalid webhook signature'
      });
    }

    const event = req.body;
    console.log('Razorpay webhook event:', event.event);

    switch (event.event) {
      case 'payment.captured':
        await handlePaymentCaptured(event.payload.payment.entity);
        break;
      case 'payment.failed':
        await handlePaymentFailed(event.payload.payment.entity);
        break;
      default:
        console.log('Unhandled webhook event:', event.event);
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({
      success: false,
      error: 'Webhook processing failed'
    });
  }
});

// Helper function to handle payment captured
async function handlePaymentCaptured(payment) {
  try {
    await razorpayService.updatePaymentTransaction({
      razorpayOrderId: payment.order_id,
      razorpayPaymentId: payment.id,
      status: 'paid',
      paymentMethod: payment.method,
      metadata: {
        captured_at: payment.captured_at,
        bank: payment.bank,
        wallet: payment.wallet
      }
    });
  } catch (error) {
    console.error('Error handling payment captured:', error);
  }
}

// Helper function to handle payment failed
async function handlePaymentFailed(payment) {
  try {
    await razorpayService.updatePaymentTransaction({
      razorpayOrderId: payment.order_id,
      razorpayPaymentId: payment.id,
      status: 'failed',
      metadata: {
        error_code: payment.error_code,
        error_description: payment.error_description,
        error_reason: payment.error_reason
      }
    });
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

// Cancel subscription (for dev mode undo)
router.post('/cancel/:subscriptionId', authenticate, async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const clientId = req.user.id;

    // Only allow in development mode
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        success: false,
        error: 'Subscription cancellation not allowed in production'
      });
    }

    const result = await razorpayService.cancelSubscription(clientId, subscriptionId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: result.error
      });
    }

    res.json({
      success: true,
      message: result.message
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel subscription'
    });
  }
});

module.exports = router;

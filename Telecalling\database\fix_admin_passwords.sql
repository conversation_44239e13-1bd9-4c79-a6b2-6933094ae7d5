-- =====================================================
-- FIX ADMIN PASSWORDS - SIMPLE AUTHENTICATION
-- =====================================================
-- Execute these queries in Supabase SQL Editor to fix admin authentication

-- 1. Update regular admin password to plain text
UPDATE admins 
SET password_hash = 'admin123' 
WHERE admin_id = 'admin001';

-- 2. Update super admin password to plain text (if exists)
UPDATE admins 
SET password_hash = 'superadmin123' 
WHERE admin_id = 'superadmin001';

-- 3. If super admin doesn't exist, create it
INSERT INTO admins (admin_id, password_hash, name, email, role, is_active, created_at, updated_at) 
VALUES (
    'superadmin001', 
    'superadmin123',
    'Super Administrator',
    '<EMAIL>',
    'super_admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT (admin_id) DO UPDATE SET
    password_hash = 'superadmin123',
    role = 'super_admin',
    is_active = true;

-- 4. If regular admin doesn't exist, create it
INSERT INTO admins (admin_id, password_hash, name, email, role, is_active, created_at, updated_at) 
VALUES (
    'admin001', 
    'admin123',
    'System Administrator',
    '<EMAIL>',
    'admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT (admin_id) DO UPDATE SET
    password_hash = 'admin123',
    role = 'admin',
    is_active = true;

-- 5. Verify the passwords are set correctly
SELECT 
    admin_id, 
    name, 
    email, 
    role, 
    password_hash,
    is_active,
    created_at
FROM admins 
WHERE admin_id IN ('admin001', 'superadmin001')
ORDER BY role DESC;

-- Expected output:
-- admin_id: superadmin001, password_hash: superadmin123, role: super_admin
-- admin_id: admin001, password_hash: admin123, role: admin

-- 6. Test authentication query (this is what the backend will do)
SELECT 
    admin_id,
    name,
    email,
    role,
    is_active,
    CASE 
        WHEN password_hash = 'superadmin123' AND admin_id = 'superadmin001' THEN 'SUPER ADMIN AUTH SUCCESS'
        WHEN password_hash = 'admin123' AND admin_id = 'admin001' THEN 'ADMIN AUTH SUCCESS'
        ELSE 'AUTH FAILED'
    END as auth_test
FROM admins 
WHERE admin_id IN ('admin001', 'superadmin001');

SELECT '✅ Admin passwords fixed! Use these credentials:' as status;
SELECT 'Regular Admin: admin001 / admin123' as regular_admin;
SELECT 'Super Admin: superadmin001 / superadmin123' as super_admin;

import React, { useState, useEffect } from 'react';
import {
  Phone,
  MessageSquare,
  Package,
  BarChart3,
  HeadphonesIcon,
  TrendingUp,
  Plus,
  Edit,
  Trash2,
  Send,
  Download,
  Activity,
  CheckCircle,
  Clock,
  Mail,
  AlertCircle,
  User,
  LogOut,
  FileText,
  Building,
  MapPin,
  X,
  Settings,
  Shield,
  Key,
  CreditCard,
  Crown,
  Zap,
  Calendar,
  Users,
  Link,
  Copy,
  ExternalLink
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { useAuth } from '../../contexts/AuthContext';
import subscriptionAPI from '../../services/subscriptionAPI';
import PaymentModal from '../../components/modals/PaymentModal';
import PaymentSuccessModal from '../../components/modals/PaymentSuccessModal';
import UsageLimitsCard from '../../components/dashboard/UsageLimitsCard';
import '../../styles/plan499Dashboard.css';

const ClientDashboard = () => {
  const { user, logout } = useAuth();
  const [activeSection, setActiveSection] = useState('overview');
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [callLogs, setCallLogs] = useState([]);
  const [smsLogs, setSmsLogs] = useState([]);
  const [supportTickets, setSupportTickets] = useState([]);
  const [showAddProductForm, setShowAddProductForm] = useState(false);
  const [newProduct, setNewProduct] = useState({
    productName: '',
    productDetails: {},
    aliasNames: []
  });
  const [productKeyValuePairs, setProductKeyValuePairs] = useState([{ key: '', value: '' }]);
  const [showBulkUpload, setShowBulkUpload] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingProduct, setEditingProduct] = useState(null);
  const [inlineEditingId, setInlineEditingId] = useState(null);
  const [inlineEditData, setInlineEditData] = useState({});
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingModalProduct, setEditingModalProduct] = useState(null);

  // Billing state
  const [subscription, setSubscription] = useState(null);
  const [availablePlans, setAvailablePlans] = useState([]);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentResult, setPaymentResult] = useState(null);

  // Customer management state
  const [customers, setCustomers] = useState([]);
  const [customerChats, setCustomerChats] = useState([]);
  const [showRegistrationLinkModal, setShowRegistrationLinkModal] = useState(false);
  const [generatedLink, setGeneratedLink] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showChatModal, setShowChatModal] = useState(false);

  // Filter products based on search term
  const filteredProducts = products.filter(product => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();

    // Try both possible field names
    const productName = (product.product_name || product.productName || '').toLowerCase();
    const productDetails = JSON.stringify(product.product_details || product.productDetails || {}).toLowerCase();
    const aliases = ((product.alias_names || product.aliasNames || []).join(' ')).toLowerCase();

    const matches = productName.includes(searchLower) ||
                   productDetails.includes(searchLower) ||
                   aliases.includes(searchLower);

    // Debug search functionality - log every product
    console.log('🔍 Search Debug for product:', {
      productId: product.id,
      searchTerm: searchLower,
      productName,
      productDetails,
      aliases,
      matches,
      rawProduct: product
    });

    return matches;
  });

  // Always log search results
  console.log('🔍 Search Results Summary:', {
    searchTerm,
    totalProducts: products.length,
    filteredProducts: filteredProducts.length,
    allProducts: products.map(p => ({
      id: p.id,
      name: p.product_name || p.productName,
      details: p.product_details || p.productDetails
    }))
  });
  const [whatsappMessages] = useState([
    { id: 1, customer: 'Rahul Kumar', phone: '+91 9876543210', message: 'Hello, I need information about your services', time: '10:30 AM', status: 'received' },
    { id: 2, customer: 'Priya Sharma', phone: '+91 9876543211', message: 'Thank you for the quick response!', time: '11:15 AM', status: 'sent' },
    { id: 3, customer: 'Amit Singh', phone: '+91 9876543212', message: 'Can you send me the pricing details?', time: '12:00 PM', status: 'received' }
  ]);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileForm, setProfileForm] = useState({
    businessName: '',
    ownerName: '',
    businessEmail: '',
    mobileNumber: '',
    whatsappNumber: '',
    businessAddress: '',
    businessDescription: ''
  });

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('voicebot_access_token');

        if (!token) {
          console.error('No access token found');
          setLoading(false);
          return;
        }

        // Fetch profile data
        const profileResponse = await fetch('http://localhost:5000/api/profile', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!profileResponse.ok) {
          throw new Error('Failed to fetch profile data');
        }

        const profileData = await profileResponse.json();

        // Fetch dashboard overview
        const overviewResponse = await fetch('http://localhost:5000/api/analytics/dashboard-overview', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!overviewResponse.ok) {
          throw new Error('Failed to fetch dashboard overview');
        }

        const overviewData = await overviewResponse.json();

        setDashboardData({
          profile: profileData,
          overview: overviewData
        });

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setDashboardData({
          profile: { user: user, planSubscription: null },
          overview: { planInfo: null, todayStats: {}, weekStats: {} }
        });
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchDashboardData();
      fetchProducts();
      fetchCallLogs();
      fetchSmsLogs();
      fetchSupportTickets();
      fetchBillingData();
    }
  }, [user]);

  // Fetch products
  const fetchProducts = async () => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/products', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProducts(data.products || []);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  // Fetch call logs
  const fetchCallLogs = async () => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/analytics/call-logs?limit=10', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCallLogs(data.callLogs || []);
      }
    } catch (error) {
      console.error('Error fetching call logs:', error);
    }
  };

  // Fetch SMS logs
  const fetchSmsLogs = async () => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/analytics/sms-logs?limit=10', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSmsLogs(data.smsLogs || []);
      }
    } catch (error) {
      console.error('Error fetching SMS logs:', error);
    }
  };

  // Fetch support tickets
  const fetchSupportTickets = async () => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/support?limit=10', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSupportTickets(data.tickets || []);
      }
    } catch (error) {
      console.error('Error fetching support tickets:', error);
    }
  };

  // Fetch billing data
  const fetchBillingData = async () => {
    try {
      console.log('Fetching billing data...');
      const [subscriptionResponse, plansResponse, paymentsResponse] = await Promise.all([
        subscriptionAPI.getCurrentSubscription(),
        subscriptionAPI.getPlans(),
        subscriptionAPI.getPaymentHistory(1, 10)
      ]);

      console.log('Billing API responses:', { subscriptionResponse, plansResponse, paymentsResponse });

      if (subscriptionResponse.success) {
        setSubscription(subscriptionResponse.subscription);
      }

      if (plansResponse.success) {
        setAvailablePlans(plansResponse.plans);
      }

      if (paymentsResponse.success) {
        setPaymentHistory(paymentsResponse.payments);
      }
    } catch (error) {
      console.error('Error fetching billing data:', error);
    }
  };

  // Handle payment upgrade
  const handleUpgrade = (plan) => {
    console.log('handleUpgrade called with plan:', plan);
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  // Handle payment success
  const handlePaymentSuccess = (result) => {
    console.log('Payment successful:', result);
    setPaymentResult(result);
    setShowPaymentModal(false);
    setShowSuccessModal(true);
    fetchBillingData(); // Refresh billing data
  };

  // Handle payment modal close
  const handlePaymentModalClose = () => {
    setShowPaymentModal(false);
    setSelectedPlan(null);
  };

  // Handle success modal close
  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    setPaymentResult(null);
  };

  // Removed duplicate state declarations - using the ones declared at the top

  const [newTicket, setNewTicket] = useState({ subject: '', description: '', priority: 'Medium' });

  // Chat functionality state
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [ticketMessages, setTicketMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [showTicketChat, setShowTicketChat] = useState(false);

  // Multiple product deletion state
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // Handle profile update
  const handleProfileUpdate = async () => {
    try {
      const token = localStorage.getItem('voicebot_access_token');

      // Transform frontend form data to match backend expectations
      const profileData = {
        username: user?.username || user?.email || 'user', // Required by backend
        businessName: profileForm.businessName,
        ownerName: profileForm.ownerName,
        businessEmail: profileForm.businessEmail,
        mobileNumber: profileForm.mobileNumber,
        whatsappNumber: profileForm.whatsappNumber,
        businessAddress: profileForm.businessAddress,
        businessSummary: profileForm.businessDescription // Backend expects businessSummary
      };

      const response = await fetch('http://localhost:5000/api/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profileData)
      });

      if (response.ok) {
        // Refresh dashboard data
        const profileResponse = await fetch('http://localhost:5000/api/profile', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (profileResponse.ok) {
          const profileData = await profileResponse.json();
          setDashboardData(prev => ({
            ...prev,
            profile: profileData
          }));
        }

        setIsEditingProfile(false);
        alert('Profile updated successfully!');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile');
    }
  };

  // Handle adding/editing product
  const handleAddProduct = async () => {
    try {
      const token = localStorage.getItem('voicebot_access_token');

      // Convert key-value pairs to productDetails object
      const productDetails = {};
      productKeyValuePairs.forEach(pair => {
        if (pair.key && pair.value) {
          productDetails[pair.key] = pair.value;
        }
      });

      const productData = {
        ...newProduct,
        productDetails
      };

      const isEditing = editingProduct !== null;
      const url = isEditing
        ? `http://localhost:5000/api/products/${editingProduct.id}`
        : 'http://localhost:5000/api/products';

      const method = isEditing ? 'PUT' : 'POST';

      console.log('💾 Saving product:', {
        isEditing,
        productId: editingProduct?.id,
        url,
        method,
        productData
      });

      const response = await fetch(url, {
        method: method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      });

      console.log('💾 Save response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Save successful:', result);

        fetchProducts(); // Refresh products list
        setShowAddProductForm(false);
        setNewProduct({
          productName: '',
          productDetails: {},
          aliasNames: []
        });
        setProductKeyValuePairs([{ key: '', value: '' }]);
        setEditingProduct(null);
        alert(isEditing ? '✅ Product updated successfully!' : '✅ Product added successfully!');
      } else {
        const error = await response.json();
        console.error('❌ Save failed:', error);
        alert('❌ Failed to ' + (isEditing ? 'update' : 'add') + ' product: ' + (error.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error ' + (editingProduct ? 'updating' : 'adding') + ' product:', error);
      alert('❌ Network error: Unable to ' + (editingProduct ? 'update' : 'add') + ' product');
    }
  };

  // Test authentication before upload
  const testAuthentication = async () => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Authentication test successful:', data.user?.email);
        return true;
      } else {
        const error = await response.json();
        console.error('❌ Authentication test failed:', error);
        return false;
      }
    } catch (error) {
      console.error('❌ Authentication test error:', error);
      return false;
    }
  };

  // Handle bulk upload with OpenAI
  const handleBulkUpload = async () => {
    if (!uploadFile) {
      alert('Please select a file to upload');
      return;
    }

    if (isUploading) {
      alert('⏳ Upload already in progress. Please wait...');
      return;
    }

    // Check if user is authenticated
    if (!user) {
      alert('❌ Authentication Error: User not logged in. Please refresh the page and try again.');
      return;
    }

    // Test authentication before proceeding
    const authTest = await testAuthentication();
    if (!authTest) {
      alert('❌ Authentication Error: Unable to verify your login. Please refresh the page and log in again.');
      return;
    }

    setIsUploading(true);

    try {
      const token = localStorage.getItem('voicebot_access_token');

      if (!token) {
        alert('❌ Authentication Error: No access token found. Please log in again.');
        return;
      }

      console.log('🔍 Debug Info:');
      console.log('- User:', user?.email);
      console.log('- Token exists:', !!token);
      console.log('- Token preview:', token?.substring(0, 20) + '...');
      console.log('- Auth test passed: ✅');

      const formData = new FormData();
      formData.append('files', uploadFile); // Changed from 'file' to 'files' for AI endpoint

      const response = await fetch('http://localhost:5000/api/products/ai-bulk-upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        fetchProducts(); // Refresh products list
        setShowBulkUpload(false);
        setUploadFile(null);

        // Handle AI endpoint response format
        const successCount = result.results?.successfulProducts || 0;
        const totalProducts = result.results?.totalProducts || 0;
        const failedCount = result.results?.failedProducts || 0;

        if (successCount > 0) {
          alert(`🎉 AI Processing Complete!\n✅ Successfully added: ${successCount} products\n📊 Total found: ${totalProducts}\n❌ Failed: ${failedCount}`);
        } else {
          alert(`⚠️ No products were added. Please check your file format.`);
        }

        setIsUploading(false);
      } else {
        const error = await response.json();
        console.error('❌ Upload failed:', error);

        // Provide specific error messages
        if (response.status === 401) {
          alert('❌ Authentication Error: ' + (error.message || 'Please log in again and try uploading.'));
        } else if (response.status === 403) {
          alert('❌ Permission Error: ' + (error.message || 'You do not have permission to upload products.'));
        } else if (response.status === 500) {
          alert('❌ Server Error: ' + (error.message || 'Please check if OpenAI is configured properly.'));
        } else {
          alert('❌ Upload Failed: ' + (error.message || 'Please try again or contact support.'));
        }
      }
    } catch (error) {
      console.error('❌ Network/Client Error:', error);
      alert('❌ Network Error: Unable to connect to server. Please check your internet connection and try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle product edit
  const handleEditProduct = (product) => {
    console.log('🔧 Editing product:', product);

    // Set form data for editing
    setNewProduct({
      productName: product.product_name || '',
      productDetails: product.product_details || {},
      aliasNames: product.alias_names || []
    });

    // Convert product details to key-value pairs for editing
    const keyValuePairs = Object.entries(product.product_details || {})
      .map(([key, value]) => ({ key, value: String(value) }));

    // Ensure at least one empty pair for adding new fields
    if (keyValuePairs.length === 0) {
      keyValuePairs.push({ key: '', value: '' });
    } else {
      keyValuePairs.push({ key: '', value: '' });
    }

    console.log('🔧 Key-value pairs for editing:', keyValuePairs);

    setProductKeyValuePairs(keyValuePairs);
    setEditingProduct(product);
    setShowAddProductForm(true);
  };

  // Multiple product selection functions
  const toggleProductSelection = (productId) => {
    setSelectedProducts(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  };

  const selectAllProducts = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(product => product.id));
    }
  };

  const handleDeleteSelectedProducts = async () => {
    if (selectedProducts.length === 0) {
      alert('Please select products to delete');
      return;
    }

    const confirmMessage = `Are you sure you want to delete ${selectedProducts.length} selected product${selectedProducts.length > 1 ? 's' : ''}? This action cannot be undone.`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      const token = localStorage.getItem('voicebot_access_token');

      // Use bulk delete endpoint for better performance
      const response = await fetch('http://localhost:5000/api/products/bulk', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productIds: selectedProducts
        })
      });

      if (response.ok) {
        const result = await response.json();
        alert(`✅ ${result.message}`);
        fetchProducts(); // Refresh the products list
        setSelectedProducts([]);
        setIsSelectionMode(false);
      } else {
        const error = await response.json();
        alert(`❌ Failed to delete products: ${error.message}`);
      }
    } catch (error) {
      console.error('Error deleting products:', error);
      alert('❌ An error occurred while deleting products.');
    }
  };

  const cancelSelection = () => {
    setSelectedProducts([]);
    setIsSelectionMode(false);
  };

  // Handle product delete
  const handleDeleteProduct = async (productId) => {
    console.log('🗑️ Deleting product with ID:', productId);

    if (!window.confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('voicebot_access_token');
      console.log('🔑 Using token for delete:', token ? 'Token exists' : 'No token');

      const response = await fetch(`http://localhost:5000/api/products/${productId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('🗑️ Delete response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Delete successful:', result);
        alert('✅ Product deleted successfully!');
        fetchProducts(); // Refresh the products list
      } else {
        const error = await response.json();
        console.error('❌ Delete failed:', error);
        alert('❌ Failed to delete product: ' + (error.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('❌ Network error deleting product:', error);
      alert('❌ Network error: Unable to delete product');
    }
  };

  // Handle modal editing
  const startModalEdit = (product) => {
    console.log('🔧 Starting modal edit for:', product.product_name || product.productName);
    console.log('🔧 Product details to edit:', product.product_details || product.productDetails);

    setEditingModalProduct(product);

    // Get all product details (JSONB data) and make them editable
    const allDetails = { ...(product.product_details || product.productDetails || {}) };

    setInlineEditData({
      productName: product.product_name || product.productName || '',
      productDetails: allDetails,
      aliasNames: ((product.alias_names || product.aliasNames || []).join(', ')) || ''
    });

    setShowEditModal(true);

    console.log('🔧 Modal edit data set:', {
      productName: product.product_name || product.productName,
      productDetails: allDetails,
      aliasNames: (product.alias_names || product.aliasNames || []).join(', ')
    });
  };

  const cancelModalEdit = () => {
    setShowEditModal(false);
    setEditingModalProduct(null);
    setInlineEditData({});
  };

  const saveModalEdit = async () => {
    if (!editingModalProduct) return;

    try {
      const token = localStorage.getItem('voicebot_access_token');

      const response = await fetch(`http://localhost:5000/api/products/${editingModalProduct.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productName: inlineEditData.productName,
          productDetails: inlineEditData.productDetails,
          aliasNames: inlineEditData.aliasNames.split(',').map(name => name.trim()).filter(name => name)
        })
      });

      if (response.ok) {
        alert('✅ Product updated successfully!');
        setShowEditModal(false);
        setEditingModalProduct(null);
        setInlineEditData({});
        fetchProducts(); // Refresh the products list
      } else {
        const error = await response.json();
        alert('❌ Failed to update product: ' + (error.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('❌ Error updating product:', error);
      alert('❌ Network error: Unable to update product');
    }
  };

  // Sample data for charts (keeping for chart display)
  const callsData = [
    { month: 'Jan', calls: 45, successful: 38, failed: 7 },
    { month: 'Feb', calls: 52, successful: 47, failed: 5 },
    { month: 'Mar', calls: 67, successful: 61, failed: 6 },
    { month: 'Apr', calls: 78, successful: 72, failed: 6 },
    { month: 'May', calls: 65, successful: 58, failed: 7 },
    { month: 'Jun', calls: 89, successful: 84, failed: 5 }
  ];

  const pieData = [
    { name: 'Successful', value: 84, color: '#10B981' },
    { name: 'Failed', value: 16, color: '#EF4444' }
  ];



  const handleSendWhatsAppMessage = (customerId) => {
    // WhatsApp message sending functionality
    console.log('Send WhatsApp message to:', customerId);
  };

  // Chat functionality functions
  const fetchTicketMessages = async (ticketId) => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch(`http://localhost:5000/api/support/${ticketId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTicketMessages(data.messages || []);
      } else {
        console.error('Failed to fetch ticket messages');
      }
    } catch (error) {
      console.error('Error fetching ticket messages:', error);
    }
  };

  const sendTicketMessage = async (ticketId, message) => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch(`http://localhost:5000/api/support/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message })
      });

      if (response.ok) {
        const data = await response.json();
        setTicketMessages(prev => [...prev, data.messageData]);
        setNewMessage('');
        return true;
      } else {
        console.error('Failed to send message');
        return false;
      }
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  };

  const handleCreateTicket = async () => {
    if (!newTicket.subject || !newTicket.description) {
      alert('Please fill in both subject and description');
      return;
    }

    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/support', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subject: newTicket.subject,
          message: newTicket.description,
          urgencyLevel: newTicket.priority.toLowerCase()
        })
      });

      if (response.ok) {
        const data = await response.json();

        // Add the new ticket to the local state
        const ticket = {
          id: data.ticket.id,
          subject: data.ticket.subject,
          description: data.ticket.message,
          priority: data.ticket.urgencyLevel,
          status: data.ticket.status,
          date: new Date(data.ticket.createdAt).toISOString().split('T')[0]
        };

        setSupportTickets([ticket, ...supportTickets]);
        setNewTicket({ subject: '', description: '', priority: 'Medium' });

        alert('Support ticket created successfully! Our team will respond via email.');
      } else {
        const errorData = await response.json();
        alert(`Failed to create ticket: ${errorData.message}`);
      }
    } catch (error) {
      console.error('Error creating support ticket:', error);
      alert('Failed to create support ticket. Please try again.');
    }
  };

  const renderOverview = () => (
    <div className="overview-section" style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '0', fontFamily: "'Poppins', sans-serif" }}>
      <div className="section-header" style={{ borderBottom: '2px solid #e5e7eb', paddingBottom: '1rem', marginBottom: '2rem' }}>
        <h2 style={{ fontWeight: 700, fontSize: '2rem', color: '#1f2937' }}>Dashboard Overview</h2>
        <p style={{ color: '#4b5563', fontSize: '1.125rem' }}>Your voice bot performance at a glance</p>
      </div>

      <div className="stats-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '1.5rem' }}>
        <div className="stat-card primary" style={{ backgroundColor: '#f9fafb', borderRadius: '0', boxShadow: '0 4px 12px rgba(0,0,0,0.1)', padding: '1.5rem', transition: 'transform 0.3s ease', cursor: 'default' }}>
          <div className="stat-icon" style={{ color: '#3b82f6', marginBottom: '0.75rem' }}>
            <Phone size={32} />
          </div>
          <div className="stat-content">
            <h3 style={{ fontWeight: 600, fontSize: '1.25rem', color: '#111827' }}>Phone Number</h3>
            <p className="stat-value" style={{ fontSize: '1.5rem', fontWeight: 700, color: '#111827' }}>{dashboardData?.profile?.user?.assignedPlivoNumber || 'Not Assigned'}</p>
            <span className="stat-label" style={{ color: '#6b7280', fontWeight: 500 }}>Active & Configured</span>
          </div>
        </div>

        <div className="stat-card secondary" style={{ backgroundColor: '#f9fafb', borderRadius: '0', boxShadow: '0 4px 12px rgba(0,0,0,0.1)', padding: '1.5rem', transition: 'transform 0.3s ease', cursor: 'default' }}>
          <div className="stat-icon" style={{ color: '#6366f1', marginBottom: '0.75rem' }}>
            <Activity size={32} />
          </div>
          <div className="stat-content">
            <h3 style={{ fontWeight: 600, fontSize: '1.25rem', color: '#111827' }}>Calls Today</h3>
            <p className="stat-value" style={{ fontSize: '1.5rem', fontWeight: 700, color: '#111827' }}>{dashboardData?.overview?.todayStats?.calls || 0}</p>
            <span className="stat-label" style={{ color: '#6b7280', fontWeight: 500 }}>Total calls made today</span>
          </div>
        </div>

        <div className="stat-card success" style={{ backgroundColor: '#f9fafb', borderRadius: '0', boxShadow: '0 4px 12px rgba(0,0,0,0.1)', padding: '1.5rem', transition: 'transform 0.3s ease', cursor: 'default' }}>
          <div className="stat-icon" style={{ color: '#10b981', marginBottom: '0.75rem' }}>
            <MessageSquare size={32} />
          </div>
          <div className="stat-content">
            <h3 style={{ fontWeight: 600, fontSize: '1.25rem', color: '#111827' }}>SMS Today</h3>
            <p className="stat-value" style={{ fontSize: '1.5rem', fontWeight: 700, color: '#111827' }}>{dashboardData?.overview?.todayStats?.sms || 0}</p>
            <span className="stat-label" style={{ color: '#6b7280', fontWeight: 500 }}>SMS sent today</span>
          </div>
        </div>

        <div className="stat-card warning" style={{ backgroundColor: '#f9fafb', borderRadius: '0', boxShadow: '0 4px 12px rgba(0,0,0,0.1)', padding: '1.5rem', transition: 'transform 0.3s ease', cursor: 'default' }}>
          <div className="stat-icon" style={{ color: '#f59e0b', marginBottom: '0.75rem' }}>
            <Package size={32} />
          </div>
          <div className="stat-content">
            <h3 style={{ fontWeight: 600, fontSize: '1.25rem', color: '#111827' }}>Total Products</h3>
            <p className="stat-value" style={{ fontSize: '1.5rem', fontWeight: 700, color: '#111827' }}>{dashboardData?.overview?.totalStats?.products || products.length || 0}</p>
            <span className="stat-label" style={{ color: '#6b7280', fontWeight: 500 }}>Products in catalog</span>
          </div>
        </div>

        <div className="stat-card info" style={{ backgroundColor: '#f9fafb', borderRadius: '0', boxShadow: '0 4px 12px rgba(0,0,0,0.1)', padding: '1.5rem', transition: 'transform 0.3s ease', cursor: 'default' }}>
          <div className="stat-icon" style={{ color: '#3b82f6', marginBottom: '0.75rem' }}>
            <Phone size={32} />
          </div>
          <div className="stat-content">
            <h3 style={{ fontWeight: 600, fontSize: '1.25rem', color: '#111827' }}>Total Calls</h3>
            <p className="stat-value" style={{ fontSize: '1.5rem', fontWeight: 700, color: '#111827' }}>{dashboardData?.overview?.totalStats?.calls || callLogs.length || 0}</p>
            <span className="stat-label" style={{ color: '#6b7280', fontWeight: 500 }}>All time calls</span>
          </div>
        </div>

        <div className="stat-card success" style={{ backgroundColor: '#f9fafb', borderRadius: '0', boxShadow: '0 4px 12px rgba(0,0,0,0.1)', padding: '1.5rem', transition: 'transform 0.3s ease', cursor: 'default' }}>
          <div className="stat-icon" style={{ color: '#10b981', marginBottom: '0.75rem' }}>
            <MessageSquare size={32} />
          </div>
          <div className="stat-content">
            <h3 style={{ fontWeight: 600, fontSize: '1.25rem', color: '#111827' }}>Total SMS</h3>
            <p className="stat-value" style={{ fontSize: '1.5rem', fontWeight: 700, color: '#111827' }}>{dashboardData?.overview?.totalStats?.sms || smsLogs.length || 0}</p>
            <span className="stat-label" style={{ color: '#6b7280', fontWeight: 500 }}>All time SMS sent</span>
          </div>
        </div>
      </div>

      {/* Usage Limits Section */}
      <div className="usage-limits-section">
        <div className="usage-card">
          <div className="icon-text">
            <Phone className="usage-icon" />
            <h3>Call Minutes</h3>
          </div>
          <p>{dashboardData?.usage?.callMinutes || 0}</p>
          <span>Used this month</span>
        </div>

        <div className="usage-card">
          <div className="icon-text">
            <MessageSquare className="usage-icon" />
            <h3>SMS Messages</h3>
          </div>
          <p>{dashboardData?.usage?.smsMessages || 0}</p>
          <span>Used this month</span>
        </div>
      </div>

      <div className="charts-section">
        <div className="chart-container">
          <h3 style={{ fontWeight: 700, fontSize: '1.5rem', color: '#1f2937', marginBottom: '1rem' }}>Call Analytics - Last 6 Months</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={callsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="calls" stroke="#3B82F6" strokeWidth={2} />
              <Line type="monotone" dataKey="successful" stroke="#10B981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="chart-container">
          <h3 style={{ fontWeight: 700, fontSize: '1.5rem', color: '#1f2937', marginBottom: '1rem' }}>Success Rate</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );

  const renderCallLogs = () => (
    <div className="call-logs-section">
      <div className="section-header">
        <h2>Call Logs & Analytics</h2>
        <button className="btn-primary">
          <Download size={16} />
          Export Report
        </button>
      </div>

      <div className="analytics-cards">
        <div className="analytics-card">
          <div className="card-icon success">
            <CheckCircle />
          </div>
          <div className="card-content">
            <h4>Total Calls</h4>
            <p className="value">{callLogs.length}</p>
            <span className="change positive">Recent calls</span>
          </div>
        </div>

        <div className="analytics-card">
          <div className="card-icon info">
            <Clock />
          </div>
          <div className="card-content">
            <h4>Today's Calls</h4>
            <p className="value">{dashboardData?.overview?.todayStats?.calls || 0}</p>
            <span className="change neutral">Calls made today</span>
          </div>
        </div>

        <div className="analytics-card">
          <div className="card-icon warning">
            <Activity />
          </div>
          <div className="card-content">
            <h4>This Week</h4>
            <p className="value">{dashboardData?.overview?.weekStats?.calls || 0}</p>
            <span className="change positive">Weekly total</span>
          </div>
        </div>

        <div className="analytics-card">
          <div className="card-icon primary">
            <Clock />
          </div>
          <div className="card-content">
            <h4>Avg Call Duration</h4>
            <p className="value">3:42</p>
            <span className="change positive">+8% vs last month</span>
          </div>
        </div>
      </div>

      <div className="calls-chart">
        <h3>Monthly Call Trends</h3>
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={callsData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="successful" fill="#10B981" name="Successful" />
            <Bar dataKey="failed" fill="#EF4444" name="Failed" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="recent-calls">
        <h3>Recent Calls</h3>
        <div className="calls-table">
          <div className="table-container">
            <table>
            <thead>
              <tr>
                <th>Caller Number</th>
                <th>Duration</th>
                <th>Status</th>
                <th>Time</th>
                <th>Summary</th>
              </tr>
            </thead>
            <tbody>
              {callLogs.length > 0 ? callLogs.map(call => (
                <tr key={call.id}>
                  <td>{call.callerNumber}</td>
                  <td>{call.durationMinutes} min</td>
                  <td>
                    <span className={`status ${call.callStatus?.toLowerCase() || 'completed'}`}>
                      {call.callStatus || 'Completed'}
                    </span>
                  </td>
                  <td>{new Date(call.callTimestamp).toLocaleString()}</td>
                  <td>{call.botConversationSummary || 'No summary'}</td>
                </tr>
              )) : (
                <tr>
                  <td colSpan="5" className="empty-message">
                    No call logs available yet
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          </div>
        </div>
      </div>
    </div>
  );

  const renderWhatsApp = () => (
    <div className="whatsapp-section">
      <div className="section-header">
        <h2>WhatsApp Integration</h2>
        <div className="whatsapp-status">
          <div className="status-indicator active"></div>
          <span>Connected</span>
        </div>
      </div>

      <div className="whatsapp-stats">
        <div className="stat-item">
          <h4>Messages Sent</h4>
          <p>89</p>
        </div>
        <div className="stat-item">
          <h4>Messages Received</h4>
          <p>56</p>
        </div>
        <div className="stat-item">
          <h4>Active Chats</h4>
          <p>12</p>
        </div>
      </div>

      <div className="message-interface">
        <div className="messages-list">
          <h3>Recent Messages</h3>
          {whatsappMessages.map(message => (
            <div key={message.id} className={`message-item ${message.status}`}>
              <div className="message-header">
                <span className="customer-name">{message.customer}</span>
                <span className="message-time">{message.time}</span>
              </div>
              <p className="message-text">{message.message}</p>
              <div className="message-actions">
                <button className="btn-secondary" onClick={() => handleSendWhatsAppMessage(message.id)}>
                  <Send size={14} />
                  Reply
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="compose-message">
          <h3>Send New Message</h3>
          <div className="compose-form">
            <input type="text" placeholder="Customer Phone Number" className="form-input" />
            <textarea placeholder="Type your message here..." className="form-textarea"></textarea>
            <button className="btn-primary">
              <Send size={16} />
              Send Message
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderProducts = () => (
    <div className="products-section">
      <div className="section-header">
        <h2>Product Catalog</h2>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <button className="btn-primary" onClick={() => {
            setEditingProduct(null);
            setNewProduct({
              productName: '',
              productDetails: {},
              aliasNames: []
            });
            setProductKeyValuePairs([{ key: '', value: '' }]);
            setShowAddProductForm(true);
          }}>
            <Plus size={16} />
            Add Product
          </button>
          <button
            onClick={() => {
              console.log('🔍 ALL PRODUCTS DEBUG:', {
                totalProducts: products.length,
                products: products,
                searchTerm,
                filteredProducts: filteredProducts.length,
                filteredProductsList: filteredProducts
              });
              alert(`Debug info logged to console. Total: ${products.length}, Filtered: ${filteredProducts.length}`);
            }}
            style={{
              background: '#ff9800',
              color: 'white',
              border: 'none',
              padding: '8px 12px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🐛 Debug Products
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <div style={{ position: 'relative' }}>
          <input
            type="text"
            placeholder="🔍 Search products by name, features, or aliases..."
            value={searchTerm || ''}
            onChange={(e) => {
              const newValue = e.target.value;
              console.log('🔍 Search input changed from:', searchTerm, 'to:', newValue);
              setSearchTerm(newValue);
              console.log('🔍 Search term after setState:', newValue);
            }}
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '2px solid #e1e5e9',
              borderRadius: '8px',
              fontSize: '16px',
              outline: 'none',
              transition: 'border-color 0.2s ease'
            }}
            onFocus={(e) => e.target.style.borderColor = '#2196f3'}
            onBlur={(e) => e.target.style.borderColor = '#e1e5e9'}
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                color: '#666',
                cursor: 'pointer',
                fontSize: '18px'
              }}
            >
              ✕
            </button>
          )}
        </div>
        {searchTerm && (
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '14px' }}>
            Found {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} matching "{searchTerm}"
            <button
              onClick={() => {
                console.log('🔍 Search Debug Info:', {
                  searchTerm,
                  totalProducts: products.length,
                  filteredProducts: filteredProducts.length,
                  products: products.map(p => ({
                    name: p.product_name,
                    details: p.product_details,
                    aliases: p.alias_names
                  }))
                });
              }}
              style={{
                marginLeft: '10px',
                padding: '2px 8px',
                fontSize: '12px',
                background: '#2196f3',
                color: 'white',
                border: 'none',
                borderRadius: '3px',
                cursor: 'pointer'
              }}
            >
              Debug
            </button>
          </p>
        )}
      </div>

      {showAddProductForm && (
        <div className="add-product-form" style={{
          background: '#f8f9fa',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <h3>{editingProduct ? `✏️ Edit Product: ${editingProduct.product_name}` : '➕ Add New Product'}</h3>
          {editingProduct && (
            <div style={{
              background: '#e3f2fd',
              padding: '10px 15px',
              borderRadius: '6px',
              marginBottom: '15px',
              border: '1px solid #2196f3'
            }}>
              <p style={{ margin: '0', color: '#1976d2', fontSize: '14px' }}>
                <strong>Editing:</strong> {editingProduct.product_name}
                {editingProduct.product_details?.category && (
                  <span> • Category: {editingProduct.product_details.category}</span>
                )}
                {editingProduct.product_details?.price && (
                  <span> • Price: ₹{editingProduct.product_details.price}</span>
                )}
              </p>
            </div>
          )}
          <div style={{ display: 'grid', gap: '15px', marginTop: '15px' }}>
            <input
              type="text"
              placeholder="Product Name"
              value={newProduct.productName}
              onChange={(e) => setNewProduct({...newProduct, productName: e.target.value})}
              style={{ padding: '10px', border: '1px solid #ddd', borderRadius: '4px' }}
            />

            <div>
              <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                Product Details (Key-Value Pairs):
              </label>
              {productKeyValuePairs.map((pair, index) => (
                <div key={index} style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
                  <input
                    type="text"
                    placeholder="Key (e.g., brand, price, color)"
                    value={pair.key}
                    onChange={(e) => {
                      const newPairs = [...productKeyValuePairs];
                      newPairs[index].key = e.target.value;
                      setProductKeyValuePairs(newPairs);
                    }}
                    style={{ flex: 1, padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                  />
                  <input
                    type="text"
                    placeholder="Value (e.g., Tata, ₹4000, Red)"
                    value={pair.value}
                    onChange={(e) => {
                      const newPairs = [...productKeyValuePairs];
                      newPairs[index].value = e.target.value;
                      setProductKeyValuePairs(newPairs);
                    }}
                    style={{ flex: 1, padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
                  />
                  <button
                    onClick={() => {
                      if (productKeyValuePairs.length > 1) {
                        setProductKeyValuePairs(productKeyValuePairs.filter((_, i) => i !== index));
                      }
                    }}
                    style={{
                      background: '#dc3545',
                      color: 'white',
                      border: 'none',
                      padding: '8px 12px',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    ×
                  </button>
                </div>
              ))}
              <button
                onClick={() => setProductKeyValuePairs([...productKeyValuePairs, { key: '', value: '' }])}
                style={{
                  background: '#28a745',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                + Add More Details
              </button>
            </div>

            <input
              type="text"
              placeholder="Alias Names (comma separated)"
              value={editingProduct ? (editingProduct.alias_names || []).join(', ') : ((newProduct.aliasNames || []).join(', ') || '')}
              onChange={(e) => setNewProduct({
                ...newProduct,
                aliasNames: e.target.value.split(',').map(name => name.trim()).filter(name => name)
              })}
              style={{ padding: '10px', border: '1px solid #ddd', borderRadius: '4px' }}
            />

            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <button className="btn-primary" onClick={handleAddProduct}>
                {editingProduct ? '✅ Update Product' : '➕ Add Product'}
              </button>
              <button
                onClick={() => setShowBulkUpload(true)}
                style={{
                  background: '#17a2b8',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                📁 Bulk Upload
              </button>
              <button
                className="btn-secondary"
                onClick={() => {
                  setShowAddProductForm(false);
                  setEditingProduct(null);
                  setNewProduct({
                    productName: '',
                    productDetails: {},
                    aliasNames: []
                  });
                  setProductKeyValuePairs([{ key: '', value: '' }]);
                }}
                style={{ background: '#6c757d', color: 'white', border: 'none', padding: '10px 20px', borderRadius: '4px' }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {showBulkUpload && (
        <div className="bulk-upload-form" style={{
          background: '#e3f2fd',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '2px dashed #2196f3'
        }}>
          <h3>🤖 AI-Powered Bulk Product Upload</h3>
          <p style={{ color: '#666', marginBottom: '15px' }}>
            Upload a menu image, PDF, Excel sheet, or Word document. Our AI will automatically extract product details!
          </p>

          <div style={{ marginBottom: '15px' }}>
            <input
              type="file"
              accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.xlsx,.xls,.docx,.doc,.csv,.txt"
              onChange={(e) => setUploadFile(e.target.files[0])}
              style={{
                padding: '10px',
                border: '2px solid #2196f3',
                borderRadius: '4px',
                background: 'white',
                width: '100%'
              }}
            />
            <small style={{ color: '#666', display: 'block', marginTop: '5px' }}>
              Supported formats: Images (JPG, PNG), PDF, Excel (XLSX, XLS), Word (DOCX, DOC)
            </small>
          </div>

          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            <button
              className="btn-primary"
              onClick={handleBulkUpload}
              disabled={!uploadFile || isUploading}
              style={{
                background: isUploading ? '#ff9800' : (uploadFile ? '#2196f3' : '#ccc'),
                cursor: (!uploadFile || isUploading) ? 'not-allowed' : 'pointer',
                opacity: isUploading ? 0.8 : 1
              }}
            >
              {isUploading ? '⏳ Processing with AI...' : '🚀 Upload & Process with AI'}
            </button>
            <button
              className="btn-secondary"
              onClick={async () => {
                const result = await testAuthentication();
                alert(result ? '✅ Authentication working!' : '❌ Authentication failed!');
              }}
              style={{
                background: '#17a2b8',
                color: 'white',
                border: 'none',
                padding: '10px 15px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              🔍 Test Auth
            </button>
            <button
              onClick={() => {
                setShowBulkUpload(false);
                setUploadFile(null);
              }}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Selection Controls */}
      {products.length > 0 && (
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          padding: '15px',
          background: '#f8f9fa',
          borderRadius: '8px',
          border: '1px solid #e9ecef'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              className={isSelectionMode ? "btn-secondary" : "btn-primary"}
              onClick={() => setIsSelectionMode(!isSelectionMode)}
              style={{ padding: '8px 16px' }}
            >
              {isSelectionMode ? 'Cancel Selection' : 'Select Multiple'}
            </button>

            {isSelectionMode && (
              <>
                <button
                  className="btn-secondary"
                  onClick={selectAllProducts}
                  style={{ padding: '8px 16px' }}
                >
                  {selectedProducts.length === products.length ? 'Deselect All' : 'Select All'}
                </button>

                {selectedProducts.length > 0 && (
                  <span style={{ color: '#666', fontSize: '14px' }}>
                    {selectedProducts.length} product{selectedProducts.length > 1 ? 's' : ''} selected
                  </span>
                )}
              </>
            )}
          </div>

          {isSelectionMode && selectedProducts.length > 0 && (
            <button
              className="btn-delete"
              onClick={handleDeleteSelectedProducts}
              style={{ padding: '8px 16px' }}
            >
              <Trash2 size={16} style={{ marginRight: '5px' }} />
              Delete Selected ({selectedProducts.length})
            </button>
          )}
        </div>
      )}

      <div className="products-grid">
        {filteredProducts.length === 0 && searchTerm ? (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            background: 'white',
            borderRadius: '8px',
            color: '#666'
          }}>
            <p style={{ fontSize: '18px', margin: '0 0 10px 0' }}>🔍 No products found</p>
            <p style={{ margin: '0' }}>Try searching with different keywords or clear the search to see all products.</p>
          </div>
        ) : filteredProducts.length === 0 ? (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            background: 'white',
            borderRadius: '8px',
            color: '#666'
          }}>
            <p style={{ fontSize: '18px', margin: '0 0 10px 0' }}>📦 No products yet</p>
            <p style={{ margin: '0' }}>Add your first product or upload a file to get started!</p>
          </div>
        ) : (
          filteredProducts.map(product => (
          <div key={product.id} className="product-card" style={{
            position: 'relative',
            border: isSelectionMode && selectedProducts.includes(product.id) ? '2px solid #007bff' : undefined,
            background: isSelectionMode && selectedProducts.includes(product.id) ? '#f8f9ff' : undefined
          }}>
            {/* Selection Checkbox */}
            {isSelectionMode && (
              <div style={{
                position: 'absolute',
                top: '10px',
                left: '10px',
                zIndex: 10
              }}>
                <input
                  type="checkbox"
                  checked={selectedProducts.includes(product.id)}
                  onChange={() => toggleProductSelection(product.id)}
                  style={{
                    width: '18px',
                    height: '18px',
                    cursor: 'pointer'
                  }}
                />
              </div>
            )}

            <div className="product-header" style={{ marginLeft: isSelectionMode ? '30px' : '0' }}>
                  <h4>{product.product_name || product.productName}</h4>
                  <span className="product-status active">Active</span>
                </div>
                <div className="product-details">
                  <div className="product-key-values" style={{ marginBottom: '10px' }}>
                    {Object.entries(product.product_details || product.productDetails || {}).map(([key, value]) => (
                      <div key={key} style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: '4px 0',
                        borderBottom: '1px solid #eee'
                      }}>
                        <strong style={{ color: '#666', textTransform: 'capitalize' }}>{key}:</strong>
                        <span>{value}</span>
                      </div>
                    ))}
                    {Object.keys(product.product_details || product.productDetails || {}).length === 0 && (
                      <p style={{ color: '#999', fontStyle: 'italic' }}>No product details</p>
                    )}
                  </div>
                  <div className="product-aliases">
                    {(product.alias_names || product.aliasNames || []).map((alias, index) => (
                      <span
                        key={index}
                        className="alias-tag"
                        style={{
                          background: '#e3f2fd',
                          color: '#1976d2',
                          padding: '2px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          marginRight: '5px',
                          display: 'inline-block',
                          marginBottom: '5px'
                        }}
                      >
                        {alias}
                      </span>
                    ))}
                  </div>
                </div>
                {!isSelectionMode && (
                  <div className="product-actions">
                    <button
                      className="btn-edit"
                      onClick={() => startModalEdit(product)}
                      title="Edit this product"
                    >
                      <Edit size={16} />
                      Edit
                    </button>
                    <button
                      className="btn-delete"
                      onClick={() => handleDeleteProduct(product.id)}
                      title="Delete this product"
                    >
                      <Trash2 size={16} />
                      Delete
                    </button>
                  </div>
                )}

                {isSelectionMode && (
                  <div style={{
                    textAlign: 'center',
                    padding: '10px',
                    color: selectedProducts.includes(product.id) ? '#007bff' : '#666',
                    fontSize: '14px',
                    fontWeight: selectedProducts.includes(product.id) ? 'bold' : 'normal'
                  }}>
                    {selectedProducts.includes(product.id) ? '✓ Selected' : 'Click checkbox to select'}
                  </div>
                )}
          </div>
          ))
        )}
      </div>

      <div className="catalog-info">
        <div className="info-card">
          <h3>Catalog Usage</h3>
          <p>{products.length}/10 Products</p>
          <div className="progress-bar">
            <div className="progress-fill" style={{ width: `${(products.length/10)*100}%` }}></div>
          </div>
        </div>
      </div>

      {/* Edit Product Modal */}
      {showEditModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
          }}>
            {/* Modal Header */}
            <div style={{
              padding: '20px 24px',
              borderBottom: '1px solid #e1e5e9',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              borderRadius: '12px 12px 0 0'
            }}>
              <div>
                <h2 style={{ margin: 0, fontSize: '20px', fontWeight: '600' }}>
                  ✏️ Edit Product
                </h2>
                <p style={{ margin: '4px 0 0 0', fontSize: '14px', opacity: 0.9 }}>
                  {editingModalProduct?.product_name || editingModalProduct?.productName}
                </p>
              </div>
              <button
                onClick={cancelModalEdit}
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  border: 'none',
                  color: 'white',
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  cursor: 'pointer',
                  fontSize: '18px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                ×
              </button>
            </div>

            {/* Modal Body */}
            <div style={{ padding: '24px' }}>
              {/* Product Name Section */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  marginBottom: '8px',
                  color: '#2c3e50',
                  fontSize: '14px'
                }}>
                  📝 Product Name
                </label>
                <input
                  type="text"
                  value={inlineEditData.productName || ''}
                  onChange={(e) => setInlineEditData({...inlineEditData, productName: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px',
                    fontSize: '16px',
                    fontWeight: '500',
                    outline: 'none',
                    transition: 'border-color 0.2s ease'
                  }}
                  placeholder="Enter product name..."
                  onFocus={(e) => e.target.style.borderColor = '#667eea'}
                  onBlur={(e) => e.target.style.borderColor = '#e1e5e9'}
                />
              </div>

              {/* Product Details Section */}
              <div style={{ marginBottom: '24px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <label style={{
                    fontWeight: '600',
                    color: '#2c3e50',
                    fontSize: '14px'
                  }}>
                    🏷️ Product Details ({Object.keys(inlineEditData.productDetails || {}).length})
                  </label>
                  <button
                    onClick={() => {
                      const newDetails = {...inlineEditData.productDetails};
                      const newKey = `detail_${Date.now()}`;
                      newDetails[newKey] = '';
                      setInlineEditData({...inlineEditData, productDetails: newDetails});
                    }}
                    style={{
                      background: '#28a745',
                      color: 'white',
                      border: 'none',
                      padding: '8px 16px',
                      borderRadius: '6px',
                      fontSize: '13px',
                      cursor: 'pointer',
                      fontWeight: '500'
                    }}
                  >
                    + Add Detail
                  </button>
                </div>

                <div style={{
                  maxHeight: '300px',
                  overflowY: 'auto',
                  border: '1px solid #e1e5e9',
                  borderRadius: '8px',
                  padding: '16px'
                }}>
                  {Object.keys(inlineEditData.productDetails || {}).length === 0 ? (
                    <div style={{
                      textAlign: 'center',
                      padding: '40px 20px',
                      color: '#6c757d',
                      fontStyle: 'italic'
                    }}>
                      <div style={{ fontSize: '48px', marginBottom: '16px' }}>📦</div>
                      <p>No product details yet</p>
                      <p style={{ fontSize: '14px' }}>Click "Add Detail" to start adding product information</p>
                    </div>
                  ) : (
                    Object.entries(inlineEditData.productDetails || {}).map(([key, value], index) => (
                      <div key={`${key}-${index}`} style={{
                        display: 'flex',
                        gap: '12px',
                        marginBottom: '12px',
                        padding: '12px',
                        background: '#f8f9fa',
                        borderRadius: '8px',
                        border: '1px solid #e9ecef'
                      }}>
                        <input
                          type="text"
                          value={key}
                          onChange={(e) => {
                            const newDetails = {...inlineEditData.productDetails};
                            const oldValue = newDetails[key];
                            delete newDetails[key];
                            if (e.target.value.trim()) {
                              newDetails[e.target.value] = oldValue;
                            }
                            setInlineEditData({...inlineEditData, productDetails: newDetails});
                          }}
                          placeholder="Key (e.g., price, brand)"
                          style={{
                            flex: 1,
                            padding: '10px 12px',
                            border: '1px solid #ced4da',
                            borderRadius: '6px',
                            fontSize: '14px',
                            outline: 'none'
                          }}
                        />
                        <input
                          type="text"
                          value={value || ''}
                          onChange={(e) => {
                            const newDetails = {...inlineEditData.productDetails};
                            newDetails[key] = e.target.value;
                            setInlineEditData({...inlineEditData, productDetails: newDetails});
                          }}
                          placeholder="Value (e.g., ₹999, Apple)"
                          style={{
                            flex: 2,
                            padding: '10px 12px',
                            border: '1px solid #ced4da',
                            borderRadius: '6px',
                            fontSize: '14px',
                            outline: 'none'
                          }}
                        />
                        <button
                          onClick={() => {
                            const newDetails = {...inlineEditData.productDetails};
                            delete newDetails[key];
                            setInlineEditData({...inlineEditData, productDetails: newDetails});
                          }}
                          style={{
                            background: '#dc3545',
                            color: 'white',
                            border: 'none',
                            padding: '10px 12px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '14px'
                          }}
                          title="Remove this detail"
                        >
                          🗑️
                        </button>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Aliases Section */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontWeight: '600',
                  marginBottom: '8px',
                  color: '#2c3e50',
                  fontSize: '14px'
                }}>
                  🏷️ Alias Names
                </label>
                <input
                  type="text"
                  value={inlineEditData.aliasNames || ''}
                  onChange={(e) => setInlineEditData({...inlineEditData, aliasNames: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'border-color 0.2s ease'
                  }}
                  placeholder="e.g., Coffee Cup, Mug, Ceramic Cup"
                  onFocus={(e) => e.target.style.borderColor = '#667eea'}
                  onBlur={(e) => e.target.style.borderColor = '#e1e5e9'}
                />
                <small style={{ color: '#6c757d', fontSize: '12px', marginTop: '6px', display: 'block' }}>
                  Separate multiple aliases with commas. These help in search and voice recognition.
                </small>
              </div>
            </div>

            {/* Modal Footer */}
            <div style={{
              padding: '20px 24px',
              borderTop: '1px solid #e1e5e9',
              display: 'flex',
              gap: '12px',
              justifyContent: 'flex-end',
              background: '#f8f9fa',
              borderRadius: '0 0 12px 12px'
            }}>
              <button
                onClick={cancelModalEdit}
                style={{
                  background: '#6c757d',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                ❌ Cancel
              </button>
              <button
                onClick={saveModalEdit}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                ✅ Save Changes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderSupport = () => (
    <div className="support-section">
      <div className="section-header">
        <h2>Email Support</h2>
        <div className="support-contact">
          <Mail size={16} />
          <span><EMAIL></span>
        </div>
      </div>

      <div className="support-stats">
        <div className="support-stat">
          <h4>Open Tickets</h4>
          <p>{supportTickets.filter(t => t.status === 'Open').length}</p>
        </div>
        <div className="support-stat">
          <h4>Average Response Time</h4>
          <p>2.5 hours</p>
        </div>
        <div className="support-stat">
          <h4>Resolution Rate</h4>
          <p>94%</p>
        </div>
      </div>

      <div className="create-ticket">
        <h3>Create New Ticket</h3>
        <div className="ticket-form">
          <input
            type="text"
            placeholder="Subject"
            value={newTicket.subject}
            onChange={(e) => setNewTicket({...newTicket, subject: e.target.value})}
            className="form-input"
          />
          <textarea
            placeholder="Describe your issue in detail..."
            value={newTicket.description}
            onChange={(e) => setNewTicket({...newTicket, description: e.target.value})}
            className="form-textarea"
            rows="4"
          ></textarea>
          <select
            value={newTicket.priority}
            onChange={(e) => setNewTicket({...newTicket, priority: e.target.value})}
            className="form-select"
          >
            <option value="Low">Low Priority</option>
            <option value="Medium">Medium Priority</option>
            <option value="High">High Priority</option>
            <option value="Urgent">Urgent Priority</option>
          </select>
          <button className="btn-primary" onClick={handleCreateTicket}>
            <AlertCircle size={16} />
            Create Ticket
          </button>
        </div>
      </div>

      <div className="tickets-list">
        <h3>Your Support Tickets</h3>
        <div className="tickets-table">
          <div className="table-container">
            <table>
            <thead>
              <tr>
                <th>Ticket ID</th>
                <th>Subject</th>
                <th>Priority</th>
                <th>Status</th>
                <th>Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {supportTickets.map(ticket => (
                <tr key={ticket.id}>
                  <td>#{ticket.id.toString().padStart(4, '0')}</td>
                  <td>{ticket.subject}</td>
                  <td>
                    <span className={`priority ${(ticket.priority || 'medium').toLowerCase()}`}>
                      {ticket.priority || 'Medium'}
                    </span>
                  </td>
                  <td>
                    <span className={`ticket-status ${(ticket.status || 'open').toLowerCase().replace(' ', '-')}`}>
                      {ticket.status || 'Open'}
                    </span>
                  </td>
                  <td>{ticket.date}</td>
                  <td>
                    <button
                      className="btn-secondary"
                      onClick={() => {
                        setSelectedTicket(ticket);
                        setShowTicketChat(true);
                        fetchTicketMessages(ticket.id);
                      }}
                      style={{ padding: '4px 8px', fontSize: '12px' }}
                    >
                      <MessageSquare size={14} style={{ marginRight: '4px' }} />
                      Chat
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          </div>
        </div>
      </div>

      {/* Support Ticket Chat Modal */}
      {showTicketChat && selectedTicket && (
        <div className="modal-overlay">
          <div className="modal-container" style={{ maxWidth: '800px', height: '600px',background: 'white' }}>
            <div className="modal-header">
              <h2 className="modal-title">
                <MessageSquare className="w-5 h-5" />
                Chat - {selectedTicket.subject}
              </h2>
              <button onClick={() => setShowTicketChat(false)} className="modal-close">
                <X size={20} />
              </button>
            </div>

            <div className="modal-body" style={{ display: 'flex', flexDirection: 'column', height: '500px' }}>
              {/* Ticket Info */}
              <div style={{
                background: '#f8f9fa',
                padding: '12px',
                borderRadius: '8px',
                marginBottom: '16px',
                border: '1px solid #e9ecef'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <strong>Priority:</strong>
                    <span className={`priority ${(selectedTicket.priority || 'medium').toLowerCase()}`} style={{ marginLeft: '8px' }}>
                      {selectedTicket.priority || 'Medium'}
                    </span>
                  </div>
                  <div>
                    <strong>Status:</strong>
                    <span className={`ticket-status ${(selectedTicket.status || 'open').toLowerCase().replace(' ', '-')}`} style={{ marginLeft: '8px' }}>
                      {selectedTicket.status || 'Open'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div style={{
                flex: 1,
                overflowY: 'auto',
                padding: '16px',
                background: '#f8f9fa',
                borderRadius: '8px',
                marginBottom: '16px'
              }}>
                {ticketMessages.map((msg, index) => (
                  <div key={index} style={{
                    marginBottom: '16px',
                    display: 'flex',
                    justifyContent: msg.senderType === 'client' ? 'flex-end' : 'flex-start'
                  }}>
                    <div style={{
                      maxWidth: '70%',
                      padding: '12px',
                      borderRadius: '12px',
                      background: msg.senderType === 'client' ? '#007bff' : '#e9ecef',
                      color: msg.senderType === 'client' ? 'white' : 'black'
                    }}>
                      <div style={{ fontSize: '12px', marginBottom: '6px', opacity: 0.8 }}>
                        {msg.senderName} - {new Date(msg.createdAt).toLocaleString()}
                      </div>
                      <div>{msg.message}</div>
                    </div>
                  </div>
                ))}

                {ticketMessages.length === 0 && (
                  <div style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
                    No messages yet. Start the conversation!
                  </div>
                )}
              </div>

              {/* Message Input */}
              <div style={{ display: 'flex', gap: '12px' }}>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="form-input"
                  style={{ flex: 1 }}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && newMessage.trim()) {
                      sendTicketMessage(selectedTicket.id, newMessage);
                    }
                  }}
                />
                <button
                  onClick={() => {
                    if (newMessage.trim()) {
                      sendTicketMessage(selectedTicket.id, newMessage);
                    }
                  }}
                  className="btn-primary"
                  disabled={!newMessage.trim()}
                  style={{ minWidth: '80px' }}
                >
                  Send
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const handleLogout = async () => {
    try {
      await logout();
      // Redirect will be handled by AuthContext
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Render profile section
  const renderProfile = () => (
    <div className="profile-section">
      <div className="section-header">
        <div className="header-content">
          <div className="header-icon">
            <User size={32} />
          </div>
          <div className="header-text">
            <h2>Profile Management</h2>
            <p>Manage your business profile and settings</p>
          </div>
        </div>
        <button
          className={`btn-primary ${isEditingProfile ? 'btn-cancel' : ''}`}
          onClick={() => {
            if (!isEditingProfile) {
              // Populate form with current data
              setProfileForm({
                businessName: dashboardData?.profile?.user?.businessName || '',
                ownerName: dashboardData?.profile?.user?.ownerName || '',
                businessEmail: dashboardData?.profile?.user?.businessEmail || '',
                mobileNumber: dashboardData?.profile?.user?.mobileNumber || '',
                whatsappNumber: dashboardData?.profile?.user?.whatsappNumber || '',
                businessAddress: dashboardData?.profile?.user?.businessAddress || '',
                businessDescription: dashboardData?.profile?.user?.businessSummary || ''
              });
            }
            setIsEditingProfile(!isEditingProfile);
          }}
        >
          {isEditingProfile ? <X size={16} /> : <Edit size={16} />}
          {isEditingProfile ? 'Cancel' : 'Edit Profile'}
        </button>
      </div>

      <div className="profile-cards">
        <div className="profile-card main-card">
          <div className="card-header">
            <div className="card-icon">
              <Building size={24} />
            </div>
            <h3>Business Information</h3>
          </div>
          {isEditingProfile ? (
            <div className="profile-form">
              <div className="form-row">
                <div className="form-group">
                  <label><Building size={16} /> Business Name:</label>
                  <input
                    type="text"
                    value={profileForm.businessName}
                    onChange={(e) => setProfileForm({...profileForm, businessName: e.target.value})}
                    placeholder="Enter business name"
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label><User size={16} /> Owner Name:</label>
                  <input
                    type="text"
                    value={profileForm.ownerName}
                    onChange={(e) => setProfileForm({...profileForm, ownerName: e.target.value})}
                    placeholder="Enter owner name"
                    className="form-input"
                  />
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label><Mail size={16} /> Business Email:</label>
                  <input
                    type="email"
                    value={profileForm.businessEmail}
                    onChange={(e) => setProfileForm({...profileForm, businessEmail: e.target.value})}
                    placeholder="Enter business email"
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label><Phone size={16} /> Mobile Number:</label>
                  <input
                    type="tel"
                    value={profileForm.mobileNumber}
                    onChange={(e) => setProfileForm({...profileForm, mobileNumber: e.target.value})}
                    placeholder="Enter mobile number"
                    className="form-input"
                  />
                </div>
              </div>
              <div className="form-group">
                <label><MessageSquare size={16} /> WhatsApp Number:</label>
                <input
                  type="tel"
                  value={profileForm.whatsappNumber}
                  onChange={(e) => setProfileForm({...profileForm, whatsappNumber: e.target.value})}
                  placeholder="Enter WhatsApp number"
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label><MapPin size={16} /> Business Address:</label>
                <textarea
                  value={profileForm.businessAddress}
                  onChange={(e) => setProfileForm({...profileForm, businessAddress: e.target.value})}
                  placeholder="Enter business address"
                  className="form-textarea"
                  rows="3"
                />
              </div>
              <div className="form-group">
                <label><FileText size={16} /> Business Description:</label>
                <textarea
                  value={profileForm.businessDescription}
                  onChange={(e) => setProfileForm({...profileForm, businessDescription: e.target.value})}
                  placeholder="Describe your business"
                  className="form-textarea"
                  rows="4"
                />
              </div>
              <div className="form-actions">
                <button className="btn-primary save-btn" onClick={handleProfileUpdate}>
                  <CheckCircle size={16} />
                  Save Changes
                </button>
                <button
                  className="btn-secondary cancel-btn"
                  onClick={() => setIsEditingProfile(false)}
                >
                  <X size={16} />
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="profile-info">
              <div className="info-grid">
                <div className="info-item">
                  <div className="info-icon">
                    <Building size={20} />
                  </div>
                  <div className="info-content">
                    <label>Business Name</label>
                    <span>{dashboardData?.profile?.user?.businessName || 'Not provided'}</span>
                  </div>
                </div>
                <div className="info-item">
                  <div className="info-icon">
                    <User size={20} />
                  </div>
                  <div className="info-content">
                    <label>Owner Name</label>
                    <span>{dashboardData?.profile?.user?.ownerName || 'Not provided'}</span>
                  </div>
                </div>
                <div className="info-item">
                  <div className="info-icon">
                    <Mail size={20} />
                  </div>
                  <div className="info-content">
                    <label>Business Email</label>
                    <span>{dashboardData?.profile?.user?.businessEmail || 'Not provided'}</span>
                  </div>
                </div>
                <div className="info-item">
                  <div className="info-icon">
                    <Phone size={20} />
                  </div>
                  <div className="info-content">
                    <label>Mobile Number</label>
                    <span>{dashboardData?.profile?.user?.mobileNumber || 'Not provided'}</span>
                  </div>
                </div>
                <div className="info-item">
                  <div className="info-icon">
                    <MessageSquare size={20} />
                  </div>
                  <div className="info-content">
                    <label>WhatsApp Number</label>
                    <span>{dashboardData?.profile?.user?.whatsappNumber || 'Not provided'}</span>
                  </div>
                </div>
                <div className="info-item full-width">
                  <div className="info-icon">
                    <MapPin size={20} />
                  </div>
                  <div className="info-content">
                    <label>Business Address</label>
                    <span>{dashboardData?.profile?.user?.businessAddress || 'Not provided'}</span>
                  </div>
                </div>
                <div className="info-item full-width">
                  <div className="info-icon">
                    <FileText size={20} />
                  </div>
                  <div className="info-content">
                    <label>Business Description</label>
                    <span>{dashboardData?.profile?.user?.businessSummary || 'Not provided'}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="profile-card settings-card">
          <div className="card-header">
            <div className="card-icon">
              <Settings size={24} />
            </div>
            <h3>Account Settings</h3>
          </div>
          <div className="settings-content">
            <div className="setting-item">
              <div className="setting-info">
                <div className="setting-icon">
                  <Shield size={20} />
                </div>
                <div className="setting-text">
                  <h4>Password Security</h4>
                  <p>Update your account password</p>
                </div>
              </div>
              <button
                className="btn-secondary"
                onClick={() => window.location.href = '/?showReset=true'}
              >
                <Key size={16} />
                Reset Password
              </button>
            </div>
            <div className="setting-item">
              <div className="setting-info">
                <div className="setting-icon">
                  <LogOut size={20} />
                </div>
                <div className="setting-text">
                  <h4>Sign Out</h4>
                  <p>Sign out from your account</p>
                </div>
              </div>
              <button className="btn-danger" onClick={handleLogout}>
                <LogOut size={16} />
                Logout
              </button>
            </div>
          </div>
        </div>

        <div className="profile-card phone-card">
          <div className="card-header">
            <div className="card-icon">
              <Phone size={24} />
            </div>
            <h3>Assigned Phone Number</h3>
          </div>
          <div className="phone-number-content">
            {dashboardData?.profile?.user?.assignedPlivoNumber ? (
              <div className="assigned-number-display">
                <div className="number-badge">
                  <Phone size={32} />
                  <div className="number-info">
                    <span className="number">{dashboardData.profile.user.assignedPlivoNumber}</span>
                    <span className="status active">
                      <CheckCircle size={16} />
                      Active & Ready
                    </span>
                  </div>
                </div>
                <div className="number-features">
                  <div className="feature">
                    <Phone size={16} />
                    <span>Voice Calls</span>
                  </div>
                  <div className="feature">
                    <MessageSquare size={16} />
                    <span>SMS Messages</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="no-number">
                <div className="no-number-icon">
                  <AlertCircle size={48} />
                </div>
                <div className="no-number-content">
                  <h4>No Phone Number Assigned</h4>
                  <p>Contact our support team to get a dedicated phone number for your voice bot</p>
                  <button className="btn-primary contact-support">
                    <Mail size={16} />
                    Contact Support
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  // Render SMS logs section
  const renderSMSLogs = () => (
    <div className="sms-logs-section">
      <div className="section-header">
        <h2>SMS Logs</h2>
        <p>Track all SMS messages sent through your voice bot</p>
      </div>

      <div className="sms-stats">
        <div className="stat-card">
          <MessageSquare size={24} />
          <div className="stat-info">
            <h3>{dashboardData?.overview?.todayStats?.sms || 0}</h3>
            <p>SMS Today</p>
          </div>
        </div>
        <div className="stat-card">
          <TrendingUp size={24} />
          <div className="stat-info">
            <h3>{dashboardData?.overview?.weekStats?.sms || 0}</h3>
            <p>SMS This Week</p>
          </div>
        </div>
        <div className="stat-card">
          <Activity size={24} />
          <div className="stat-info">
            <h3>{dashboardData?.overview?.planInfo?.usage?.sms?.used || 0}</h3>
            <p>Total SMS Used</p>
          </div>
        </div>
      </div>

      <div className="sms-logs-table">
        <div className="table-header">
          <h3>Recent SMS Messages</h3>
          <button className="btn-secondary">View All</button>
        </div>
        {smsLogs.length > 0 ? (
          <div className="table-container" style={{ marginTop: '15px' }}>
            <table>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Recipient</th>
                <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Content</th>
                <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Attachments</th>
                <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Status</th>
                <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Time</th>
              </tr>
            </thead>
            <tbody>
              {smsLogs.map(sms => (
                <tr key={sms.id}>
                  <td style={{ padding: '12px', border: '1px solid #ddd' }}>{sms.recipientNumber}</td>
                  <td style={{ padding: '12px', border: '1px solid #ddd' }}>
                    {sms.messageContent?.substring(0, 50) || sms.smsContent?.substring(0, 50)}...
                  </td>
                  <td style={{ padding: '12px', border: '1px solid #ddd' }}>
                    {sms.pdfFiles && sms.pdfFiles.length > 0 ? (
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                        {sms.pdfFiles.map((pdf, index) => (
                          <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <FileText size={16} style={{ color: '#dc3545' }} />
                            <a
                              href={pdf.storage_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              style={{
                                color: '#007bff',
                                textDecoration: 'none',
                                fontSize: '12px',
                                maxWidth: '120px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                              title={pdf.file_name}
                            >
                              {pdf.file_name}
                            </a>
                            <span style={{ fontSize: '10px', color: '#6c757d' }}>
                              ({(pdf.file_size / 1024).toFixed(1)}KB)
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : sms.totalPdfAttachments > 0 || sms.total_pdf_attachments > 0 ? (
                      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                        <FileText size={16} style={{ color: '#dc3545' }} />
                        <span style={{ fontSize: '12px', color: '#6c757d' }}>
                          {sms.totalPdfAttachments || sms.total_pdf_attachments} PDF(s)
                        </span>
                      </div>
                    ) : (
                      <span style={{ fontSize: '12px', color: '#6c757d' }}>No attachments</span>
                    )}
                  </td>
                  <td style={{ padding: '12px', border: '1px solid #ddd' }}>
                    <span className={`status ${sms.status?.toLowerCase() || 'sent'}`}>
                      {sms.status || 'Sent'}
                    </span>
                  </td>
                  <td style={{ padding: '12px', border: '1px solid #ddd' }}>
                    {new Date(sms.smsTimestamp).toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          </div>
        ) : (
          <div className="table-placeholder" style={{
            textAlign: 'center',
            padding: '40px',
            color: '#6c757d'
          }}>
            <MessageSquare size={48} />
            <p>No SMS logs yet</p>
            <small>SMS logs will appear here once you start sending messages</small>
          </div>
        )}
      </div>
    </div>
  );

  const renderBilling = () => {
    console.log('renderBilling called', { subscription, availablePlans, paymentHistory });
    const currentPlan = subscription?.subscription_plans;
    const isFreePlan = !subscription || currentPlan?.name === 'free';

    // Check if subscription is expired
    const isExpired = subscription && subscription.ends_at && new Date(subscription.ends_at) < new Date();
    const hasActiveSubscription = subscription && subscription.status === 'active' && !isExpired;

    // Show loading state if data is not loaded yet
    if (!availablePlans || availablePlans.length === 0) {
      return (
        <div className="billing-section">
          <div className="section-header">
            <h2>Billing & Subscription</h2>
            <p>Loading billing information...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="billing-section">
        <div className="section-header">
          <h2>Billing & Subscription</h2>
          <p>Manage your subscription and payment details</p>
        </div>

        {/* Current Plan Card */}
        <div className="billing-cards">
          <div className="billing-card current-plan">
            <div className="card-header">
              <div className="plan-icon">
                {isFreePlan ? <Clock size={24} /> : <Crown size={24} />}
              </div>
              <div className="plan-info">
                <h3>{currentPlan?.display_name || 'Free Plan'}</h3>
                <p>{currentPlan?.description || 'Basic features for testing'}</p>
                {isExpired && (
                  <div style={{ color: '#dc3545', fontWeight: 'bold', marginTop: '8px' }}>
                    ⚠️ Plan Expired - Please recharge to continue
                  </div>
                )}
              </div>
              <div className="plan-price">
                <span className="price">
                  {currentPlan?.price ? `₹${currentPlan.price.toLocaleString()}` : 'Free'}
                </span>
                {currentPlan?.price && <span className="period">/month</span>}
              </div>
            </div>

            <div className="plan-features">
              <div className="feature-item">
                <CheckCircle size={16} />
                <span>Calls: {currentPlan?.max_calls === -1 ? 'Unlimited' : (currentPlan?.max_calls || 10)}/month</span>
              </div>
              <div className="feature-item">
                <CheckCircle size={16} />
                <span>Products: {currentPlan?.max_products === -1 ? 'Unlimited' : (currentPlan?.max_products || 5)}</span>
              </div>
              <div className="feature-item">
                <CheckCircle size={16} />
                <span>Support: {currentPlan?.features?.support || 'Email'}</span>
              </div>
            </div>

            {subscription?.ends_at && (
              <div className="renewal-info">
                <Calendar size={16} />
                <span>
                  {isExpired
                    ? `Expired on ${new Date(subscription.ends_at).toLocaleDateString()}`
                    : `Renews on ${new Date(subscription.ends_at).toLocaleDateString()}`
                  }
                </span>
              </div>
            )}
          </div>

          {/* Payment Stats */}
          <div className="billing-card payment-stats">
            <h3>Payment Summary</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">Total Spent</span>
                <span className="stat-value">
                  ₹{paymentHistory.filter(p => p.status === 'paid').reduce((sum, p) => sum + (p.amount || 0), 0).toLocaleString()}
                </span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Payments Made</span>
                <span className="stat-value">{paymentHistory.filter(p => p.status === 'paid').length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Status</span>
                <span className={`stat-value status ${subscription?.status || 'free'}`}>
                  {subscription?.status === 'active' ? 'Active' : 'Free'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Available Plans */}
        <div className="available-plans">
          <h3>Available Plans</h3>
          <div className="plans-grid">
            {availablePlans.filter(plan => plan.name !== 'free').map((plan) => {
              const isCurrentPlan = currentPlan?.id === plan.id && !isExpired;
              const canUpgrade = !isCurrentPlan && (
                isExpired || // If expired, allow all plans
                !currentPlan ||
                plan.sort_order > (currentPlan.sort_order || 0)
              );

              return (
                <div key={plan.id} className={`plan-card ${isCurrentPlan ? 'current' : ''} ${plan.name === 'pro' ? 'popular' : ''}`}>
                  {plan.name === 'pro' && <div className="popular-badge">Most Popular</div>}

                  <div className="plan-header">
                    <h4>{plan.display_name}</h4>
                    <div className="plan-price">
                      <span className="price">₹{plan.price.toLocaleString()}</span>
                      <span className="period">/month</span>
                    </div>
                  </div>

                  <p className="plan-description">{plan.description}</p>

                  <div className="plan-features">
                    <div className="feature">
                      <CheckCircle size={14} />
                      <span>{plan.max_calls === -1 ? 'Unlimited' : plan.max_calls} calls/month</span>
                    </div>
                    <div className="feature">
                      <CheckCircle size={14} />
                      <span>{plan.max_products === -1 ? 'Unlimited' : plan.max_products} products</span>
                    </div>
                    <div className="feature">
                      <CheckCircle size={14} />
                      <span>{plan.features?.support || 'Email'} support</span>
                    </div>
                  </div>

                  <div className="plan-action">
                    {isCurrentPlan ? (
                      <button className="btn-current" disabled>
                        <CheckCircle size={16} />
                        Current Plan
                      </button>
                    ) : canUpgrade ? (
                      <button
                        className="btn-upgrade"
                        onClick={() => handleUpgrade(plan)}
                      >
                        <TrendingUp size={16} />
                        {isExpired ? 'Recharge Now' : 'Upgrade Now'}
                      </button>
                    ) : (
                      <button className="btn-disabled" disabled>
                        Lower Plan
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Payment History */}
        <div className="payment-history">
          <h3>Recent Payments</h3>
          {paymentHistory.length > 0 ? (
            <div className="payments-table">
              <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Plan</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Payment ID</th>
                  </tr>
                </thead>
                <tbody>
                  {paymentHistory.slice(0, 5).map((payment) => (
                    <tr key={payment.id}>
                      <td>{new Date(payment.created_at).toLocaleDateString()}</td>
                      <td>{payment.client_subscriptions?.subscription_plans?.display_name || 'N/A'}</td>
                      <td>₹{payment.amount?.toLocaleString()}</td>
                      <td>
                        <span className={`payment-status ${payment.status}`}>
                          {payment.status === 'paid' ? 'Paid' : payment.status}
                        </span>
                      </td>
                      <td className="payment-id">{payment.razorpay_payment_id || 'N/A'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="no-payments">
              <CreditCard size={48} />
              <p>No payment history yet</p>
              <small>Your payment transactions will appear here</small>
            </div>
          )}
        </div>

        {/* Free Plan CTA */}
        {isFreePlan && (
          <div className="upgrade-cta">
            <div className="cta-content">
              <Zap size={32} />
              <div className="cta-text">
                <h3>Ready to scale your business?</h3>
                <p>Upgrade to Pro and unlock advanced features with 500 calls per month</p>
              </div>
              <button
                className="cta-button"
                onClick={() => {
                  const proPlan = availablePlans.find(p => p.name === 'pro');
                  if (proPlan) handleUpgrade(proPlan);
                }}
              >
                <Crown size={16} />
                Upgrade to Pro
              </button>
            </div>
          </div>
        )}

        {/* Payment Modals */}
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={handlePaymentModalClose}
          selectedPlan={selectedPlan}
          onSuccess={handlePaymentSuccess}
        />

        <PaymentSuccessModal
          isOpen={showSuccessModal}
          onClose={handleSuccessModalClose}
          paymentData={paymentResult}
        />
      </div>
    );
  };

  // Generate registration link for customers
  const generateRegistrationLink = async (phoneNumber, callLogId = null) => {
    try {
      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/customers/generate-registration-link', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phoneNumber,
          callLogId,
          expiryHours: 24
        })
      });

      if (response.ok) {
        const data = await response.json();
        setGeneratedLink(data.registrationLink);
        setShowRegistrationLinkModal(true);
        return data.registrationLink;
      } else {
        alert('Failed to generate registration link');
      }
    } catch (error) {
      console.error('Error generating registration link:', error);
      alert('Failed to generate registration link');
    }
  };

  // Copy link to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Link copied to clipboard!');
    });
  };

  // Render customers section
  const renderCustomers = () => (
    <div className="customers-section" style={{ backgroundColor: 'white', padding: '2rem', borderRadius: '0', fontFamily: "'Poppins', sans-serif" }}>
      <div className="section-header" style={{ borderBottom: '2px solid #e5e7eb', paddingBottom: '1rem', marginBottom: '2rem' }}>
        <h2 style={{ fontWeight: 700, fontSize: '2rem', color: '#1f2937' }}>My Customers</h2>
        <p style={{ color: '#4b5563', fontSize: '1.125rem' }}>View and manage customers who have interacted with your business</p>
      </div>

      {/* Debug Info */}
      <div style={{ backgroundColor: '#fef3c7', border: '1px solid #f59e0b', borderRadius: '4px', padding: '1rem', marginBottom: '2rem' }}>
        <strong>✅ Customer Management System Active!</strong> This shows customers who have called and interacted with your business.
      </div>

      {/* Mock Customer Data */}
      {(() => {
        const mockCustomers = [
          {
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+91 9876543210',
            isActive: true,
            interactionCount: 3,
            lastInteractionAt: '2024-01-20T10:30:00Z',
            conversationSummary: 'Interested in gaming laptops, discussed budget of ₹80,000',
            productInterests: ['Gaming Laptop', 'Mouse', 'Keyboard'],
            estimatedValue: 85000
          },
          {
            id: '2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: '+91 9876543211',
            isActive: true,
            interactionCount: 1,
            lastInteractionAt: '2024-01-19T15:45:00Z',
            conversationSummary: 'Looking for office furniture, needs ergonomic chair',
            productInterests: ['Office Chair', 'Desk'],
            estimatedValue: 25000
          },
          {
            id: '3',
            name: 'Mike Johnson',
            email: '<EMAIL>',
            phone: '+91 9876543212',
            isActive: false,
            interactionCount: 2,
            lastInteractionAt: '2024-01-15T09:20:00Z',
            conversationSummary: 'Inquired about mobile phones, price sensitive',
            productInterests: ['Smartphone', 'Phone Case'],
            estimatedValue: 15000
          }
        ];

        return (
          <>
            {/* Customer Statistics */}
            <div className="customer-stats" style={{ marginBottom: '2rem' }}>
              <div className="stats-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
                <div className="stat-card" style={{ backgroundColor: '#f0f9ff', borderRadius: '8px', padding: '1.5rem', textAlign: 'center' }}>
                  <div style={{ color: '#0369a1', fontSize: '2rem', fontWeight: 700 }}>{mockCustomers.length}</div>
                  <div style={{ color: '#0369a1', fontSize: '0.875rem', fontWeight: 500 }}>Total Customers</div>
                </div>
                <div className="stat-card" style={{ backgroundColor: '#f0fdf4', borderRadius: '8px', padding: '1.5rem', textAlign: 'center' }}>
                  <div style={{ color: '#166534', fontSize: '2rem', fontWeight: 700 }}>{mockCustomers.filter(c => c.isActive).length}</div>
                  <div style={{ color: '#166534', fontSize: '0.875rem', fontWeight: 500 }}>Active Customers</div>
                </div>
                <div className="stat-card" style={{ backgroundColor: '#fefce8', borderRadius: '8px', padding: '1.5rem', textAlign: 'center' }}>
                  <div style={{ color: '#a16207', fontSize: '2rem', fontWeight: 700 }}>{mockCustomers.reduce((sum, c) => sum + c.interactionCount, 0)}</div>
                  <div style={{ color: '#a16207', fontSize: '0.875rem', fontWeight: 500 }}>Total Interactions</div>
                </div>
                <div className="stat-card" style={{ backgroundColor: '#fdf2f8', borderRadius: '8px', padding: '1.5rem', textAlign: 'center' }}>
                  <div style={{ color: '#be185d', fontSize: '2rem', fontWeight: 700 }}>₹{(mockCustomers.reduce((sum, c) => sum + c.estimatedValue, 0) / 1000).toFixed(0)}K</div>
                  <div style={{ color: '#be185d', fontSize: '0.875rem', fontWeight: 500 }}>Total Value</div>
                </div>
              </div>
            </div>

            {/* Customer List */}
            <div className="customer-list" style={{ backgroundColor: '#f9fafb', borderRadius: '8px', padding: '1.5rem', border: '1px solid #e5e7eb' }}>
              <h3 style={{ fontWeight: 600, fontSize: '1.25rem', color: '#111827', marginBottom: '1rem' }}>
                Customer List ({mockCustomers.length})
              </h3>

              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ backgroundColor: '#f3f4f6' }}>
                      <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: '#374151' }}>Customer</th>
                      <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: '#374151' }}>Contact</th>
                      <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: '#374151' }}>Interactions</th>
                      <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: '#374151' }}>Last Contact</th>
                      <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: '#374151' }}>Status</th>
                      <th style={{ padding: '0.75rem', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: '#374151' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {mockCustomers.map((customer, index) => (
                      <tr key={customer.id} style={{ backgroundColor: index % 2 === 0 ? 'white' : '#f9fafb', borderTop: '1px solid #e5e7eb' }}>
                        <td style={{ padding: '1rem' }}>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <div style={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '50%',
                              backgroundColor: '#3b82f6',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginRight: '0.75rem'
                            }}>
                              <span style={{ color: 'white', fontWeight: 600 }}>
                                {customer.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <div style={{ fontWeight: 600, color: '#111827' }}>{customer.name}</div>
                              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>ID: {customer.id}</div>
                            </div>
                          </div>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <div style={{ fontSize: '0.875rem', color: '#111827' }}>{customer.email}</div>
                          <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>{customer.phone}</div>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <div style={{ fontSize: '0.875rem', color: '#111827' }}>{customer.interactionCount} calls</div>
                          <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>₹{(customer.estimatedValue / 1000).toFixed(0)}K value</div>
                        </td>
                        <td style={{ padding: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                          {new Date(customer.lastInteractionAt).toLocaleDateString()}
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <span style={{
                            padding: '0.25rem 0.75rem',
                            borderRadius: '12px',
                            fontSize: '0.75rem',
                            fontWeight: 600,
                            backgroundColor: customer.isActive ? '#dcfce7' : '#fef2f2',
                            color: customer.isActive ? '#166534' : '#dc2626'
                          }}>
                            {customer.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td style={{ padding: '1rem' }}>
                          <div style={{ display: 'flex', gap: '0.5rem' }}>
                            <button
                              onClick={() => alert(`Viewing details for ${customer.name}`)}
                              style={{
                                padding: '0.5rem',
                                backgroundColor: '#3b82f6',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '0.75rem'
                              }}
                              title="View Details"
                            >
                              👁️
                            </button>
                            <button
                              onClick={() => alert(`Starting chat with ${customer.name}`)}
                              style={{
                                padding: '0.5rem',
                                backgroundColor: '#10b981',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '0.75rem'
                              }}
                              title="Start Chat"
                            >
                              💬
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </>
        );
      })()}






    </div>
  );

  return (
    <div className="plan499-dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>Client Dashboard</h1>
          <p>Voice Bot Solution with Complete Analytics & Management</p>
          {dashboardData?.profile?.user?.assignedPlivoNumber && (
            <div className="assigned-number">
              <Phone size={16} />
              <span>Your Number: {dashboardData.profile.user.assignedPlivoNumber}</span>
            </div>
          )}
        </div>
        <div className="header-actions">
          <div className="user-info">
            <div className="user-avatar">
              <User size={20} />
            </div>
            <div className="user-details">
              <span className="user-name">{user?.username || user?.email || 'User'}</span>
              <span className="user-email">{user?.email}</span>
            </div>
          </div>
          <div className="plan-badge">
            <span>{dashboardData?.profile?.planSubscription?.planName || 'Basic Plan'}</span>
          </div>
          <button className="logout-btn" onClick={handleLogout} title="Logout">
            <LogOut size={18} />
          </button>
        </div>
      </div>

      <div className="dashboard-nav">
        <button
          className={`nav-button ${activeSection === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveSection('overview')}
        >
          <BarChart3 size={20} />
          <span>Overview</span>
        </button>
        <button
          className={`nav-button ${activeSection === 'profile' ? 'active' : ''}`}
          onClick={() => setActiveSection('profile')}
        >
          <User size={20} />
          <span>Profile</span>
        </button>
        <button
          className={`nav-button ${activeSection === 'calls' ? 'active' : ''}`}
          onClick={() => setActiveSection('calls')}
        >
          <Phone size={20} />
          <span>Call Logs</span>
        </button>
        <button
          className={`nav-button ${activeSection === 'sms' ? 'active' : ''}`}
          onClick={() => setActiveSection('sms')}
        >
          <MessageSquare size={20} />
          <span>SMS Logs</span>
        </button>
        <button
          className={`nav-button ${activeSection === 'products' ? 'active' : ''}`}
          onClick={() => setActiveSection('products')}
        >
          <Package size={20} />
          <span>Products</span>
        </button>
        <button
          className={`nav-button ${activeSection === 'customers' ? 'active' : ''}`}
          onClick={() => setActiveSection('customers')}
        >
          <Users size={20} />
          <span>Customers</span>
        </button>
        <button
          className={`nav-button ${activeSection === 'support' ? 'active' : ''}`}
          onClick={() => setActiveSection('support')}
        >
          <HeadphonesIcon size={20} />
          <span>Support</span>
        </button>
        <button
          className={`nav-button ${activeSection === 'billing' ? 'active' : ''}`}
          onClick={() => setActiveSection('billing')}
        >
          <CreditCard size={20} />
          <span>Billing</span>
        </button>
      </div>

      <div className="dashboard-content">
        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading dashboard data...</p>
          </div>
        ) : (
          <>
            {activeSection === 'overview' && renderOverview()}
            {activeSection === 'profile' && renderProfile()}
            {activeSection === 'calls' && renderCallLogs()}
            {activeSection === 'sms' && renderSMSLogs()}
            {activeSection === 'products' && renderProducts()}
            {activeSection === 'customers' && renderCustomers()}
            {activeSection === 'support' && renderSupport()}
            {activeSection === 'billing' && renderBilling()}
          </>
        )}
      </div>
    </div>
  );
};

export default ClientDashboard;
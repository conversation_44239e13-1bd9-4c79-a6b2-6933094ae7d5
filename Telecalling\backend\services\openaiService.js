const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');

class OpenAIService {
  constructor() {
    this.openai = null;
  }

  /**
   * Initialize OpenAI client (lazy initialization)
   */
  getOpenAI() {
    if (!this.openai && process.env.OPENAI_API_KEY) {
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });
    }
    return this.openai;
  }

  /**
   * Extract product data from image using OpenAI Vision with multi-pass approach
   */
  async extractProductDataFromImage(imagePath) {
    try {
      console.log('🖼️ Extracting product data from image:', imagePath);

      const openai = this.getOpenAI();
      if (!openai) {
        throw new Error('OpenAI not configured');
      }

      // Read image file
      const imageBuffer = fs.readFileSync(imagePath);
      const base64Image = imageBuffer.toString('base64');
      const mimeType = this.getMimeType(imagePath);

      // First pass: Get comprehensive extraction
      console.log('🔍 Starting comprehensive product extraction...');
      const allProducts = await this.performComprehensiveExtraction(openai, base64Image, mimeType);

      console.log(`✅ Total products extracted: ${allProducts.length}`);
      return allProducts;

    } catch (error) {
      console.error('❌ Error extracting from image:', error);

      // If it's a model error, provide a helpful message
      if (error.message.includes('deprecated') || error.message.includes('model_not_found')) {
        throw new Error('AI vision model is currently unavailable. Please try again later or contact support.');
      }

      throw error;
    }
  }

  /**
   * Perform comprehensive extraction with multiple passes
   */
  async performComprehensiveExtraction(openai, base64Image, mimeType) {
    const allProducts = [];
    const extractedNames = new Set(); // To avoid duplicates

    // Pass 1: Comprehensive extraction with detailed instructions
    console.log('📋 Pass 1: Comprehensive extraction');
    const pass1Products = await this.extractWithPrompt(openai, base64Image, mimeType, this.getComprehensivePrompt());

    // Add all products from pass 1 (they should be unique from the image)
    for (const product of pass1Products) {
      if (product.name && product.name.trim() && !this.isPlaceholderData(product)) {
        const normalizedName = product.name.toLowerCase().trim();
        if (!extractedNames.has(normalizedName)) {
          allProducts.push(product);
          extractedNames.add(normalizedName);
        }
      }
    }

    console.log(`📊 Pass 1 found: ${pass1Products.length} products, ${allProducts.length} unique added`);

    // If we got a good number of products from pass 1, we might be done
    if (allProducts.length >= 10) {
      console.log('✅ Pass 1 extracted sufficient products, skipping additional passes to save tokens');
      return allProducts;
    }

    // Pass 2: Only if we didn't get enough products
    console.log('📋 Pass 2: Focused extraction for missed items');
    const pass2Products = await this.extractWithPrompt(openai, base64Image, mimeType, this.getFocusedPrompt(extractedNames));

    for (const product of pass2Products) {
      if (product.name && product.name.trim() && !this.isPlaceholderData(product)) {
        const normalizedName = product.name.toLowerCase().trim();
        if (!extractedNames.has(normalizedName)) {
          allProducts.push(product);
          extractedNames.add(normalizedName);
        }
      }
    }

    console.log(`📊 Pass 2 found: ${pass2Products.length} products, ${allProducts.length - pass1Products.length} new unique added`);

    // Pass 3: Only if we still need more products
    if (allProducts.length < 8) {
      console.log('📋 Pass 3: Final scan for remaining items');
      const pass3Products = await this.extractWithPrompt(openai, base64Image, mimeType, this.getFinalScanPrompt(extractedNames));

      for (const product of pass3Products) {
        if (product.name && product.name.trim() && !this.isPlaceholderData(product)) {
          const normalizedName = product.name.toLowerCase().trim();
          if (!extractedNames.has(normalizedName)) {
            allProducts.push(product);
            extractedNames.add(normalizedName);
          }
        }
      }

      console.log(`📊 Pass 3 found: ${pass3Products.length} products, additional unique added`);
    } else {
      console.log('✅ Sufficient products found, skipping Pass 3 to save tokens');
    }

    console.log(`🎯 Total unique products extracted: ${allProducts.length}`);
    return allProducts;
  }

  /**
   * Extract products with a specific prompt
   */
  async extractWithPrompt(openai, base64Image, mimeType, promptText) {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: promptText
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:${mimeType};base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 4000, // Reduced from 6000 for efficiency
        temperature: 0.1  // Lower temperature for more consistent results
      });

      const content = response.choices[0].message.content;
      console.log('🤖 OpenAI Vision Response:', content);

      // Parse JSON response with enhanced error handling
      return this.parseAIResponse(content);

      // Fallback: Try to extract basic product info from text
      console.log('⚠️ No valid JSON found, attempting text extraction fallback');
      console.log('📄 Raw AI response:', content.substring(0, 1000));

      // Try to extract at least product names and prices from the text
      const fallbackProducts = this.extractBasicProductInfo(content);
      if (fallbackProducts.length > 0) {
        console.log('✅ Extracted products using fallback method:', fallbackProducts.length);
        return fallbackProducts;
      }

      throw new Error('Could not parse product data from image - no valid JSON or extractable content found');
    } catch (error) {
      console.error('❌ Error extracting from image:', error);

      // If it's a model error, provide a helpful message
      if (error.message.includes('deprecated') || error.message.includes('model_not_found')) {
        throw new Error('AI vision model is currently unavailable. Please try again later or contact support.');
      }

      throw error;
    }
  }

  /**
   * Enhanced JSON parsing with repair capabilities
   */
  parseAIResponse(content) {
    try {
      console.log('🔧 Parsing AI response with enhanced error handling');

      // Clean the content
      let jsonContent = content.trim();
      jsonContent = jsonContent.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Try to find JSON content (array or object)
      const arrayMatch = jsonContent.match(/\[[\s\S]*\]/);
      const objectMatch = jsonContent.match(/\{[\s\S]*\}/);

      // Try direct parsing first
      if (arrayMatch) {
        console.log('📋 Attempting direct array parsing');
        const result = this.tryParseJSON(arrayMatch[0], 'array');
        if (result) return result;
      }

      if (objectMatch) {
        console.log('📋 Attempting direct object parsing');
        const result = this.tryParseJSON(objectMatch[0], 'object');
        if (result) return result;
      }

      // If direct parsing fails, try to repair the JSON
      console.log('🔧 Direct parsing failed, attempting JSON repair');
      if (objectMatch) {
        const repairedResult = this.repairAndParseJSON(objectMatch[0]);
        if (repairedResult && repairedResult.length > 0) {
          console.log(`✅ JSON repair successful: ${repairedResult.length} products recovered`);
          return repairedResult;
        }
      }

      // Last resort: extract from raw text
      console.log('⚠️ JSON repair failed, using text extraction fallback');
      const fallbackProducts = this.extractBasicProductInfo(content);
      if (fallbackProducts.length > 0) {
        console.log(`✅ Text extraction successful: ${fallbackProducts.length} products found`);
        return fallbackProducts;
      }

      throw new Error('Could not parse any product data from AI response');

    } catch (error) {
      console.error('❌ Error in parseAIResponse:', error);
      throw error;
    }
  }

  /**
   * Try to parse JSON with error handling
   */
  tryParseJSON(jsonString, type) {
    try {
      const parsed = JSON.parse(jsonString);
      if (type === 'array') {
        return Array.isArray(parsed) ? parsed : [];
      } else if (type === 'object') {
        return parsed.products || [];
      }
      return [];
    } catch (error) {
      console.log(`❌ ${type} JSON parsing failed:`, error.message);
      return null;
    }
  }

  /**
   * Repair malformed JSON and extract products
   */
  repairAndParseJSON(jsonString) {
    try {
      console.log('🔧 Attempting JSON repair...');

      // Find the products array start
      const productsStart = jsonString.indexOf('"products": [');
      if (productsStart === -1) {
        console.log('❌ No products array found');
        return [];
      }

      // Extract just the products array part
      let productsSection = jsonString.substring(productsStart + 12); // Skip '"products": ['

      // Find where the array should end and try to close it properly
      const products = [];
      let currentProduct = '';
      let braceCount = 0;
      let inString = false;
      let escapeNext = false;

      for (let i = 0; i < productsSection.length; i++) {
        const char = productsSection[i];

        if (escapeNext) {
          escapeNext = false;
          currentProduct += char;
          continue;
        }

        if (char === '\\') {
          escapeNext = true;
          currentProduct += char;
          continue;
        }

        if (char === '"' && !escapeNext) {
          inString = !inString;
        }

        if (!inString) {
          if (char === '{') {
            braceCount++;
          } else if (char === '}') {
            braceCount--;

            // When we close a product object
            if (braceCount === 0) {
              currentProduct += char;

              // Try to parse this individual product
              try {
                const product = JSON.parse(currentProduct);
                if (product.name) {
                  products.push(product);
                  console.log(`✅ Recovered product: ${product.name}`);
                }
              } catch (productError) {
                console.log(`❌ Failed to parse product: ${currentProduct.substring(0, 50)}...`);
              }

              currentProduct = '';
              continue;
            }
          }
        }

        currentProduct += char;
      }

      console.log(`🔧 JSON repair completed: ${products.length} products recovered`);
      return products;

    } catch (error) {
      console.error('❌ JSON repair failed:', error);
      return [];
    }
  }

  /**
   * Fallback method to extract basic product info from text response
   */
  extractBasicProductInfo(text) {
    const products = [];

    try {
      console.log('🔍 Starting enhanced text extraction fallback');

      // First, try to extract from JSON-like patterns in the text
      const jsonProducts = this.extractFromPartialJSON(text);
      products.push(...jsonProducts);

      // Then, look for simple name-price patterns
      const lines = text.split('\n').filter(line => line.trim().length > 0);

      for (const line of lines) {
        // Skip lines that look like headers or instructions
        if (line.includes('JSON') || line.includes('format') || line.includes('extract') ||
            line.includes('analyze') || line.includes('RULES') || line.includes('products:')) {
          continue;
        }

        // Look for name patterns with quotes
        const nameMatch = line.match(/"name":\s*"([^"]+)"/);
        const priceMatch = line.match(/"price":\s*"(\d+)"/);

        if (nameMatch && priceMatch) {
          const productName = nameMatch[1];
          const price = parseInt(priceMatch[1]);

          // Check if we already have this product
          const exists = products.some(p => p.name.toLowerCase() === productName.toLowerCase());
          if (!exists) {
            products.push({
              name: productName,
              price: price,
              category: 'Food',
              features: {
                'Description': 'Extracted from partial JSON'
              },
              availability: 'available',
              alias1: productName.split(' ')[0] || productName,
              alias2: productName.toLowerCase().replace(/\s+/g, '_')
            });
          }
          continue;
        }

        // Look for simple price patterns (₹, Rs, $, numbers)
        const simplePriceMatch = line.match(/[₹$Rs]?\s*(\d+(?:\.\d{2})?)/);

        if (simplePriceMatch) {
          // Extract product name (text before price)
          let productName = line.split(/[₹$Rs]/)[0].trim();
          productName = productName.replace(/[-:]/g, '').trim();

          if (productName.length > 2 && productName.length < 100) {
            const price = parseFloat(simplePriceMatch[1]);

            // Check if we already have this product
            const exists = products.some(p => p.name.toLowerCase() === productName.toLowerCase());
            if (!exists) {
              products.push({
                name: productName,
                price: price,
                category: 'General',
                features: {
                  'Description': 'Extracted from text pattern'
                },
                availability: 'available',
                alias1: productName.split(' ')[0] || productName,
                alias2: productName.toLowerCase().replace(/\s+/g, '_')
              });
            }
          }
        }
      }

      console.log(`📋 Enhanced fallback extraction found ${products.length} products`);
      return products;
    } catch (error) {
      console.error('❌ Error in fallback extraction:', error);
      return [];
    }
  }

  /**
   * Extract products from partial JSON patterns
   */
  extractFromPartialJSON(text) {
    const products = [];

    try {
      // Look for individual product objects in the text
      const productPattern = /"name":\s*"([^"]+)"[\s\S]*?"price":\s*"(\d+)"/g;
      let match;

      while ((match = productPattern.exec(text)) !== null) {
        const name = match[1];
        const price = parseInt(match[2]);

        if (name && price) {
          products.push({
            name: name,
            price: price,
            category: 'Food',
            features: {
              'Description': 'Extracted from partial JSON pattern'
            },
            availability: 'available',
            alias1: name.split(' ')[0] || name,
            alias2: name.toLowerCase().replace(/\s+/g, '_')
          });
        }
      }

      console.log(`🔍 Partial JSON extraction found ${products.length} products`);
      return products;
    } catch (error) {
      console.error('❌ Error in partial JSON extraction:', error);
      return [];
    }
  }

  /**
   * Check if product data is placeholder/fake
   */
  isPlaceholderData(product) {
    if (!product || !product.name) return true;

    const name = product.name.toLowerCase();
    const description = product.features?.Description?.toLowerCase() || '';

    // Check for placeholder patterns
    const placeholderPatterns = [
      'lorem ipsum',
      'dummy text',
      'placeholder',
      'sample text',
      'example',
      'test product',
      'lorem',
      'ipsum'
    ];

    return placeholderPatterns.some(pattern =>
      name.includes(pattern) || description.includes(pattern)
    );
  }

  /**
   * Get comprehensive extraction prompt (Pass 1)
   */
  getComprehensivePrompt() {
    return `Look at this menu/catalog image and extract ALL visible products with their exact details.

CRITICAL INSTRUCTIONS:
- Extract ONLY what you can actually SEE in the image
- DO NOT make up or invent any information
- DO NOT use placeholder text like "Lorem Ipsum" or "dummy text"
- If you can't see a detail clearly, leave that field empty
- Extract the EXACT names and prices as written

Return in this exact JSON format:
{
  "products": [
    {
      "name": "exact product name as written",
      "price": "exact price number only (no currency symbols)",
      "category": "Food/Beverages/Desserts/etc",
      "features": {
        "Description": "actual description if visible, otherwise empty",
        "Type": "type if visible",
        "Cuisine": "cuisine if visible",
        "Spice_Level": "spice level if indicated",
        "Dietary": "veg/non-veg if indicated"
      },
      "availability": "available",
      "alias1": "short form or common name",
      "alias2": "alternative name"
    }
  ]
}

SCAN SYSTEMATICALLY:
1. Start from top-left, go row by row
2. Look for product names with prices
3. Check all sections, headers, columns
4. Include beverages, mains, sides, desserts
5. Extract ONLY what is actually visible
6. DO NOT invent or assume information

Find ALL products but use ONLY real data from the image.`;
  }

  /**
   * Get focused extraction prompt (Pass 2)
   */
  getFocusedPrompt(alreadyExtracted) {
    const extractedList = Array.from(alreadyExtracted).slice(0, 10).join(', '); // Limit to avoid token waste

    return `I already found these items: ${extractedList}

Look at the image again and find ONLY the items I missed.

STRICT RULES:
- Extract ONLY what you can actually see in the image
- DO NOT repeat items I already found
- DO NOT make up information or use placeholder text
- If you can't see clear details, leave fields empty
- Look in corners, edges, different sections

Return ONLY NEW items in this format:
{
  "products": [
    {
      "name": "exact name as written in image",
      "price": "exact price if visible",
      "category": "category if clear",
      "features": {
        "Description": "only if actually visible"
      },
      "availability": "available",
      "alias1": "short form",
      "alias2": "alternative"
    }
  ]
}

Find ONLY real, visible items that I missed. NO placeholder data.`;
  }

  /**
   * Get final scan prompt (Pass 3)
   */
  getFinalScanPrompt(alreadyExtracted) {
    const extractedList = Array.from(alreadyExtracted).join(', ');

    return `FINAL SCAN: I've found ${alreadyExtracted.size} items so far: ${extractedList}

This is your LAST CHANCE to find any remaining items. Look for:
- Any text that could be a product name (even without price)
- Items in headers or footers
- Watermarked text
- Items in different languages
- Abbreviations or short forms
- Numbers that could be item codes with names
- ANY text that looks like it could be a menu item

Return even partial information in JSON format:
{
  "products": [
    {
      "name": "any remaining item name",
      "category": "best guess category",
      "features": {
        "Description": "any visible details"
      },
      "availability": "available",
      "alias1": "short form",
      "alias2": "alternative"
    }
  ]
}

Find EVERYTHING remaining. Don't give up!`;
  }

  /**
   * Extract product data from text content
   */
  async extractProductDataFromText(textContent, fileType = 'text') {
    try {
      console.log('📄 Extracting product data from text, type:', fileType);
      console.log('📊 Text content length:', textContent.length, 'characters');

      const openai = this.getOpenAI();
      if (!openai) {
        throw new Error('OpenAI not configured');
      }

      // For large files, use chunking approach
      const maxChunkSize = 15000; // Characters per chunk (safe for GPT-4)

      if (textContent.length > maxChunkSize) {
        console.log('📚 Large file detected, using chunking approach');
        return await this.extractFromLargeText(openai, textContent, fileType, maxChunkSize);
      }

      // For smaller files, use single extraction
      return await this.extractFromSingleText(openai, textContent, fileType);

    } catch (error) {
      console.error('❌ Error extracting from text:', error);
      throw error;
    }
  }

  /**
   * Extract from large text files using chunking
   */
  async extractFromLargeText(openai, textContent, fileType, maxChunkSize) {
    const allProducts = [];
    const extractedNames = new Set();

    // Split text into chunks
    const chunks = this.splitTextIntoChunks(textContent, maxChunkSize);
    console.log(`📚 Processing ${chunks.length} chunks for large ${fileType} file`);

    for (let i = 0; i < chunks.length; i++) {
      console.log(`📄 Processing chunk ${i + 1}/${chunks.length} (${chunks[i].length} chars)`);

      try {
        const chunkProducts = await this.extractFromSingleText(openai, chunks[i], fileType, i + 1, chunks.length);

        // Add unique products
        for (const product of chunkProducts) {
          if (product.name && !extractedNames.has(product.name.toLowerCase())) {
            allProducts.push(product);
            extractedNames.add(product.name.toLowerCase());
          }
        }

        console.log(`✅ Chunk ${i + 1} processed: ${chunkProducts.length} products found`);

        // Small delay to avoid rate limiting
        if (i < chunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }

      } catch (error) {
        console.error(`❌ Error processing chunk ${i + 1}:`, error);
        // Continue with other chunks
      }
    }

    console.log(`🎯 Large file extraction complete: ${allProducts.length} total products`);
    return allProducts;
  }

  /**
   * Split text into manageable chunks
   */
  splitTextIntoChunks(text, maxChunkSize) {
    const chunks = [];
    const lines = text.split('\n');
    let currentChunk = '';

    for (const line of lines) {
      // If adding this line would exceed chunk size, start new chunk
      if (currentChunk.length + line.length + 1 > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = line;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + line;
      }
    }

    // Add the last chunk
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  /**
   * Extract from single text chunk
   */
  async extractFromSingleText(openai, textContent, fileType, chunkNum = 1, totalChunks = 1) {
    try {
      const chunkInfo = totalChunks > 1 ? ` (Chunk ${chunkNum}/${totalChunks})` : '';

      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `You are a product data extraction expert. Extract product information from the provided text and return it in JSON format.`
          },
          {
            role: "user",
            content: `Extract product information from this ${fileType} content${chunkInfo} and format as JSON:

IMPORTANT: This may be part of a larger file. Extract ALL products you can find in this section.

${textContent}

Return in this exact JSON format:
{
  "products": [
    {
      "name": "product name",
      "price": "price as number (numeric value only)",
      "category": "product category",
      "features": {
        "Brand": "brand name",
        "Model": "model name",
        "Color": "color",
        "Size": "size/dimensions",
        "Material": "material type",
        "Weight": "weight",
        "Warranty": "warranty period",
        "Features": "key features",
        "Specifications": "technical specs"
      },
      "availability": "available/out_of_stock",
      "alias1": "first alternative name",
      "alias2": "second alternative name"
    }
  ]
}

CRITICAL RULES for features:
- Extract ALL product attributes as key-value pairs in the "features" object (EXCEPT the product name)
- If the data has columns like "Brand", "Model", "Color", etc., extract each as separate key-value pairs
- Use descriptive keys: "Brand", "Model", "Color", "Size", "Material", "Weight", "Warranty", "Features", "Specifications"
- If you see 4 feature columns (excluding name), create 4 key-value pairs
- If you see 10 feature columns (excluding name), create 10 key-value pairs
- DO NOT include the product name in features (it goes in the "name" field separately)
- For missing information, omit the key entirely (don't include empty values)
- Be thorough and extract every detail available

Other Rules:
- Extract all products found in the content
- For price, return only numeric value (no currency symbols)
- Generate 2 smart aliases for each product (synonyms, common names, slang terms)
- Be intelligent about categorizing products
- Make aliases creative and useful for search`
          }
        ],
        max_tokens: 6000
      });

      const content = response.choices[0].message.content;
      console.log('🤖 OpenAI Text Response:', content);

      // Parse JSON response - handle both array and object formats
      let jsonContent = content.trim();

      // Remove any markdown code blocks if present
      jsonContent = jsonContent.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Try to find JSON content (array or object)
      const arrayMatch = jsonContent.match(/\[[\s\S]*\]/);
      const objectMatch = jsonContent.match(/\{[\s\S]*\}/);

      let productData;

      if (arrayMatch) {
        // Direct array format: [{...}, {...}]
        console.log('📋 Parsing as direct array format');
        try {
          productData = JSON.parse(arrayMatch[0]);
          return Array.isArray(productData) ? productData : [];
        } catch (parseError) {
          console.error('❌ Error parsing array JSON:', parseError);
          console.log('📄 Raw array content:', arrayMatch[0].substring(0, 500) + '...');
          throw new Error('Invalid JSON array format from AI response');
        }
      } else if (objectMatch) {
        // Object format: {products: [{...}, {...}]}
        console.log('📋 Parsing as object format');
        try {
          const parsedData = JSON.parse(objectMatch[0]);
          return parsedData.products || [];
        } catch (parseError) {
          console.error('❌ Error parsing object JSON:', parseError);
          console.log('📄 Raw object content:', objectMatch[0].substring(0, 500) + '...');
          throw new Error('Invalid JSON object format from AI response');
        }
      }

      throw new Error('Could not parse product data from text - no valid JSON found');
    } catch (error) {
      console.error('❌ Error extracting from single text:', error);
      throw error;
    }
  }

  /**
   * Generate smart aliases for a product
   */
  async generateSmartAliases(productName, category, description) {
    try {
      console.log('🧠 Generating smart aliases for:', productName);

      const openai = this.getOpenAI();
      if (!openai) {
        throw new Error('OpenAI not configured');
      }

      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an expert at creating product aliases and alternative names for search optimization."
          },
          {
            role: "user",
            content: `Generate 2 smart aliases for this product:
            
Product Name: ${productName}
Category: ${category}
Description: ${description}

Return only 2 aliases separated by commas. Make them:
- Searchable and commonly used terms
- Different from the original name
- Relevant to the product
- Include slang, abbreviations, or descriptive terms

Example: "iPhone 15" -> "Apple Phone, Latest iPhone"`
          }
        ],
        max_tokens: 100
      });

      const aliases = response.choices[0].message.content.trim().split(',');
      return {
        alias1: aliases[0]?.trim() || 'Alternative name',
        alias2: aliases[1]?.trim() || 'Common name'
      };
    } catch (error) {
      console.error('❌ Error generating aliases:', error);
      return {
        alias1: 'Alternative name',
        alias2: 'Common name'
      };
    }
  }

  /**
   * Get MIME type from file extension
   */
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };
    return mimeTypes[ext] || 'image/jpeg';
  }

  /**
   * Check if OpenAI API key is configured
   */
  isConfigured() {
    return !!process.env.OPENAI_API_KEY;
  }
}

module.exports = new OpenAIService();

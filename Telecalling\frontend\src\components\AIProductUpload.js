import React, { useState, useRef } from 'react';
import { Upload, FileText, Image, FileSpreadsheet, File, X, CheckCircle, AlertCircle, Loader, Brain, Sparkles } from 'lucide-react';

const AIProductUpload = ({ onUploadComplete, onClose }) => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadResults, setUploadResults] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  const supportedTypes = {
    images: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    documents: ['.pdf', '.doc', '.docx'],
    spreadsheets: ['.xlsx', '.xls', '.csv'],
    text: ['.txt']
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };

  const handleFileSelect = (e) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = (newFiles) => {
    const validFiles = newFiles.filter(file => {
      const ext = '.' + file.name.split('.').pop().toLowerCase();
      return [...supportedTypes.images, ...supportedTypes.documents, ...supportedTypes.spreadsheets, ...supportedTypes.text].includes(ext);
    });

    setFiles(prev => [...prev, ...validFiles.map(file => ({
      file,
      id: Date.now() + Math.random(),
      status: 'ready'
    }))]);
  };

  const removeFile = (id) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  const getFileIcon = (filename) => {
    const ext = '.' + filename.split('.').pop().toLowerCase();
    if (supportedTypes.images.includes(ext)) return <Image size={20} />;
    if (supportedTypes.documents.includes(ext)) return <FileText size={20} />;
    if (supportedTypes.spreadsheets.includes(ext)) return <FileSpreadsheet size={20} />;
    return <File size={20} />;
  };

  const handleUpload = async () => {
    if (files.length === 0) return;

    setUploading(true);
    setUploadResults(null);

    try {
      const formData = new FormData();
      files.forEach(({ file }) => {
        formData.append('files', file);
      });

      const token = localStorage.getItem('voicebot_access_token');
      const response = await fetch('http://localhost:5000/api/products/ai-bulk-upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      const result = await response.json();

      if (response.ok) {
        setUploadResults(result.results);
        if (onUploadComplete) {
          onUploadComplete(result.results);
        }
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadResults({
        error: error.message,
        totalFiles: files.length,
        processedFiles: 0,
        totalProducts: 0,
        successfulProducts: 0,
        failedProducts: 0
      });
    } finally {
      setUploading(false);
    }
  };

  const resetUpload = () => {
    setFiles([]);
    setUploadResults(null);
    setUploading(false);
  };

  const modalStyles = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000
  };

  const containerStyles = {
    backgroundColor: 'white',
    borderRadius: '12px',
    padding: '24px',
    maxWidth: '600px',
    width: '90%',
    maxHeight: '80vh',
    overflowY: 'auto',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
  };

  return (
    <div style={modalStyles}>
      <div style={containerStyles}>
        <div className="ai-upload-header">
          <div className="header-content">
            <div className="header-icon">
              <Brain size={32} />
              <Sparkles size={16} className="sparkle-icon" />
            </div>
            <div>
              <h2>AI-Powered Product Upload</h2>
              <p>Upload images, documents, or spreadsheets and let AI extract product data automatically</p>
            </div>
          </div>
          <button className="close-btn" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        {!uploadResults ? (
          <>
            <div 
              className={`upload-zone ${dragActive ? 'drag-active' : ''}`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="upload-content">
                <Upload size={48} />
                <h3>Drop files here or click to browse</h3>
                <p>Supports images, PDFs, Word docs, Excel files, and CSV</p>
                
                <div className="supported-formats">
                  <div className="format-group">
                    <Image size={16} />
                    <span>Images: JPG, PNG, GIF, WebP</span>
                  </div>
                  <div className="format-group">
                    <FileText size={16} />
                    <span>Documents: PDF, DOC, DOCX</span>
                  </div>
                  <div className="format-group">
                    <FileSpreadsheet size={16} />
                    <span>Spreadsheets: XLSX, XLS, CSV</span>
                  </div>
                </div>
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.doc,.docx,.xlsx,.xls,.csv,.txt"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
            </div>

            {files.length > 0 && (
              <div className="file-list">
                <h3>Selected Files ({files.length})</h3>
                {files.map(({ file, id }) => (
                  <div key={id} className="file-item">
                    <div className="file-info">
                      {getFileIcon(file.name)}
                      <div>
                        <div className="file-name">{file.name}</div>
                        <div className="file-size">{(file.size / 1024 / 1024).toFixed(2)} MB</div>
                      </div>
                    </div>
                    <button className="remove-file" onClick={() => removeFile(id)}>
                      <X size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}

            <div className="upload-actions">
              <button 
                className="btn-secondary" 
                onClick={resetUpload}
                disabled={uploading}
              >
                Clear All
              </button>
              <button 
                className="btn-primary ai-upload-btn" 
                onClick={handleUpload}
                disabled={files.length === 0 || uploading}
              >
                {uploading ? (
                  <>
                    <Loader size={16} className="spinning" />
                    Processing with AI...
                  </>
                ) : (
                  <>
                    <Brain size={16} />
                    Upload & Extract with AI
                  </>
                )}
              </button>
            </div>
          </>
        ) : (
          <div className="upload-results">
            <div className="results-header">
              {uploadResults.error ? (
                <AlertCircle size={32} className="error-icon" />
              ) : (
                <CheckCircle size={32} className="success-icon" />
              )}
              <h3>
                {uploadResults.error ? 'Upload Failed' : 'Upload Complete!'}
              </h3>
            </div>

            {uploadResults.error ? (
              <div className="error-message">
                <p>{uploadResults.error}</p>
              </div>
            ) : (
              <div className="results-summary">
                <div className="summary-grid">
                  <div className="summary-item">
                    <span className="summary-label">Files Processed</span>
                    <span className="summary-value">{uploadResults.processedFiles}/{uploadResults.totalFiles}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Products Found</span>
                    <span className="summary-value">{uploadResults.totalProducts}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Successfully Added</span>
                    <span className="summary-value success">{uploadResults.successfulProducts}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Failed</span>
                    <span className="summary-value error">{uploadResults.failedProducts}</span>
                  </div>
                </div>

                {uploadResults.products && uploadResults.products.length > 0 && (
                  <div className="products-preview">
                    <h4>Added Products:</h4>
                    <div className="products-list">
                      {uploadResults.products.slice(0, 5).map((product, index) => (
                        <div key={index} className="product-item">
                          <div className="product-name">{product.name}</div>
                          <div className="product-details">
                            {product.category} • ${product.price} • Aliases: {product.aliases.join(', ')}
                          </div>
                        </div>
                      ))}
                      {uploadResults.products.length > 5 && (
                        <div className="more-products">
                          +{uploadResults.products.length - 5} more products
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="results-actions">
              <button className="btn-secondary" onClick={resetUpload}>
                Upload More Files
              </button>
              <button className="btn-primary" onClick={onClose}>
                Done
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIProductUpload;

-- =====================================================
-- IMMEDIATE FIX FOR SUPER ADMIN ROLE ISSUE
-- =====================================================
-- Execute these queries in Supabase SQL Editor RIGHT NOW

-- 1. Add role column if it doesn't exist
ALTER TABLE admins ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin'));

-- 2. Update super admin role
UPDATE admins 
SET role = 'super_admin', password_hash = 'superadmin123' 
WHERE admin_id = 'superadmin001';

-- 3. If super admin doesn't exist, create it
INSERT INTO admins (admin_id, password_hash, name, email, role, is_active, created_at, updated_at) 
VALUES (
    'superadmin001', 
    'superadmin123',
    'Super Administrator',
    '<EMAIL>',
    'super_admin',
    true,
    NOW(),
    NOW()
) ON CONFLICT (admin_id) DO UPDATE SET
    role = 'super_admin',
    password_hash = 'superadmin123';

-- 4. Verify the fix
SELECT 
    admin_id, 
    name, 
    email, 
    role, 
    password_hash,
    is_active
FROM admins 
WHERE admin_id = 'superadmin001';

-- Expected result:
-- admin_id: superadmin001
-- role: super_admin
-- password_hash: superadmin123

SELECT 'Super admin role fixed!' as status;

-- Customer Portal Database Migration
-- Run this in your Supabase SQL editor

-- 1. Customers table (end users who interact with businesses)
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    name VARCHAR(255) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    pincode VARCHAR(10),
    date_of_birth DATE,
    gender VARCHAR(10),
    is_email_verified BOOLEAN DEFAULT FALSE,
    is_phone_verified BOOLEAN DEFAULT FALSE,
    profile_picture_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Customer interactions (links customers to businesses they've contacted)
CREATE TABLE IF NOT EXISTS customer_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    call_log_id UUID REFERENCES call_logs(id) ON DELETE SET NULL,
    interaction_type VARCHAR(50) NOT NULL DEFAULT 'call', -- call, sms, chat, order
    interaction_source VARCHAR(100), -- phone_number, website, etc
    first_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    interaction_count INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'active', -- active, inactive, blocked
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(customer_id, client_id)
);

-- 3. Customer registration tokens (for secure registration links)
CREATE TABLE IF NOT EXISTS customer_registration_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token VARCHAR(255) UNIQUE NOT NULL,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    call_log_id UUID REFERENCES call_logs(id) ON DELETE SET NULL,
    phone_number VARCHAR(20),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP WITH TIME ZONE,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Customer orders/appointments
CREATE TABLE IF NOT EXISTS customer_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    interaction_id UUID REFERENCES customer_interactions(id) ON DELETE SET NULL,
    order_number VARCHAR(100) UNIQUE NOT NULL,
    order_type VARCHAR(50) NOT NULL, -- appointment, product_order, service_request
    title VARCHAR(255) NOT NULL,
    description TEXT,
    products JSONB DEFAULT '[]', -- array of products with quantities
    total_amount DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(50) DEFAULT 'pending', -- pending, confirmed, in_progress, completed, cancelled
    scheduled_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    payment_status VARCHAR(50) DEFAULT 'pending', -- pending, paid, failed, refunded
    payment_details JSONB DEFAULT '{}',
    delivery_address TEXT,
    special_instructions TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Customer-Business chat system
CREATE TABLE IF NOT EXISTS customer_chats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    interaction_id UUID REFERENCES customer_interactions(id) ON DELETE SET NULL,
    chat_title VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active', -- active, closed, archived
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    unread_count_customer INTEGER DEFAULT 0,
    unread_count_client INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(customer_id, client_id)
);

-- 6. Chat messages
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chat_id UUID NOT NULL REFERENCES customer_chats(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL, -- customer, client
    sender_id UUID NOT NULL, -- customer_id or client_id
    message_type VARCHAR(50) DEFAULT 'text', -- text, image, file, system
    content TEXT NOT NULL,
    attachments JSONB DEFAULT '[]',
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    reply_to_message_id UUID REFERENCES chat_messages(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Customer email verification tokens
CREATE TABLE IF NOT EXISTS customer_email_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(6) NOT NULL, -- 6-digit OTP
    token_type VARCHAR(50) NOT NULL, -- email_verification, password_reset, login
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Customer sessions (for authentication)
CREATE TABLE IF NOT EXISTS customer_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    device_info JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_customer_id ON customer_interactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_client_id ON customer_interactions(client_id);
CREATE INDEX IF NOT EXISTS idx_customer_registration_tokens_token ON customer_registration_tokens(token);
CREATE INDEX IF NOT EXISTS idx_customer_registration_tokens_expires_at ON customer_registration_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_customer_orders_customer_id ON customer_orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_orders_client_id ON customer_orders(client_id);
CREATE INDEX IF NOT EXISTS idx_customer_orders_status ON customer_orders(status);
CREATE INDEX IF NOT EXISTS idx_customer_chats_customer_id ON customer_chats(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_chats_client_id ON customer_chats(client_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_id ON chat_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_customer_email_tokens_email ON customer_email_tokens(email);
CREATE INDEX IF NOT EXISTS idx_customer_email_tokens_token ON customer_email_tokens(token);
CREATE INDEX IF NOT EXISTS idx_customer_sessions_customer_id ON customer_sessions(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_sessions_token ON customer_sessions(session_token);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_interactions_updated_at BEFORE UPDATE ON customer_interactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_orders_updated_at BEFORE UPDATE ON customer_orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_chats_updated_at BEFORE UPDATE ON customer_chats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data for testing
INSERT INTO customers (email, name, phone, address, city, state, pincode, is_email_verified) VALUES
('<EMAIL>', 'John Doe', '+919876543210', '123 Main Street', 'Mumbai', 'Maharashtra', '400001', true),
('<EMAIL>', 'Jane Smith', '+919876543211', '456 Park Avenue', 'Delhi', 'Delhi', '110001', true),
('<EMAIL>', 'Test Customer', '+919876543212', '789 Test Road', 'Bangalore', 'Karnataka', '560001', false);

-- Grant necessary permissions (adjust based on your RLS policies)
-- ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE customer_interactions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE customer_orders ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE customer_chats ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

COMMENT ON TABLE customers IS 'End customers who interact with businesses';
COMMENT ON TABLE customer_interactions IS 'Links customers to businesses they have contacted';
COMMENT ON TABLE customer_registration_tokens IS 'Secure tokens for customer registration links';
COMMENT ON TABLE customer_orders IS 'Customer orders and appointments';
COMMENT ON TABLE customer_chats IS 'Chat conversations between customers and businesses';
COMMENT ON TABLE chat_messages IS 'Individual messages in customer-business chats';
COMMENT ON TABLE customer_email_tokens IS 'Email verification and authentication tokens';
COMMENT ON TABLE customer_sessions IS 'Customer authentication sessions';

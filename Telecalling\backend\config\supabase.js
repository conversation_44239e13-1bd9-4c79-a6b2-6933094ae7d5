const postgres = require('postgres');
const { createClient } = require('@supabase/supabase-js');

// Database connection using postgres package
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required');
}

console.log('🔍 Database connection info:');
console.log('Connection string exists:', !!connectionString);
console.log('Connection string starts with:', connectionString.substring(0, 20) + '...');

// Create SQL connection
const sql = postgres(connectionString, {
  max: 20, // Maximum number of connections
  idle_timeout: 20, // Idle timeout in seconds
  connect_timeout: 10, // Connect timeout in seconds
  onnotice: (notice) => {
    console.log('📢 Database notice:', notice);
  },
  debug: process.env.NODE_ENV === 'development' ? console.log : false,
});

// Legacy Supabase client for any remaining operations that need it

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabase = null;
let supabaseAdmin = null;

// Only create Supabase clients if credentials are available (for backward compatibility)
if (supabaseUrl && supabaseAnonKey && supabaseServiceRoleKey) {
  supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: true,
      persistSession: false
    }
  });

  supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Database helper functions using raw SQL
const dbHelpers = {
  // Get the SQL connection
  getSql() {
    return sql;
  },

  // Generic select query
  async findMany(table, where = {}, options = {}) {
    let query = `SELECT ${options.select || '*'} FROM ${table}`;
    const conditions = [];
    const values = [];
    let valueIndex = 1;

    // Build WHERE clause
    if (Object.keys(where).length > 0) {
      Object.entries(where).forEach(([key, value]) => {
        conditions.push(`${key} = $${valueIndex}`);
        values.push(value);
        valueIndex++;
      });
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    // Add ORDER BY
    if (options.orderBy) {
      const direction = options.orderBy.ascending ? 'ASC' : 'DESC';
      query += ` ORDER BY ${options.orderBy.column} ${direction}`;
    }

    // Add LIMIT
    if (options.limit) {
      query += ` LIMIT $${valueIndex}`;
      values.push(options.limit);
      valueIndex++;
    }

    // Add OFFSET
    if (options.offset) {
      query += ` OFFSET $${valueIndex}`;
      values.push(options.offset);
    }

    const result = await sql.unsafe(query, values);
    return result;
  },

  // Find single record
  async findOne(table, where) {
    try {
    const conditions = [];
    const values = [];
    let valueIndex = 1;

    Object.entries(where).forEach(([key, value]) => {
      conditions.push(`${key} = $${valueIndex}`);
      values.push(value);
      valueIndex++;
    });

    const query = `SELECT * FROM ${table} WHERE ${conditions.join(' AND ')} LIMIT 1`;
      console.log('🔍 Executing query:', query);
      console.log('🔍 With values:', values);
      
    const result = await sql.unsafe(query, values);
      console.log('✅ Query result:', result.length, 'rows');
    return result[0] || null;
    } catch (error) {
      console.error('❌ Database query error:', error);
      throw error;
    }
  },

  // Insert data
  async insert(table, data) {
    if (Array.isArray(data)) {
      // Bulk insert
      if (data.length === 0) return [];
      
      const columns = Object.keys(data[0]);
      const placeholders = data.map((_, index) => 
        `(${columns.map((_, colIndex) => `$${index * columns.length + colIndex + 1}`).join(', ')})`
      ).join(', ');
      
      const values = data.flatMap(item => columns.map(col => item[col]));
      const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES ${placeholders} RETURNING *`;
      
      return await sql.unsafe(query, values);
    } else {
      // Single insert
      const columns = Object.keys(data);
      const placeholders = columns.map((_, index) => `$${index + 1}`).join(', ');
      const values = Object.values(data);
      
      const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders}) RETURNING *`;
      const result = await sql.unsafe(query, values);
      return result[0];
    }
  },

  // Update data
  async update(table, data, where) {
    const setClause = [];
    const values = [];
    let valueIndex = 1;

    // Build SET clause
    Object.entries(data).forEach(([key, value]) => {
      setClause.push(`${key} = $${valueIndex}`);
      values.push(value);
      valueIndex++;
    });

    // Build WHERE clause
    const conditions = [];
    Object.entries(where).forEach(([key, value]) => {
      conditions.push(`${key} = $${valueIndex}`);
      values.push(value);
      valueIndex++;
    });

    const query = `UPDATE ${table} SET ${setClause.join(', ')} WHERE ${conditions.join(' AND ')} RETURNING *`;
    const result = await sql.unsafe(query, values);
    return result;
  },

  // Delete data
  async delete(table, where) {
    const conditions = [];
    const values = [];
    let valueIndex = 1;

    Object.entries(where).forEach(([key, value]) => {
      conditions.push(`${key} = $${valueIndex}`);
      values.push(value);
      valueIndex++;
    });

    const query = `DELETE FROM ${table} WHERE ${conditions.join(' AND ')} RETURNING *`;
    const result = await sql.unsafe(query, values);
    return result;
  },

  // Count records
  async count(table, where = {}) {
    let query = `SELECT COUNT(*) as count FROM ${table}`;
    const conditions = [];
    const values = [];
    let valueIndex = 1;

    if (Object.keys(where).length > 0) {
      Object.entries(where).forEach(([key, value]) => {
        conditions.push(`${key} = $${valueIndex}`);
        values.push(value);
        valueIndex++;
      });
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    const result = await sql.unsafe(query, values);
    return parseInt(result[0].count);
  },

  // Execute raw SQL query
  async query(queryString, params = []) {
    return await sql.unsafe(queryString, params);
  },

  // Transaction support
  async transaction(callback) {
    return await sql.begin(async sql => {
      return await callback(sql);
    });
  }
};

// Supabase Auth helpers
const supabaseAuthHelpers = {
  // Create user with Supabase Auth
  createUser: async (email, password, metadata = {}) => {
    if (!supabaseAdmin) {
      throw new Error('Supabase Admin client not configured');
    }

    try {
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: false, // We'll handle email verification manually
        user_metadata: metadata
      });

      if (error) throw error;
      return data.user;
    } catch (error) {
      console.error('❌ Supabase createUser error:', error);
      throw error;
    }
  },

  // Update user
  updateUser: async (userId, updates) => {
    if (!supabaseAdmin) {
      throw new Error('Supabase Admin client not configured');
    }

    try {
      const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, updates);
      if (error) throw error;
      return data.user;
    } catch (error) {
      console.error('❌ Supabase updateUser error:', error);
      throw error;
    }
  },

  // Delete user
  deleteUser: async (userId) => {
    if (!supabaseAdmin) {
      throw new Error('Supabase Admin client not configured');
    }

    try {
      const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
      if (error) throw error;
      return true;
    } catch (error) {
      console.error('❌ Supabase deleteUser error:', error);
      throw error;
    }
  },

  // Get user by ID
  getUserById: async (userId) => {
    if (!supabaseAdmin) {
      throw new Error('Supabase Admin client not configured');
    }

    try {
      const { data, error } = await supabaseAdmin.auth.admin.getUserById(userId);
      if (error) throw error;
      return data.user;
    } catch (error) {
      console.error('❌ Supabase getUserById error:', error);
      throw error;
    }
  },

  // Verify JWT token
  verifyToken: async (token) => {
    if (!supabase) {
      throw new Error('Supabase client not configured');
    }

    try {
      const { data, error } = await supabase.auth.getUser(token);
      if (error) throw error;
      return data.user;
    } catch (error) {
      console.error('❌ Supabase verifyToken error:', error);
      throw error;
    }
  }
};

module.exports = {
  sql,
  supabase, // For backward compatibility
  supabaseAdmin, // For backward compatibility
  dbHelpers,
  supabaseAuthHelpers
}; 
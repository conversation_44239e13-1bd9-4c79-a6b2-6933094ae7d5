# 🔍 Authentication Debug Guide - Product Upload Issue

## 🚨 **Issue Description**

User getting "User not found" error when trying to upload products, even though they are logged in and can access other dashboard features.

## 🔧 **Debugging Steps Added**

### **1. Frontend Debugging**
- ✅ Added user state validation before upload
- ✅ Added token existence check
- ✅ Added authentication test function
- ✅ Added detailed error messages
- ✅ Added debug button to test auth manually

### **2. Backend Debugging**
- ✅ Enhanced JWT verification logging
- ✅ Added user lookup debugging
- ✅ Added token preview logging
- ✅ Improved error messages

## 🧪 **How to Debug**

### **Step 1: Test Authentication**
1. **Go to Products section** in dashboard
2. **Click "🔍 Test Auth" button** in bulk upload area
3. **Check result**: Should show "✅ Authentication working!"

### **Step 2: Check Browser Console**
Open browser developer tools and look for:
```
🔍 Debug Info:
- User: <EMAIL>
- Token exists: true
- Token preview: eyJhbGciOiJIUzI1NiIs...
- Auth test passed: ✅
```

### **Step 3: Check Backend Logs**
Look for these logs in backend terminal:
```
🔍 JWT decoded: { type: 'access', userId: 'uuid-here' }
🔍 User lookup result: Found: <EMAIL>
✅ JWT verification successful for user: <EMAIL>
```

## 🔍 **Common Issues & Solutions**

### **Issue 1: Token Not Found**
**Symptoms**: "No access token found" error
**Solution**: 
```javascript
// Check localStorage
console.log(localStorage.getItem('voicebot_access_token'));
// If null, user needs to log in again
```

### **Issue 2: Invalid Token**
**Symptoms**: JWT verification failed
**Possible Causes**:
- Token expired
- Wrong JWT_SECRET in backend
- Token corrupted

**Solution**: Clear localStorage and log in again

### **Issue 3: User Not in Database**
**Symptoms**: "User not found in database"
**Check**: 
```sql
SELECT * FROM clients WHERE email = '<EMAIL>';
```

### **Issue 4: Inactive User**
**Symptoms**: User found but inactive
**Solution**: 
```sql
UPDATE clients SET is_active = true WHERE email = '<EMAIL>';
```

## 🛠️ **Manual Testing Steps**

### **Test 1: Check User State**
```javascript
// In browser console
console.log('User:', window.user);
console.log('Token:', localStorage.getItem('voicebot_access_token'));
```

### **Test 2: Test API Directly**
```javascript
// Test profile endpoint
fetch('http://localhost:5000/api/profile', {
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('voicebot_access_token')}`,
    'Content-Type': 'application/json'
  }
}).then(r => r.json()).then(console.log);
```

### **Test 3: Test Product Upload Endpoint**
```javascript
// Test with minimal data
const formData = new FormData();
formData.append('files', new File(['test'], 'test.txt', {type: 'text/plain'}));

fetch('http://localhost:5000/api/products/ai-bulk-upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('voicebot_access_token')}`
  },
  body: formData
}).then(r => r.json()).then(console.log);
```

## 🔧 **Quick Fixes**

### **Fix 1: Refresh Authentication**
```javascript
// Clear and re-login
localStorage.clear();
// Then log in again
```

### **Fix 2: Check Database Connection**
```bash
# In backend terminal
cd Telecalling/backend
node -e "
const { dbHelpers } = require('./config/supabase');
dbHelpers.findOne('clients', {}).then(console.log);
"
```

### **Fix 3: Verify JWT Secret**
```bash
# Check if JWT_SECRET is set
cd Telecalling/backend
node -e "console.log('JWT_SECRET exists:', !!process.env.JWT_SECRET);"
```

## 📊 **Expected Debug Output**

### **Successful Authentication**
```
Frontend Console:
🔍 Debug Info:
- User: <EMAIL>
- Token exists: true
- Token preview: eyJhbGciOiJIUzI1NiIs...
- Auth test passed: ✅

Backend Console:
🔍 JWT decoded: { type: 'access', userId: '855057c6-1fad-452e-87ee-56d518806c1f' }
🔍 User lookup result: Found: <EMAIL>
✅ JWT verification successful for user: <EMAIL>
```

### **Failed Authentication**
```
Frontend Console:
❌ Authentication test failed: { error: 'Access denied', message: 'User not found' }

Backend Console:
❌ Authentication failed: User not found in database
- Token preview: eyJhbGciOiJIUzI1NiIs...
```

## 🎯 **Next Steps**

1. **Try the "🔍 Test Auth" button** first
2. **Check browser console** for debug info
3. **Check backend logs** for authentication flow
4. **If auth test fails**, try logging out and back in
5. **If still failing**, check database for user record

## 🚀 **Resolution**

Once authentication is working properly:
- ✅ "🔍 Test Auth" button shows success
- ✅ Product upload works without "User not found" error
- ✅ Backend logs show successful JWT verification
- ✅ User ID is properly passed to product insertion

The enhanced debugging will help identify exactly where the authentication is failing and provide clear steps to resolve it.

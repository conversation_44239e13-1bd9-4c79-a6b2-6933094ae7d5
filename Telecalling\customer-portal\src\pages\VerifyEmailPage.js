import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { Mail, CheckCircle, AlertCircle } from 'lucide-react';

const VerifyEmailPage = () => {
  const { token } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState('verifying'); // verifying, success, error
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (token) {
      verifyEmail(token);
    } else {
      setStatus('error');
      setMessage('Invalid verification link');
    }
  }, [token]);

  const verifyEmail = async (verificationToken) => {
    try {
      // TODO: Implement email verification API call
      console.log('Verifying email with token:', verificationToken);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setStatus('success');
      setMessage('Your email has been successfully verified!');
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      setStatus('error');
      setMessage('Email verification failed. The link may be expired or invalid.');
    }
  };

  return (
    <div className="verify-email-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '1rem' }}>
      <div style={{ width: '100%', maxWidth: '400px', textAlign: 'center' }}>
        <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem' }}>
          {status === 'verifying' && (
            <>
              <div style={{ marginBottom: '1.5rem' }}>
                <Mail size={48} style={{ color: '#3b82f6', margin: '0 auto' }} />
              </div>
              <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '1rem' }}>
                Verifying Your Email
              </h1>
              <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>
                Please wait while we verify your email address...
              </p>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <div style={{ 
                  width: '32px', 
                  height: '32px', 
                  border: '3px solid #e5e7eb', 
                  borderTop: '3px solid #3b82f6', 
                  borderRadius: '50%', 
                  animation: 'spin 1s linear infinite' 
                }}></div>
              </div>
            </>
          )}

          {status === 'success' && (
            <>
              <div style={{ marginBottom: '1.5rem' }}>
                <CheckCircle size={48} style={{ color: '#10b981', margin: '0 auto' }} />
              </div>
              <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '1rem' }}>
                Email Verified!
              </h1>
              <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>
                {message}
              </p>
              <p style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                Redirecting you to login page in a few seconds...
              </p>
            </>
          )}

          {status === 'error' && (
            <>
              <div style={{ marginBottom: '1.5rem' }}>
                <AlertCircle size={48} style={{ color: '#ef4444', margin: '0 auto' }} />
              </div>
              <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '1rem' }}>
                Verification Failed
              </h1>
              <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>
                {message}
              </p>
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
                <Link 
                  to="/login" 
                  style={{ 
                    padding: '0.5rem 1rem', 
                    backgroundColor: '#3b82f6', 
                    color: 'white', 
                    textDecoration: 'none', 
                    borderRadius: '4px',
                    fontSize: '0.875rem'
                  }}
                >
                  Go to Login
                </Link>
                <Link 
                  to="/register" 
                  style={{ 
                    padding: '0.5rem 1rem', 
                    backgroundColor: 'white', 
                    color: '#3b82f6', 
                    textDecoration: 'none', 
                    borderRadius: '4px',
                    border: '1px solid #3b82f6',
                    fontSize: '0.875rem'
                  }}
                >
                  Register Again
                </Link>
              </div>
            </>
          )}
        </div>

        <div style={{ textAlign: 'center', marginTop: '2rem' }}>
          <Link 
            to="/" 
            style={{ color: '#6b7280', textDecoration: 'none', fontSize: '0.875rem' }}
          >
            ← Back to Home
          </Link>
        </div>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default VerifyEmailPage;

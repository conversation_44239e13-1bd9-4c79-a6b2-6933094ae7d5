import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { User, Mail, Phone, MapPin, Building, Calendar, Users } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import customerAPI from '../services/customerAPI';

const RegisterPage = () => {
  const { token } = useParams();
  const navigate = useNavigate();
  const { register, validateRegistrationToken } = useAuth();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    dateOfBirth: '',
    gender: ''
  });

  const [tokenData, setTokenData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [validatingToken, setValidatingToken] = useState(!!token);

  useEffect(() => {
    if (token) {
      validateToken();
    }
  }, [token]);

  const validateToken = async () => {
    try {
      setValidatingToken(true);
      const response = await validateRegistrationToken(token);
      
      if (response.success) {
        setTokenData(response.tokenData);
        // Pre-fill phone number if available
        if (response.tokenData.phoneNumber) {
          setFormData(prev => ({
            ...prev,
            phone: response.tokenData.phoneNumber
          }));
        }
      } else {
        setError('Invalid or expired registration link. Please contact the business for a new link.');
      }
    } catch (error) {
      setError('Failed to validate registration link. Please try again.');
    } finally {
      setValidatingToken(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate required fields
      if (!formData.name || !formData.email || !formData.phone) {
        setError('Please fill in all required fields.');
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        setError('Please enter a valid email address.');
        return;
      }

      // Phone validation
      const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
      if (!phoneRegex.test(formData.phone)) {
        setError('Please enter a valid phone number.');
        return;
      }

      const registrationData = { ...formData };
      if (token) {
        registrationData.registrationToken = token;
      }

      const result = await register(registrationData);
      
      if (result.success) {
        setSuccess(result.message);
        setTimeout(() => {
          navigate('/verify-email', { 
            state: { 
              email: formData.email,
              fromRegistration: true 
            } 
          });
        }, 2000);
      } else {
        setError(result.error);
      }
    } catch (error) {
      setError('Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (validatingToken) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-white text-lg">Validating registration link...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-2">
            {token ? 'Complete Your Registration' : 'Create Your Account'}
          </h2>
          <p className="text-blue-100">
            {token && tokenData 
              ? `Register to interact with ${tokenData.businessName || 'the business'}`
              : 'Join our platform to manage your orders and communicate with businesses'
            }
          </p>
        </div>

        {tokenData && (
          <div className="bg-white bg-opacity-10 rounded-lg p-4 mb-6">
            <div className="flex items-center text-white">
              <Building className="w-5 h-5 mr-2" />
              <span className="font-medium">
                Registering for: {tokenData.businessName || 'Business'}
              </span>
            </div>
          </div>
        )}

        <form className="card fade-in" onSubmit={handleSubmit}>
          {error && (
            <div className="error-message mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              {error}
            </div>
          )}

          {success && (
            <div className="success-message mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              {success}
            </div>
          )}

          <div className="grid grid-cols-1 gap-4">
            {/* Name */}
            <div className="form-group">
              <label className="form-label">
                <User className="w-4 h-4 inline mr-2" />
                Full Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Enter your full name"
                required
              />
            </div>

            {/* Email */}
            <div className="form-group">
              <label className="form-label">
                <Mail className="w-4 h-4 inline mr-2" />
                Email Address *
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Enter your email address"
                required
              />
            </div>

            {/* Phone */}
            <div className="form-group">
              <label className="form-label">
                <Phone className="w-4 h-4 inline mr-2" />
                Phone Number *
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Enter your phone number"
                required
              />
            </div>

            {/* Address */}
            <div className="form-group">
              <label className="form-label">
                <MapPin className="w-4 h-4 inline mr-2" />
                Address
              </label>
              <textarea
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className="form-textarea"
                placeholder="Enter your complete address"
                rows="3"
              />
            </div>

            {/* City, State, Pincode */}
            <div className="grid grid-cols-3 gap-3">
              <div className="form-group">
                <label className="form-label">City</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="City"
                />
              </div>
              <div className="form-group">
                <label className="form-label">State</label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="State"
                />
              </div>
              <div className="form-group">
                <label className="form-label">Pincode</label>
                <input
                  type="text"
                  name="pincode"
                  value={formData.pincode}
                  onChange={handleInputChange}
                  className="form-input"
                  placeholder="Pincode"
                />
              </div>
            </div>

            {/* Date of Birth and Gender */}
            <div className="grid grid-cols-2 gap-3">
              <div className="form-group">
                <label className="form-label">
                  <Calendar className="w-4 h-4 inline mr-2" />
                  Date of Birth
                </label>
                <input
                  type="date"
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange}
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label className="form-label">
                  <Users className="w-4 h-4 inline mr-2" />
                  Gender
                </label>
                <select
                  name="gender"
                  value={formData.gender}
                  onChange={handleInputChange}
                  className="form-input"
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer_not_to_say">Prefer not to say</option>
                </select>
              </div>
            </div>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="btn btn-primary w-full mt-6"
          >
            {loading ? (
              <span className="loading">
                <div className="spinner"></div>
                Creating Account...
              </span>
            ) : (
              'Create Account'
            )}
          </button>

          {!token && (
            <div className="text-center mt-4">
              <p className="text-gray-600">
                Already have an account?{' '}
                <Link to="/login" className="text-blue-600 hover:text-blue-800 font-medium">
                  Sign in here
                </Link>
              </p>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default RegisterPage;

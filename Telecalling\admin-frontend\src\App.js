import React, { useState, useEffect } from 'react';
import { Users, MessageSquare, LogOut, Shield, UserPlus, BarChart3, Eye, EyeOff } from 'lucide-react';

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [adminData, setAdminData] = useState(null);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);

  // Super admin specific state
  const [admins, setAdmins] = useState([]);
  const [allClients, setAllClients] = useState([]);
  const [unassignedClients, setUnassignedClients] = useState([]);
  const [dashboardData, setDashboardData] = useState(null);

  // Login form state
  const [loginForm, setLoginForm] = useState({
    adminId: 'admin001',
    password: 'admin123'
  });
  const [loginError, setLoginError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Support tickets state
  const [supportTickets, setSupportTickets] = useState([]);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [ticketMessages, setTicketMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [ticketFilter, setTicketFilter] = useState('all');
  const [showTicketChat, setShowTicketChat] = useState(false);

  // Support ticket functions
  const fetchSupportTickets = async () => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch('http://localhost:4000/api/admin/support/tickets', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSupportTickets(data.tickets || []);
      } else {
        console.error('Failed to fetch support tickets');
      }
    } catch (error) {
      console.error('Error fetching support tickets:', error);
    }
  };

  const fetchTicketMessages = async (ticketId) => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch(`http://localhost:4000/api/admin/support/tickets/${ticketId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTicketMessages(data.messages || []);
      } else {
        console.error('Failed to fetch ticket messages');
      }
    } catch (error) {
      console.error('Error fetching ticket messages:', error);
    }
  };

  const sendTicketMessage = async (ticketId, message) => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch(`http://localhost:4000/api/admin/support/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message })
      });

      if (response.ok) {
        const data = await response.json();
        setTicketMessages(prev => [...prev, data.messageData]);
        setNewMessage('');
        return true;
      } else {
        console.error('Failed to send message');
        return false;
      }
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  };

  const updateTicketStatus = async (ticketId, status) => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch(`http://localhost:4000/api/admin/support/tickets/${ticketId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        // Update the ticket in the local state
        setSupportTickets(prev =>
          prev.map(ticket =>
            ticket.id === ticketId
              ? { ...ticket, status }
              : ticket
          )
        );

        // Update selected ticket if it's the one being updated
        if (selectedTicket && selectedTicket.id === ticketId) {
          setSelectedTicket(prev => ({ ...prev, status }));
        }

        alert(`Ticket ${status} successfully!`);

        // If ticket is closed, close the chat modal
        if (status === 'closed') {
          setShowTicketChat(false);
        }
      } else {
        const errorData = await response.json();
        alert(`Failed to update ticket: ${errorData.message}`);
      }
    } catch (error) {
      console.error('Error updating ticket status:', error);
      alert('Error updating ticket status');
    }
  };

  const fetchAdminProfile = async (token) => {
    try {
      const response = await fetch('http://localhost:4000/api/admin/auth/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('🔐 Admin profile loaded:', data.admin);
        setAdminData(data.admin);
        setIsLoggedIn(true);

        // Check if super admin and fetch appropriate data
        if (data.admin.role === 'super_admin') {
          console.log('👑 Super Admin detected - loading super admin interface');
          setActiveSection('dashboard'); // Ensure super admin starts with dashboard
          fetchSuperAdminData(token);
        } else {
          console.log('👤 Regular Admin detected - loading regular admin interface');
          setActiveSection('dashboard'); // Ensure regular admin starts with dashboard
          fetchClients(token);
        }
      } else {
        localStorage.removeItem('admin_access_token');
      }
    } catch (error) {
      console.error('Error fetching admin profile:', error);
      localStorage.removeItem('admin_access_token');
    }
  };

  // Check if admin is already logged in
  useEffect(() => {
    const token = localStorage.getItem('admin_access_token');
    if (token) {
      fetchAdminProfile(token);
    }
  }, []);

  // Force super admin to correct sections
  useEffect(() => {
    if (adminData?.role === 'super_admin' && activeSection === 'clients') {
      console.log('🚨 Super admin trying to access regular admin section - redirecting to all-clients');
      setActiveSection('all-clients');
    }
  }, [adminData, activeSection]);

  // Fetch support tickets when support section is active
  useEffect(() => {
    if (activeSection === 'support' && isLoggedIn) {
      fetchSupportTickets();
    }
  }, [activeSection, isLoggedIn]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setLoginError('');

    try {
      const response = await fetch('http://localhost:4000/api/admin/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(loginForm)
      });

      const data = await response.json();

      if (response.ok) {
        console.log('🔐 Login successful:', data.admin);
        localStorage.setItem('admin_access_token', data.tokens.accessToken);
        setAdminData(data.admin);
        setIsLoggedIn(true);

        // Check if super admin and fetch appropriate data
        if (data.admin.role === 'super_admin') {
          console.log('👑 Super Admin login - loading super admin interface');
          setActiveSection('dashboard'); // Ensure super admin starts with dashboard
          fetchSuperAdminData(data.tokens.accessToken);
        } else {
          console.log('👤 Regular Admin login - loading regular admin interface');
          setActiveSection('dashboard'); // Ensure regular admin starts with dashboard
          fetchClients(data.tokens.accessToken);
        }
      } else {
        setLoginError(data.message || 'Login failed');
      }
    } catch (error) {
      setLoginError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchClients = async (token) => {
    try {
      const response = await fetch('http://localhost:4000/api/admin/panel/clients', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setClients(data.clients || []);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  // Super admin data fetching functions
  const fetchSuperAdminData = async (token) => {
    await Promise.all([
      fetchSuperAdminDashboard(token),
      fetchAllAdmins(token),
      fetchAllClientsWithAdmins(token),
      fetchUnassignedClients(token)
    ]);
  };

  const fetchSuperAdminDashboard = async (token) => {
    try {
      const response = await fetch('http://localhost:4000/api/admin/super/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDashboardData(data.dashboard);
      }
    } catch (error) {
      console.error('Error fetching super admin dashboard:', error);
    }
  };

  const fetchAllAdmins = async (token) => {
    try {
      const response = await fetch('http://localhost:4000/api/admin/super/admins', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAdmins(data.admins || []);
      }
    } catch (error) {
      console.error('Error fetching admins:', error);
    }
  };

  const fetchAllClientsWithAdmins = async (token) => {
    try {
      const response = await fetch('http://localhost:4000/api/admin/super/clients', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAllClients(data.clients || []);
      }
    } catch (error) {
      console.error('Error fetching all clients:', error);
    }
  };

  const fetchUnassignedClients = async (token) => {
    try {
      const response = await fetch('http://localhost:4000/api/admin/super/clients/unassigned', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUnassignedClients(data.clients || []);
      }
    } catch (error) {
      console.error('Error fetching unassigned clients:', error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_access_token');
    setIsLoggedIn(false);
    setAdminData(null);
    setClients([]);
    setAdmins([]);
    setAllClients([]);
    setUnassignedClients([]);
    setDashboardData(null);
  };

  // Super admin action functions
  const assignAdminToClient = async (clientId, adminId) => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch(`http://localhost:4000/api/admin/super/clients/${clientId}/assign-admin`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adminId })
      });

      if (response.ok) {
        await fetchSuperAdminData(token); // Refresh all data
        alert('Admin assigned to client successfully!');
      } else {
        const data = await response.json();
        alert(data.message || 'Failed to assign admin');
      }
    } catch (error) {
      alert('Error assigning admin to client');
    }
  };

  const removeAdminFromClient = async (clientId) => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch(`http://localhost:4000/api/admin/super/clients/${clientId}/remove-admin`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        await fetchSuperAdminData(token); // Refresh all data
        alert('Admin removed from client successfully!');
      } else {
        const data = await response.json();
        alert(data.message || 'Failed to remove admin');
      }
    } catch (error) {
      alert('Error removing admin from client');
    }
  };

  const createNewAdmin = async (adminData) => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch('http://localhost:4000/api/admin/super/admins', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(adminData)
      });

      if (response.ok) {
        await fetchAllAdmins(token); // Refresh admins list
        alert('Admin created successfully!');
        return true;
      } else {
        const data = await response.json();
        alert(data.message || 'Failed to create admin');
        return false;
      }
    } catch (error) {
      alert('Error creating admin');
      return false;
    }
  };

  const assignPlivoNumber = async (clientId, plivoNumber) => {
    try {
      const token = localStorage.getItem('admin_access_token');
      const response = await fetch(`http://localhost:4000/api/admin/panel/clients/${clientId}/assign-number`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ plivoNumber })
      });

      if (response.ok) {
        fetchClients(token); // Refresh clients list
        alert('Plivo number assigned successfully!');
      } else {
        const data = await response.json();
        alert(data.message || 'Failed to assign number');
      }
    } catch (error) {
      alert('Error assigning number');
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="admin-container">
        <div className="admin-header">
          <h1>Telecalling Admin Panel</h1>
          <p>Login to manage clients and support tickets</p>
        </div>
        
        <div className="admin-content">
          <form onSubmit={handleLogin} style={{ maxWidth: '400px', margin: '0 auto' }}>
            <h2>Admin Login</h2>

            <div style={{
              background: '#f8f9fa',
              padding: '15px',
              borderRadius: '8px',
              marginBottom: '20px',
              fontSize: '14px'
            }}>
              <h4 style={{ margin: '0 0 10px 0', color: '#495057' }}>Available Accounts:</h4>
              <div style={{ marginBottom: '8px' }}>
                <strong>Regular Admin:</strong> admin001 / admin123
              </div>
              <div style={{ color: '#dc3545' }}>
                <strong>Super Admin:</strong> superadmin001 / superadmin123
              </div>
            </div>
            
            <div className="form-group">
              <label>Admin ID:</label>
              <input
                type="text"
                value={loginForm.adminId}
                onChange={(e) => setLoginForm({...loginForm, adminId: e.target.value})}
                required
              />
            </div>
            
            <div className="form-group">
              <label>Password:</label>
              <div style={{ position: 'relative' }}>
                <input
                  type={showPassword ? "text" : "password"}
                  value={loginForm.password}
                  onChange={(e) => setLoginForm({...loginForm, password: e.target.value})}
                  required
                  style={{ paddingRight: '40px' }}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '10px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    padding: '0',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
            </div>
            
            {loginError && <div className="error">{loginError}</div>}
            
            <button type="submit" className="btn btn-primary" disabled={loading}>
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </form>
        </div>
      </div>
    );
  }

  // Debug logging for super admin detection
  console.log('🔍 RENDER DEBUG:');
  console.log('   - Admin data:', adminData);
  console.log('   - Admin role:', adminData?.role);
  console.log('   - Is super admin?', adminData?.role === 'super_admin');
  console.log('   - Active section:', activeSection);

  return (
    <div className="admin-container">
      <div className="admin-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1>{adminData?.role === 'super_admin' ? 'Super Admin Panel' : 'Admin Panel'}</h1>
            <p>Welcome, {adminData?.name} ({adminData?.adminId})</p>
            {adminData?.role === 'super_admin' ? (
              <p style={{ color: '#dc3545', fontWeight: 'bold' }}>
                <Shield size={16} style={{ marginRight: '5px' }} />
                Super Administrator - Unlimited Access
              </p>
            ) : (
              <p>Assigned Clients: {clients.length}/250</p>
            )}
          </div>
          <button onClick={handleLogout} className="btn btn-primary">
            <LogOut size={16} style={{ marginRight: '8px' }} />
            Logout
          </button>
        </div>
      </div>

      {/* Debug Panel */}
      <div style={{
        background: '#fff3cd',
        border: '1px solid #ffeaa7',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '20px',
        fontSize: '14px'
      }}>
        <h4 style={{ margin: '0 0 10px 0', color: '#856404' }}>🔍 Debug Info</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          <div><strong>Admin ID:</strong> {adminData?.adminId || 'Not set'}</div>
          <div><strong>Name:</strong> {adminData?.name || 'Not set'}</div>
          <div><strong>Role:</strong> <span style={{ color: adminData?.role === 'super_admin' ? '#dc3545' : '#007bff' }}>{adminData?.role || 'Not set'}</span></div>
          <div><strong>Active Section:</strong> {activeSection}</div>
          <div><strong>Expected Interface:</strong> {adminData?.role === 'super_admin' ? 'Super Admin' : 'Regular Admin'}</div>
          <div><strong>Should See:</strong> {adminData?.role === 'super_admin' ? 'Admin Management, All Clients, Client Assignments' : 'My Clients with phone assignment'}</div>
        </div>
      </div>

      <div style={{ display: 'flex', gap: '20px' }}>
        <div style={{ width: '250px' }}>
          <div className="admin-content">
            <h3>Navigation</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginTop: '15px' }}>
              <button
                className={`btn ${activeSection === 'dashboard' ? 'btn-primary' : ''}`}
                onClick={() => setActiveSection('dashboard')}
              >
                <BarChart3 size={16} style={{ marginRight: '8px' }} />
                Dashboard
              </button>

              {adminData?.role === 'super_admin' ? (
                <>
                  <button
                    className={`btn ${activeSection === 'admins' ? 'btn-primary' : ''}`}
                    onClick={() => setActiveSection('admins')}
                  >
                    <Shield size={16} style={{ marginRight: '8px' }} />
                    Admin Management
                  </button>
                  <button
                    className={`btn ${activeSection === 'all-clients' ? 'btn-primary' : ''}`}
                    onClick={() => setActiveSection('all-clients')}
                  >
                    <Users size={16} style={{ marginRight: '8px' }} />
                    All Clients
                  </button>
                  <button
                    className={`btn ${activeSection === 'assignments' ? 'btn-primary' : ''}`}
                    onClick={() => setActiveSection('assignments')}
                  >
                    <UserPlus size={16} style={{ marginRight: '8px' }} />
                    Client Assignments
                  </button>
                </>
              ) : (
                <button
                  className={`btn ${activeSection === 'clients' ? 'btn-primary' : ''}`}
                  onClick={() => setActiveSection('clients')}
                >
                  <Users size={16} style={{ marginRight: '8px' }} />
                  My Clients
                </button>
              )}

              <button
                className={`btn ${activeSection === 'support' ? 'btn-primary' : ''}`}
                onClick={() => setActiveSection('support')}
              >
                <MessageSquare size={16} style={{ marginRight: '8px' }} />
                Support
              </button>
            </div>
          </div>
        </div>

        <div style={{ flex: 1 }}>
          <div className="admin-content">
            {activeSection === 'dashboard' && (
              <div>
                <h2>{adminData?.role === 'super_admin' ? 'Super Admin Dashboard' : 'Dashboard Overview'}</h2>

                {adminData?.role === 'super_admin' && dashboardData ? (
                  <div>
                    {/* Admin Management Stats */}
                    <h3 style={{ marginTop: '30px', marginBottom: '15px' }}>Admin Management</h3>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                      <div style={{ padding: '20px', background: '#e3f2fd', borderRadius: '8px', border: '1px solid #2196f3' }}>
                        <h4>Total Admins</h4>
                        <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#1976d2' }}>
                          {dashboardData.adminManagement?.totalAdmins || 0}
                        </p>
                      </div>
                      <div style={{ padding: '20px', background: '#fff3e0', borderRadius: '8px', border: '1px solid #ff9800' }}>
                        <h4>Inactive Admins</h4>
                        <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#f57c00' }}>
                          {dashboardData.adminManagement?.inactiveAdmins || 0}
                        </p>
                      </div>
                    </div>

                    {/* Client Management Stats */}
                    <h3 style={{ marginTop: '30px', marginBottom: '15px' }}>Client Management</h3>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                      <div style={{ padding: '20px', background: '#e8f5e8', borderRadius: '8px', border: '1px solid #4caf50' }}>
                        <h4>Total Clients</h4>
                        <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#388e3c' }}>
                          {dashboardData.clientManagement?.totalClients || 0}
                        </p>
                      </div>
                      <div style={{ padding: '20px', background: '#f3e5f5', borderRadius: '8px', border: '1px solid #9c27b0' }}>
                        <h4>Assigned Clients</h4>
                        <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#7b1fa2' }}>
                          {dashboardData.clientManagement?.assignedClients || 0}
                        </p>
                      </div>
                      <div style={{ padding: '20px', background: '#ffebee', borderRadius: '8px', border: '1px solid #f44336' }}>
                        <h4>Unassigned Clients</h4>
                        <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#d32f2f' }}>
                          {dashboardData.clientManagement?.unassignedClients || 0}
                        </p>
                      </div>
                      <div style={{ padding: '20px', background: '#e0f2f1', borderRadius: '8px', border: '1px solid #009688' }}>
                        <h4>Clients with Numbers</h4>
                        <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#00695c' }}>
                          {dashboardData.clientManagement?.clientsWithNumbers || 0}
                        </p>
                      </div>
                    </div>

                    {/* Top Admins by Workload */}
                    {dashboardData.adminManagement?.topAdminsByWorkload?.length > 0 && (
                      <div style={{ marginTop: '30px' }}>
                        <h3>Top Admins by Workload</h3>
                        <div style={{ marginTop: '15px' }}>
                          {dashboardData.adminManagement.topAdminsByWorkload.map(admin => (
                            <div key={admin.id} style={{
                              padding: '15px',
                              background: '#f8f9fa',
                              borderRadius: '8px',
                              marginBottom: '10px',
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center'
                            }}>
                              <div>
                                <strong>{admin.name}</strong> ({admin.adminId})
                              </div>
                              <div style={{ display: 'flex', gap: '20px' }}>
                                <span style={{ color: '#007bff' }}>
                                  {admin.assignedClients} clients
                                </span>
                                <span style={{ color: '#28a745' }}>
                                  {admin.availableSlots} slots available
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Super Admin Quick Actions */}
                    <div style={{ marginTop: '30px' }}>
                      <h3>Quick Actions</h3>
                      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px', marginTop: '15px' }}>
                        <div
                          style={{
                            padding: '20px',
                            background: '#e3f2fd',
                            borderRadius: '8px',
                            border: '1px solid #2196f3',
                            cursor: 'pointer'
                          }}
                          onClick={() => setActiveSection('admins')}
                        >
                          <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>
                            <Shield size={20} style={{ marginRight: '8px' }} />
                            Admin Management
                          </h4>
                          <p style={{ margin: '0', fontSize: '14px', color: '#666' }}>
                            View all admins, create new admin accounts, monitor workloads
                          </p>
                        </div>

                        <div
                          style={{
                            padding: '20px',
                            background: '#e8f5e8',
                            borderRadius: '8px',
                            border: '1px solid #4caf50',
                            cursor: 'pointer'
                          }}
                          onClick={() => setActiveSection('all-clients')}
                        >
                          <h4 style={{ margin: '0 0 10px 0', color: '#388e3c' }}>
                            <Users size={20} style={{ marginRight: '8px' }} />
                            All Clients
                          </h4>
                          <p style={{ margin: '0', fontSize: '14px', color: '#666' }}>
                            View all clients, see admin assignments, manage relationships
                          </p>
                        </div>

                        <div
                          style={{
                            padding: '20px',
                            background: '#f3e5f5',
                            borderRadius: '8px',
                            border: '1px solid #9c27b0',
                            cursor: 'pointer'
                          }}
                          onClick={() => setActiveSection('assignments')}
                        >
                          <h4 style={{ margin: '0 0 10px 0', color: '#7b1fa2' }}>
                            <UserPlus size={20} style={{ marginRight: '8px' }} />
                            Client Assignments
                          </h4>
                          <p style={{ margin: '0', fontSize: '14px', color: '#666' }}>
                            Assign admins to clients, manage unassigned clients
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Regular admin dashboard
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px', marginTop: '20px' }}>
                    <div style={{ padding: '20px', background: '#f8f9fa', borderRadius: '8px' }}>
                      <h4>Total Clients</h4>
                      <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#007bff' }}>{clients.length}</p>
                    </div>
                    <div style={{ padding: '20px', background: '#f8f9fa', borderRadius: '8px' }}>
                      <h4>Active Numbers</h4>
                      <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#28a745' }}>
                        {clients.filter(c => c.assignedPlivoNumber).length}
                      </p>
                    </div>
                    <div style={{ padding: '20px', background: '#f8f9fa', borderRadius: '8px' }}>
                      <h4>Available Slots</h4>
                      <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffc107' }}>
                        {250 - clients.length}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Super Admin - Admin Management */}
            {activeSection === 'admins' && adminData?.role === 'super_admin' && (
              <div>
                <h2>Admin Management</h2>
                <div style={{ marginBottom: '20px' }}>
                  <button
                    className="btn btn-primary"
                    onClick={() => {
                      const adminId = prompt('Enter Admin ID:');
                      const name = prompt('Enter Admin Name:');
                      const email = prompt('Enter Admin Email:');
                      const password = prompt('Enter Admin Password:');

                      if (adminId && name && email && password) {
                        createNewAdmin({ adminId, name, email, password });
                      }
                    }}
                  >
                    <UserPlus size={16} style={{ marginRight: '8px' }} />
                    Create New Admin
                  </button>
                </div>

                <div style={{ background: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
                  <h3>All Admins</h3>
                  {admins.length === 0 ? (
                    <p>No admins found.</p>
                  ) : (
                    <div style={{ marginTop: '15px' }}>
                      {admins.map(admin => (
                        <div key={admin.id} style={{
                          padding: '15px',
                          background: 'white',
                          borderRadius: '8px',
                          marginBottom: '10px',
                          border: '1px solid #dee2e6'
                        }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <div>
                              <h4 style={{ margin: '0 0 5px 0' }}>{admin.name}</h4>
                              <p style={{ margin: '0', color: '#6c757d' }}>
                                ID: {admin.adminId} | Email: {admin.email}
                              </p>
                              <p style={{ margin: '5px 0 0 0', fontSize: '14px' }}>
                                <span style={{ color: admin.isActive ? '#28a745' : '#dc3545' }}>
                                  {admin.isActive ? '● Active' : '● Inactive'}
                                </span>
                                <span style={{ marginLeft: '15px', color: '#007bff' }}>
                                  {admin.assignedClientsCount} clients assigned
                                </span>
                                <span style={{ marginLeft: '15px', color: '#ffc107' }}>
                                  {admin.availableSlots} slots available
                                </span>
                              </p>
                            </div>
                            <div>
                              <button
                                className="btn btn-sm btn-outline-primary"
                                onClick={() => setActiveSection('assignments')}
                                style={{ marginRight: '10px' }}
                              >
                                Manage Clients
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Super Admin - All Clients */}
            {activeSection === 'all-clients' && adminData?.role === 'super_admin' && (
              <div>
                <h2>All Clients</h2>
                <div style={{ background: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
                  {allClients.length === 0 ? (
                    <p>No clients found.</p>
                  ) : (
                    <div>
                      <p style={{ marginBottom: '20px', color: '#6c757d' }}>
                        Total: {allClients.length} clients
                      </p>
                      <div style={{ display: 'grid', gap: '15px' }}>
                        {allClients.map(client => (
                          <div key={client.id} style={{
                            padding: '15px',
                            background: 'white',
                            borderRadius: '8px',
                            border: '1px solid #dee2e6'
                          }}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <div>
                                <h4 style={{ margin: '0 0 5px 0' }}>{client.username}</h4>
                                <p style={{ margin: '0', color: '#6c757d' }}>
                                  {client.email} | {client.businessName || 'No business name'}
                                </p>
                                <p style={{ margin: '5px 0 0 0', fontSize: '14px' }}>
                                  {client.assignedAdmin ? (
                                    <span style={{ color: '#28a745' }}>
                                      ● Assigned to {client.assignedAdmin.name} ({client.assignedAdmin.adminId})
                                    </span>
                                  ) : (
                                    <span style={{ color: '#dc3545' }}>
                                      ● Unassigned
                                    </span>
                                  )}
                                  {client.assignedPlivoNumber && (
                                    <span style={{ marginLeft: '15px', color: '#007bff' }}>
                                      📞 {client.assignedPlivoNumber}
                                    </span>
                                  )}
                                </p>
                              </div>
                              <div>
                                {client.assignedAdmin ? (
                                  <button
                                    className="btn btn-sm btn-outline-danger"
                                    onClick={() => removeAdminFromClient(client.id)}
                                  >
                                    Remove Admin
                                  </button>
                                ) : (
                                  <select
                                    onChange={(e) => {
                                      if (e.target.value) {
                                        assignAdminToClient(client.id, e.target.value);
                                        e.target.value = '';
                                      }
                                    }}
                                    style={{ padding: '5px' }}
                                  >
                                    <option value="">Assign Admin...</option>
                                    {admins.filter(admin => admin.isActive).map(admin => (
                                      <option key={admin.id} value={admin.id}>
                                        {admin.name} ({admin.availableSlots} slots)
                                      </option>
                                    ))}
                                  </select>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Super Admin - Client Assignments */}
            {activeSection === 'assignments' && adminData?.role === 'super_admin' && (
              <div>
                <h2>Client Assignments</h2>

                {/* Unassigned Clients */}
                <div style={{ marginBottom: '30px' }}>
                  <h3 style={{ color: '#dc3545' }}>Unassigned Clients ({unassignedClients.length})</h3>
                  <div style={{ background: '#fff5f5', padding: '20px', borderRadius: '8px', border: '1px solid #f5c6cb' }}>
                    {unassignedClients.length === 0 ? (
                      <p style={{ color: '#28a745' }}>✅ All clients are assigned to admins!</p>
                    ) : (
                      <div style={{ display: 'grid', gap: '10px' }}>
                        {unassignedClients.map(client => (
                          <div key={client.id} style={{
                            padding: '10px',
                            background: 'white',
                            borderRadius: '5px',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                          }}>
                            <div>
                              <strong>{client.username}</strong> ({client.email})
                              {client.businessName && <span style={{ color: '#6c757d' }}> - {client.businessName}</span>}
                            </div>
                            <select
                              onChange={(e) => {
                                if (e.target.value) {
                                  assignAdminToClient(client.id, e.target.value);
                                }
                              }}
                              style={{ padding: '5px' }}
                            >
                              <option value="">Assign to Admin...</option>
                              {admins.filter(admin => admin.isActive && admin.availableSlots > 0).map(admin => (
                                <option key={admin.id} value={admin.id}>
                                  {admin.name} ({admin.availableSlots} slots available)
                                </option>
                              ))}
                            </select>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Admin Workload Overview */}
                <div>
                  <h3>Admin Workload Overview</h3>
                  <div style={{ background: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
                    {admins.map(admin => (
                      <div key={admin.id} style={{
                        padding: '15px',
                        background: 'white',
                        borderRadius: '8px',
                        marginBottom: '15px',
                        border: '1px solid #dee2e6'
                      }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                          <h4 style={{ margin: 0 }}>{admin.name} ({admin.adminId})</h4>
                          <div style={{ display: 'flex', gap: '15px' }}>
                            <span style={{ color: '#007bff' }}>
                              {admin.assignedClientsCount}/250 clients
                            </span>
                            <span style={{ color: admin.availableSlots > 50 ? '#28a745' : admin.availableSlots > 10 ? '#ffc107' : '#dc3545' }}>
                              {admin.availableSlots} slots available
                            </span>
                          </div>
                        </div>
                        <div style={{
                          width: '100%',
                          height: '10px',
                          background: '#e9ecef',
                          borderRadius: '5px',
                          overflow: 'hidden'
                        }}>
                          <div style={{
                            width: `${(admin.assignedClientsCount / 250) * 100}%`,
                            height: '100%',
                            background: admin.assignedClientsCount > 200 ? '#dc3545' : admin.assignedClientsCount > 150 ? '#ffc107' : '#28a745',
                            transition: 'width 0.3s ease'
                          }}></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* SUPER ADMIN WRONG SECTION WARNING */}
            {adminData?.role === 'super_admin' && activeSection === 'clients' && (
              <div style={{
                background: '#f8d7da',
                padding: '30px',
                borderRadius: '8px',
                border: '2px solid #dc3545',
                textAlign: 'center'
              }}>
                <h1 style={{ color: '#721c24', margin: '0 0 20px 0' }}>🚨 SUPER ADMIN ERROR 🚨</h1>
                <h2 style={{ color: '#721c24', margin: '0 0 15px 0' }}>You're in the WRONG section!</h2>
                <p style={{ fontSize: '18px', margin: '0 0 20px 0' }}>
                  Super Admin should <strong>NOT</strong> see phone number assignment!
                </p>
                <div style={{ background: 'white', padding: '20px', borderRadius: '8px', margin: '20px 0' }}>
                  <h3 style={{ color: '#721c24', margin: '0 0 15px 0' }}>Super Admin Should Use:</h3>
                  <ul style={{ textAlign: 'left', fontSize: '16px' }}>
                    <li><strong>Admin Management</strong> - View and create admin accounts</li>
                    <li><strong>All Clients</strong> - See all clients with admin assignments</li>
                    <li><strong>Client Assignments</strong> - Assign ADMINS to clients (not phone numbers)</li>
                  </ul>
                </div>
                <div style={{ marginTop: '25px' }}>
                  <button
                    className="btn btn-primary"
                    onClick={() => setActiveSection('admins')}
                    style={{ marginRight: '15px', padding: '15px 25px', fontSize: '16px' }}
                  >
                    🛡️ Go to Admin Management
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={() => setActiveSection('all-clients')}
                    style={{ marginRight: '15px', padding: '15px 25px', fontSize: '16px' }}
                  >
                    👥 Go to All Clients
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={() => setActiveSection('assignments')}
                    style={{ padding: '15px 25px', fontSize: '16px' }}
                  >
                    ➕ Go to Client Assignments
                  </button>
                </div>
              </div>
            )}

            {/* Regular Admin - My Clients */}
            {activeSection === 'clients' && adminData?.role !== 'super_admin' && (
              <div>
                <h2>My Assigned Clients</h2>
                <div style={{ marginTop: '20px' }}>
                  {clients.length > 0 ? (
                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                      <thead>
                        <tr style={{ background: '#f8f9fa' }}>
                          <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Client</th>
                          <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Email</th>
                          <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Business</th>
                          <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Phone Number</th>
                          <th style={{ padding: '12px', textAlign: 'left', border: '1px solid #ddd' }}>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {clients.map(client => (
                          <tr key={client.id}>
                            <td style={{ padding: '12px', border: '1px solid #ddd' }}>{client.username}</td>
                            <td style={{ padding: '12px', border: '1px solid #ddd' }}>{client.email}</td>
                            <td style={{ padding: '12px', border: '1px solid #ddd' }}>{client.businessName || 'N/A'}</td>
                            <td style={{ padding: '12px', border: '1px solid #ddd' }}>
                              {client.assignedPlivoNumber || 'Not assigned'}
                            </td>
                            <td style={{ padding: '12px', border: '1px solid #ddd' }}>
                              {!client.assignedPlivoNumber && (
                                <button 
                                  className="btn btn-primary"
                                  onClick={() => {
                                    const number = prompt('Enter Plivo number:');
                                    if (number) assignPlivoNumber(client.id, number);
                                  }}
                                  style={{ fontSize: '12px', padding: '5px 10px' }}
                                >
                                  Assign Number
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <p>No clients assigned yet.</p>
                  )}
                </div>
              </div>
            )}

            {activeSection === 'support' && (
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
                  <h2>Support Tickets</h2>
                  <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                    <select
                      value={ticketFilter}
                      onChange={(e) => setTicketFilter(e.target.value)}
                      style={{ padding: '8px', borderRadius: '4px', border: '1px solid #ddd' }}
                    >
                      <option value="all">All Tickets</option>
                      <option value="open">Open</option>
                      <option value="in-progress">In Progress</option>
                      <option value="resolved">Resolved</option>
                      <option value="closed">Closed</option>
                    </select>
                    <button
                      onClick={fetchSupportTickets}
                      className="btn btn-primary"
                      style={{ padding: '8px 16px' }}
                    >
                      Refresh
                    </button>
                  </div>
                </div>

                {showTicketChat && selectedTicket ? (
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: '20px', height: '600px' }}>
                    {/* Ticket Details */}
                    <div style={{ background: '#f8f9fa', padding: '20px', borderRadius: '8px', overflow: 'auto' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                        <h3>Ticket Details</h3>
                        <button
                          onClick={() => setShowTicketChat(false)}
                          style={{ background: 'none', border: 'none', fontSize: '18px', cursor: 'pointer' }}
                        >
                          ×
                        </button>
                      </div>
                      <div style={{ marginBottom: '15px' }}>
                        <strong>Subject:</strong> {selectedTicket.subject}
                      </div>
                      <div style={{ marginBottom: '15px' }}>
                        <strong>Client:</strong> {selectedTicket.clientName} ({selectedTicket.clientEmail})
                      </div>
                      <div style={{ marginBottom: '15px' }}>
                        <strong>Priority:</strong>
                        <span style={{
                          padding: '2px 8px',
                          borderRadius: '4px',
                          marginLeft: '8px',
                          background: selectedTicket.urgencyLevel === 'urgent' ? '#dc3545' :
                                     selectedTicket.urgencyLevel === 'high' ? '#fd7e14' :
                                     selectedTicket.urgencyLevel === 'medium' ? '#ffc107' : '#28a745',
                          color: 'white',
                          fontSize: '12px'
                        }}>
                          {selectedTicket.urgencyLevel?.toUpperCase()}
                        </span>
                      </div>
                      <div style={{ marginBottom: '15px' }}>
                        <strong>Status:</strong>
                        <span style={{
                          padding: '2px 8px',
                          borderRadius: '4px',
                          marginLeft: '8px',
                          background: selectedTicket.status === 'open' ? '#17a2b8' :
                                     selectedTicket.status === 'in-progress' ? '#ffc107' :
                                     selectedTicket.status === 'resolved' ? '#28a745' : '#6c757d',
                          color: 'white',
                          fontSize: '12px'
                        }}>
                          {selectedTicket.status?.toUpperCase()}
                        </span>
                      </div>
                      <div style={{ marginBottom: '15px' }}>
                        <strong>Created:</strong> {new Date(selectedTicket.createdAt).toLocaleString()}
                      </div>
                      <div style={{ marginBottom: '15px' }}>
                        <strong>Original Message:</strong>
                        <div style={{
                          background: 'white',
                          padding: '10px',
                          borderRadius: '4px',
                          marginTop: '8px',
                          border: '1px solid #ddd'
                        }}>
                          {selectedTicket.message}
                        </div>
                      </div>

                      {/* Ticket Actions */}
                      {selectedTicket.status !== 'closed' && (
                        <div style={{ marginTop: '20px' }}>
                          <strong>Actions:</strong>
                          <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
                            {selectedTicket.status !== 'resolved' && (
                              <button
                                onClick={() => updateTicketStatus(selectedTicket.id, 'resolved')}
                                style={{
                                  padding: '8px 16px',
                                  background: '#28a745',
                                  color: 'white',
                                  border: 'none',
                                  borderRadius: '4px',
                                  cursor: 'pointer',
                                  fontSize: '12px'
                                }}
                              >
                                Mark as Resolved
                              </button>
                            )}
                            <button
                              onClick={() => updateTicketStatus(selectedTicket.id, 'closed')}
                              style={{
                                padding: '8px 16px',
                                background: '#dc3545',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              Close Ticket
                            </button>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Chat Interface */}
                    <div style={{ display: 'flex', flexDirection: 'column', background: 'white', borderRadius: '8px', border: '1px solid #ddd' }}>
                      <div style={{ padding: '15px', borderBottom: '1px solid #ddd', background: '#f8f9fa' }}>
                        <h3 style={{ margin: 0 }}>Chat with {selectedTicket.clientName}</h3>
                      </div>

                      {/* Messages */}
                      <div style={{ flex: 1, padding: '15px', overflowY: 'auto', maxHeight: '400px' }}>
                        {ticketMessages.map((msg, index) => (
                          <div key={index} style={{
                            marginBottom: '15px',
                            display: 'flex',
                            justifyContent: msg.senderType === 'admin' ? 'flex-end' : 'flex-start'
                          }}>
                            <div style={{
                              maxWidth: '70%',
                              padding: '10px',
                              borderRadius: '8px',
                              background: msg.senderType === 'admin' ? '#007bff' : '#e9ecef',
                              color: msg.senderType === 'admin' ? 'white' : 'black'
                            }}>
                              <div style={{ fontSize: '12px', marginBottom: '5px', opacity: 0.8 }}>
                                {msg.senderName} - {new Date(msg.createdAt).toLocaleString()}
                              </div>
                              <div>{msg.message}</div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Message Input */}
                      <div style={{ padding: '15px', borderTop: '1px solid #ddd' }}>
                        <div style={{ display: 'flex', gap: '10px' }}>
                          <input
                            type="text"
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            placeholder="Type your message..."
                            style={{
                              flex: 1,
                              padding: '10px',
                              border: '1px solid #ddd',
                              borderRadius: '4px'
                            }}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter' && newMessage.trim()) {
                                sendTicketMessage(selectedTicket.id, newMessage);
                              }
                            }}
                          />
                          <button
                            onClick={() => {
                              if (newMessage.trim()) {
                                sendTicketMessage(selectedTicket.id, newMessage);
                              }
                            }}
                            className="btn btn-primary"
                            disabled={!newMessage.trim()}
                          >
                            Send
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div style={{ background: 'white', borderRadius: '8px', overflow: 'hidden' }}>
                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                      <thead>
                        <tr style={{ background: '#f8f9fa' }}>
                          <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Ticket ID</th>
                          <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Subject</th>
                          <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Client</th>
                          <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Priority</th>
                          <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Status</th>
                          <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Created</th>
                          <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {supportTickets
                          .filter(ticket => ticketFilter === 'all' || ticket.status === ticketFilter)
                          .map((ticket, index) => (
                          <tr key={ticket.id} style={{ borderBottom: '1px solid #eee' }}>
                            <td style={{ padding: '12px' }}>#{ticket.id.slice(-8)}</td>
                            <td style={{ padding: '12px' }}>{ticket.subject}</td>
                            <td style={{ padding: '12px' }}>{ticket.clientName}</td>
                            <td style={{ padding: '12px' }}>
                              <span style={{
                                padding: '2px 8px',
                                borderRadius: '4px',
                                background: ticket.urgencyLevel === 'urgent' ? '#dc3545' :
                                           ticket.urgencyLevel === 'high' ? '#fd7e14' :
                                           ticket.urgencyLevel === 'medium' ? '#ffc107' : '#28a745',
                                color: 'white',
                                fontSize: '12px'
                              }}>
                                {ticket.urgencyLevel?.toUpperCase()}
                              </span>
                            </td>
                            <td style={{ padding: '12px' }}>
                              <span style={{
                                padding: '2px 8px',
                                borderRadius: '4px',
                                background: ticket.status === 'open' ? '#17a2b8' :
                                           ticket.status === 'in-progress' ? '#ffc107' :
                                           ticket.status === 'resolved' ? '#28a745' : '#6c757d',
                                color: 'white',
                                fontSize: '12px'
                              }}>
                                {ticket.status?.toUpperCase()}
                              </span>
                            </td>
                            <td style={{ padding: '12px' }}>{new Date(ticket.createdAt).toLocaleDateString()}</td>
                            <td style={{ padding: '12px' }}>
                              <button
                                onClick={() => {
                                  setSelectedTicket(ticket);
                                  setShowTicketChat(true);
                                  fetchTicketMessages(ticket.id);
                                }}
                                className="btn btn-primary"
                                style={{ padding: '4px 8px', fontSize: '12px' }}
                              >
                                Open Chat
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>

                    {supportTickets.filter(ticket => ticketFilter === 'all' || ticket.status === ticketFilter).length === 0 && (
                      <div style={{ padding: '40px', textAlign: 'center', color: '#666' }}>
                        No support tickets found.
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;

// Add CSS styles
const styles = `
  .admin-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }
  .admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
  }
  .admin-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .form-group {
    margin-bottom: 15px;
  }
  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
  .form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }
  .error {
    color: #dc3545;
    background: #f8d7da;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
  }
  .btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    text-align: left;
    width: 100%;
    font-size: 14px;
  }
  .btn:hover {
    background: #f5f5f5;
  }
  .btn-primary {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
  .btn-primary:hover {
    background: #0056b3;
  }
  .btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    width: auto;
  }
  .btn-outline-primary {
    background: white;
    color: #007bff;
    border-color: #007bff;
  }
  .btn-outline-primary:hover {
    background: #007bff;
    color: white;
  }
  .btn-outline-danger {
    background: white;
    color: #dc3545;
    border-color: #dc3545;
  }
  .btn-outline-danger:hover {
    background: #dc3545;
    color: white;
  }
  .super-admin-badge {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

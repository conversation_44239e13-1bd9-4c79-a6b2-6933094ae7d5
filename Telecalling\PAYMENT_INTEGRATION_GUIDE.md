# 💳 Razorpay Payment Integration Guide

This guide covers the complete Razorpay payment integration for the VoiceBot telecalling system.

## 🚀 Features Implemented

### ✅ Core Payment Features
- **Plan-based Subscriptions**: Multiple subscription plans (Free, Basic, Pro, Business, Enterprise)
- **Razorpay Integration**: Secure payment processing with <PERSON><PERSON>pay
- **Payment Verification**: Server-side payment signature verification
- **Subscription Management**: Automatic subscription activation and management
- **Payment History**: Complete payment transaction tracking
- **Dev Mode Undo**: Testing functionality to undo payments in development

### ✅ User Experience
- **Landing Page Integration**: Plan selection directly from landing page
- **Seamless Flow**: Signup/Login → Plan Selection → Payment → Dashboard
- **Payment Modals**: Beautiful payment interface with Razorpay checkout
- **Success Handling**: Payment success confirmation and dashboard redirect
- **Dashboard Integration**: Current plan display and upgrade options

### ✅ Security & Compliance
- **Signature Verification**: All payments verified with Razorpay signatures
- **Webhook Security**: Secure webhook handling with signature validation
- **Plan Enforcement**: Middleware to enforce plan limits and features
- **Audit Logging**: Payment activity logging for compliance

## 📋 Setup Instructions

### 1. Backend Setup

#### Install Dependencies
```bash
cd Telecalling/backend
npm install razorpay
```

#### Environment Variables
Add to your `.env` file:
```env
# Razorpay Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret
```

#### Database Migration
Run the database migration to create payment tables:
```bash
# Execute the SQL in your Supabase SQL Editor
cat database/create_all_tables.sql
```

This creates:
- `subscription_plans` - Available subscription plans
- `client_subscriptions` - User subscription records
- `payment_transactions` - Payment transaction logs
- Updates `clients` table with payment-related columns

### 2. Frontend Setup

#### Environment Variables
Add to your `frontend/.env` file:
```env
REACT_APP_RAZORPAY_KEY_ID=your_razorpay_key_id
```

#### Dependencies
The required dependencies are already installed:
- `lucide-react` for icons
- `axios` for API calls

### 3. Razorpay Dashboard Setup

1. **Create Razorpay Account**: Sign up at [razorpay.com](https://razorpay.com)
2. **Get API Keys**: 
   - Go to Settings → API Keys
   - Generate Key ID and Key Secret
   - Use Test keys for development
3. **Setup Webhooks**:
   - Go to Settings → Webhooks
   - Add webhook URL: `https://yourdomain.com/api/subscriptions/webhook`
   - Select events: `payment.captured`, `payment.failed`
   - Generate webhook secret

## 🔄 Payment Flow

### User Journey
1. **Landing Page**: User selects a plan
2. **Authentication**: User signs up or logs in
3. **Payment Modal**: Razorpay checkout opens
4. **Payment Processing**: User completes payment
5. **Verification**: Server verifies payment signature
6. **Subscription Activation**: User subscription is activated
7. **Dashboard Redirect**: User sees updated plan status

### Technical Flow
```
Frontend                Backend                 Razorpay
   |                       |                       |
   |-- Select Plan ------->|                       |
   |<-- Store Plan --------|                       |
   |                       |                       |
   |-- Create Order ------>|-- Create Order ------>|
   |<-- Order Details -----|<-- Order Response ----|
   |                       |                       |
   |-- Open Razorpay ----->|                       |
   |<-- Payment Success ---|                       |
   |                       |                       |
   |-- Verify Payment ---->|-- Verify Signature -->|
   |<-- Success Response --|<-- Webhook Event -----|
   |                       |                       |
   |-- Redirect Dashboard->|                       |
```

## 🧪 Testing

### Run Integration Test
```bash
cd Telecalling/backend
node test_payment_integration.js
```

### Test Payment Flow
1. **Start Backend**: `npm run dev`
2. **Start Frontend**: `cd ../frontend && npm start`
3. **Open Landing Page**: `http://localhost:3000`
4. **Select Plan**: Click "Get Started" on any paid plan
5. **Sign Up/Login**: Complete authentication
6. **Payment Modal**: Should open automatically
7. **Test Payment**: Use Razorpay test cards

### Razorpay Test Cards
```
Success: 4111 1111 1111 1111
Failure: 4000 0000 0000 0002
CVV: Any 3 digits
Expiry: Any future date
```

### Dev Mode Undo
- Available only in development mode
- Access via dashboard subscription status component
- Cancels subscription and reverts to free plan

## 📊 Subscription Plans

### Default Plans Created
1. **Free Plan**: ₹0 - Basic features for testing
2. **Basic Plan**: ₹499/month - Small businesses
3. **Pro Plan**: ₹999/month - Growing businesses  
4. **Business Plan**: ₹1,999/month - Established businesses
5. **Enterprise Plan**: Custom pricing - Large organizations

### Plan Features
Each plan includes:
- **Call Limits**: Maximum calls per month
- **Phone Numbers**: Number of phone numbers allowed
- **Products**: Maximum products in catalog
- **Support Level**: Email, Priority, or Dedicated
- **Advanced Features**: API access, custom responses, etc.

## 🔒 Security Considerations

### Payment Security
- All payments processed through Razorpay (PCI DSS compliant)
- No card details stored on your servers
- Payment signatures verified server-side
- Webhook signatures validated

### Plan Enforcement
- Middleware checks subscription status
- Plan limits enforced at API level
- Feature access controlled by plan
- Automatic subscription expiry handling

## 🚨 Production Checklist

### Before Going Live
- [ ] Replace test Razorpay keys with live keys
- [ ] Set up production webhook URLs
- [ ] Configure proper CORS settings
- [ ] Set up SSL certificates
- [ ] Test webhook delivery
- [ ] Set up monitoring and alerts
- [ ] Configure backup payment methods
- [ ] Test subscription renewal flows

### Monitoring
- Monitor webhook delivery success
- Track payment success/failure rates
- Monitor subscription churn
- Set up alerts for failed payments

## 🛠️ Customization

### Adding New Plans
1. Insert into `subscription_plans` table
2. Update frontend plan display
3. Configure plan features and limits

### Custom Payment Flow
- Modify `PaymentModal.js` for UI changes
- Update `subscriptionAPI.js` for API changes
- Customize success/failure handling

### Webhook Handling
- Add new webhook events in `subscriptions.js`
- Implement custom business logic
- Add notification systems

## 📞 Support

### Common Issues
1. **Payment Modal Not Opening**: Check Razorpay script loading
2. **Signature Verification Failed**: Verify webhook secret
3. **Subscription Not Activated**: Check database permissions
4. **Plan Limits Not Working**: Verify middleware implementation

### Debug Mode
Enable debug logging by setting:
```env
NODE_ENV=development
```

### Contact
For technical support:
- Email: <EMAIL>
- Documentation: This guide
- Test Script: `test_payment_integration.js`

## 🎉 Congratulations!

Your Razorpay payment integration is now complete! Users can:
- Select plans from the landing page
- Complete secure payments
- Access plan-based features
- Manage their subscriptions
- Upgrade/downgrade plans

The system is production-ready with proper security, error handling, and user experience considerations.

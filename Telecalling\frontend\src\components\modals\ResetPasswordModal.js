import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import resetPasswordImage from '../../assets/images/reset-password.jpeg';
import './ResetPasswordModal.css';

const ResetPasswordModal = ({ isOpen, onClose, onPasswordReset, onBackToLogin, userEmail }) => {
  const { resetPassword } = useAuth();
  const [formData, setFormData] = useState({
    otp: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState('');

  // Debug: Log userEmail when component receives it
  console.log('ResetPasswordModal received userEmail:', userEmail);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.otp) {
      newErrors.otp = 'Reset code is required';
    } else if (!/^\d{6}$/.test(formData.otp)) {
      newErrors.otp = 'Reset code must be 6 digits';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long';
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (!userEmail) {
      newErrors.general = 'Email address is missing';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Check if userEmail is available
    if (!userEmail) {
      setErrors({ general: 'Email address is missing. Please go back and try the forgot password process again.' });
      return;
    }

    setIsLoading(true);

    try {
      console.log('Attempting password reset with email:', userEmail);
      const result = await resetPassword(userEmail, formData.otp, formData.password);
      
      if (result.success) {
        setSuccess('Password reset successfully!');
        
        // Clear form
        setFormData({
          otp: '',
          password: '',
          confirmPassword: ''
        });
        
        // Navigate to login after a short delay
        setTimeout(() => {
          if (onPasswordReset) {
            onPasswordReset();
          } else {
            onBackToLogin && onBackToLogin();
          }
        }, 2000);
      } else {
        setErrors({ general: result.error });
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setErrors({ general: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setFormData({
      otp: '',
      password: '',
      confirmPassword: ''
    });
    setErrors({});
    setSuccess('');
    onBackToLogin && onBackToLogin();
  };

  if (!isOpen) return null;

  return (
    <div className="reset-password-modal-overlay" onClick={onClose}>
      <div 
        className="reset-password-modal-container" 
        onClick={e => e.stopPropagation()}
        style={{'--modal-bg-image': `url(${resetPasswordImage})`}}
      >

        {/* Left side - Form */}
        <div className="modal-form-section">
          <div className="form-content">
            {/* Header */}
            <div className="modal-header">
              <div className="modal-logo">
                <span className="logo-text">VoiceBot Platform</span>
              </div>
              <p className="modal-tagline">Revolutionizing Communication.</p>
            </div>

            {/* Main content */}
            <div className="form-main">
              <h2 className="form-title">Reset Your Password</h2>
              <p className="form-subtitle">
                Enter the code we sent to <strong>{userEmail}</strong> and your new password.
              </p>

              {/* Success message */}
              {success && (
                <div className="success-message">
                  {success}
                </div>
              )}

              {/* Error message */}
              {errors.general && (
                <div className="error-message">
                  {errors.general}
                </div>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit} className="reset-password-form">
                <div className="modal-form-group">
                  <input
                    type="text"
                    id="otp"
                    name="otp"
                    placeholder="Enter 6-digit code"
                    value={formData.otp}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    maxLength="6"
                    pattern="[0-9]*"
                    inputMode="numeric"
                    required
                    autoFocus
                  />
                  <label htmlFor="otp">Reset Code</label>
                  {errors.otp && <span className="field-error">{errors.otp}</span>}
                </div>

                <div className="modal-form-group">
                  <div className="password-input-wrapper">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      placeholder="Enter new password"
                      value={formData.password}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      required
                    />
                    <label htmlFor="password">New Password</label>
                    <button
                      type="button"
                      className="password-toggle"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                          <line x1="1" y1="1" x2="23" y2="23"></line>
                        </svg>
                      ) : (
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                      )}
                    </button>
                  </div>
                  <label htmlFor="password">New Password</label>
                  {errors.password && <span className="field-error">{errors.password}</span>}
                </div>

                <div className="modal-form-group">
                  <div className="password-input-wrapper">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      id="confirmPassword"
                      name="confirmPassword"
                      placeholder="Confirm new password"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      required
                    />
                    <label htmlFor="confirmPassword">Confirm Password</label>
                    <button
                      type="button"
                      className="password-toggle"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      disabled={isLoading}
                    >
                      {showConfirmPassword ? (
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                          <line x1="1" y1="1" x2="23" y2="23"></line>
                        </svg>
                      ) : (
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                      )}
                    </button>
                  </div>
                  <label htmlFor="confirmPassword">Confirm Password</label>
                  {errors.confirmPassword && <span className="field-error">{errors.confirmPassword}</span>}
                </div>

                <div className="password-requirements">
                  <h4>Password Requirements:</h4>
                  <ul>
                    <li className={formData.password.length >= 8 ? 'met' : ''}>
                      At least 8 characters long
                    </li>
                    <li className={/[A-Z]/.test(formData.password) ? 'met' : ''}>
                      One uppercase letter
                    </li>
                    <li className={/[a-z]/.test(formData.password) ? 'met' : ''}>
                      One lowercase letter
                    </li>
                    <li className={/\d/.test(formData.password) ? 'met' : ''}>
                      One number
                    </li>
                  </ul>
                </div>

                <button 
                  type="submit" 
                  className="submit-btn"
                  disabled={isLoading || !formData.otp || !formData.password || !formData.confirmPassword}
                >
                  {isLoading ? 'Resetting Password...' : 'Reset Password'}
                </button>
              </form>

              <div className="form-footer">
                <p>
                  Remember your password? 
                  <button 
                    type="button" 
                    className="link-btn"
                    onClick={handleBackToLogin}
                  >
                    Back to Login
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Image */}
        <div className="modal-image-section">
          <img src={resetPasswordImage} alt="Reset Password" className="modal-image" />
          <div className="image-overlay">
            {/* Decorative white shapes */}
            <div className="decorative-shape top-left-shape"></div>
            <div className="decorative-shape bottom-right-shape"></div>
            
            <div className="overlay-content">
              <div className="overlay-card">
                <h3>Almost Done!</h3>
                <p>Create a strong new password to secure your VoiceBot Platform account.</p>
              </div>
              <div className="bottom-overlay">
                <h3>Secure Your Future,<br/>One Password at a Time!</h3>
                <p>Strong passwords protect your communications.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordModal; 
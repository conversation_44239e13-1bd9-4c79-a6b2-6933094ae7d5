import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { authAPI, authHelpers } from '../services/api';
import { googleAuth, onAuthStateChange } from '../config/supabase';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check if user is authenticated on app load
  useEffect(() => {
    const checkAuth = async () => {
      const isLoggedIn = authHelpers.isAuthenticated();
      
      if (isLoggedIn) {
        try {
          // Get user from localStorage first
          const savedUser = authHelpers.getCurrentUser();
          if (savedUser) {
            setUser(savedUser);
            setIsAuthenticated(true);
          }
          
          // Verify token and get latest user profile
          const response = await authAPI.getProfile();
          const userData = response.data.user;
          
          setUser(userData);
          setIsAuthenticated(true);
          authHelpers.setCurrentUser(userData);
        } catch (error) {
          console.error('Token verification failed:', error);
          authHelpers.clearAuth();
          setUser(null);
          setIsAuthenticated(false);
        }
      }
      
      setLoading(false);
    };

    checkAuth();
  }, []);

  const signup = async (userData) => {
    try {
      const response = await authAPI.signup(userData);
      const { user: newUser } = response.data;
      
      return { 
        success: true, 
        user: newUser,
        message: response.data.message
      };
    } catch (error) {
      console.error('Signup failed:', error);
      return { 
        success: false, 
        error: error.response?.data?.error || 'Signup failed' 
      };
    }
  };

  const login = async (email, password) => {
    try {
      const response = await authAPI.login({ email, password });
      const { user: userData, tokens } = response.data;
      
      setUser(userData);
      setIsAuthenticated(true);
      authHelpers.setAuthData(tokens, userData);
      
      return { success: true, user: userData };
    } catch (error) {
      console.error('Login failed:', error);
      const errorData = error.response?.data;
      
      return { 
        success: false, 
        error: errorData?.error || 'Login failed',
        code: errorData?.code,
        userId: errorData?.userId
      };
    }
  };

  const verifyEmail = async (email, otp) => {
    try {
      const response = await authAPI.verifyEmail({ email, otp });
      const { user: userData } = response.data;
      
      // Update user state if currently logged in
      if (user && user.email === email) {
        setUser(userData);
        authHelpers.setCurrentUser(userData);
      }
      
      return { 
        success: true, 
        user: userData,
        message: response.data.message
      };
    } catch (error) {
      console.error('Email verification failed:', error);
      return { 
        success: false, 
        error: error.response?.data?.error || 'Email verification failed' 
      };
    }
  };

  const resendVerification = async (email) => {
    try {
      const response = await authAPI.resendVerification(email);
      
      return { 
        success: true,
        message: response.data.message
      };
    } catch (error) {
      console.error('Resend verification failed:', error);
      return { 
        success: false, 
        error: error.response?.data?.error || 'Failed to resend verification code' 
      };
    }
  };

  const forgotPassword = async (email) => {
    try {
      const response = await authAPI.forgotPassword(email);
      
      return { 
        success: true,
        message: response.data.message
      };
    } catch (error) {
      console.error('Forgot password failed:', error);
      return { 
        success: false, 
        error: error.response?.data?.error || 'Failed to send password reset code' 
      };
    }
  };

  const resetPassword = async (email, otp, newPassword) => {
    try {
      const response = await authAPI.resetPassword({ email, otp, newPassword });

      return {
        success: true,
        message: response.data.message
      };
    } catch (error) {
      console.error('Password reset failed:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Password reset failed'
      };
    }
  };

  // Google OAuth methods
  const signInWithGoogle = async () => {
    try {
      const result = await googleAuth.signInWithGoogle();
      if (result.success) {
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Google sign in failed:', error);
      return { success: false, error: 'Google sign in failed' };
    }
  };

  const signUpWithGoogle = async () => {
    try {
      const result = await googleAuth.signUpWithGoogle();
      if (result.success) {
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Google sign up failed:', error);
      return { success: false, error: 'Google sign up failed' };
    }
  };

  // Handle Google auth callback with request deduplication
  const [googleCallbackInProgress, setGoogleCallbackInProgress] = useState(false);
  const googleCallbackCache = useRef(new Map());

  const handleGoogleAuthCallback = async (session) => {
    const userEmail = session.user.email;
    const cacheKey = `google_callback_${userEmail}`;

    // Check if this callback is already being processed
    if (googleCallbackInProgress) {
      console.log('🔄 Google auth callback already in progress, waiting...');
      return { success: false, error: 'Authentication already in progress. Please wait.' };
    }

    // Check cache for recent successful authentication
    const cachedResult = googleCallbackCache.current.get(cacheKey);
    if (cachedResult && (Date.now() - cachedResult.timestamp) < 5000) {
      console.log('🔄 Using cached Google auth result for:', userEmail);
      return cachedResult.result;
    }

    try {
      setGoogleCallbackInProgress(true);
      console.log('🔄 Processing Google auth callback for user:', userEmail);

      // Send Google user data to backend to create/update client record
      const response = await authAPI.googleAuth({
        supabaseUser: session.user,
        accessToken: session.access_token,
        refreshToken: session.refresh_token
      });

      const { user: userData, tokens } = response.data;

      // Set user state
      setUser(userData);
      setIsAuthenticated(true);

      // IMPORTANT: Always use the custom JWT tokens from backend
      // This ensures consistency with the rest of the auth system
      if (tokens && tokens.accessToken) {
        console.log('🔄 Setting auth data in localStorage...');
        console.log('🔄 Tokens received:', {
          hasAccessToken: !!tokens.accessToken,
          hasRefreshToken: !!tokens.refreshToken
        });
        console.log('🔄 User data:', {
          email: userData.email,
          id: userData.id
        });

        authHelpers.setAuthData(tokens, userData);

        // Verify data was stored
        const storedAccessToken = localStorage.getItem('voicebot_access_token');
        const storedUser = localStorage.getItem('voicebot_user');

        console.log('✅ Auth data stored verification:');
        console.log('✅ Access token stored:', !!storedAccessToken);
        console.log('✅ User data stored:', !!storedUser);
        console.log('✅ Google auth completed with custom JWT tokens');
        console.log('✅ User authenticated:', userData.email);
      } else {
        console.error('❌ No custom tokens received from backend');
        console.error('❌ Response data:', response.data);
        throw new Error('Authentication tokens not received');
      }

      // Include tokens in the result for direct localStorage access
      const result = {
        success: true,
        user: userData,
        tokens: tokens
      };

      // Cache successful result
      googleCallbackCache.current.set(cacheKey, {
        result,
        timestamp: Date.now()
      });

      return result;
    } catch (error) {
      console.error('❌ Google auth callback failed:', error);

      // Handle specific error cases
      if (error.response?.status === 409) {
        return {
          success: false,
          error: 'Multiple login attempts detected. Please try again in a moment.'
        };
      } else if (error.response?.status === 429) {
        const retryAfter = error.response?.data?.retryAfter;
        return {
          success: false,
          error: retryAfter ? `Please wait ${retryAfter} seconds before trying again.` : 'Too many requests. Please wait and try again.'
        };
      }

      return {
        success: false,
        error: error.response?.data?.error || 'Failed to complete Google authentication'
      };
    } finally {
      setGoogleCallbackInProgress(false);

      // Clean up cache after 10 seconds
      setTimeout(() => {
        googleCallbackCache.current.delete(cacheKey);
      }, 10000);
    }
  };

  const logout = async () => {
    try {
      const refreshToken = authHelpers.getRefreshToken();
      if (refreshToken) {
        await authAPI.logout(refreshToken);
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      authHelpers.clearAuth();
      // Redirect to landing page with login modal trigger
      window.location.href = '/?showLogin=true';
    }
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await authAPI.updateProfile(profileData);
      const updatedUser = response.data.user;
      
      setUser(updatedUser);
      authHelpers.setCurrentUser(updatedUser);
      
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Profile update failed:', error);
      return { 
        success: false, 
        error: error.response?.data?.error || 'Profile update failed' 
      };
    }
  };

  const refreshUserData = async () => {
    try {
      const response = await authAPI.getProfile();
      const userData = response.data.user;
      
      setUser(userData);
      authHelpers.setCurrentUser(userData);
      
      return userData;
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      return null;
    }
  };

  // Check if user has verified email
  const isEmailVerified = () => {
    return user?.isEmailVerified || false;
  };

  // Check if user account is active
  const isAccountActive = () => {
    return user?.isActive || false;
  };

  // Get user's business name
  const getBusinessName = () => {
    return user?.businessName || null;
  };

  // Get user's username
  const getUsername = () => {
    return user?.username || null;
  };

  // Get user's email
  const getUserEmail = () => {
    return user?.email || null;
  };

  // Check if user data is complete
  const isProfileComplete = () => {
    return user && user.email && (user.username || user.businessName);
  };

  const value = {
    // State
    user,
    loading,
    isAuthenticated,

    // Auth methods
    signup,
    login,
    logout,
    verifyEmail,
    resendVerification,
    forgotPassword,
    resetPassword,

    // Google OAuth methods
    signInWithGoogle,
    signUpWithGoogle,
    handleGoogleAuthCallback,

    // Profile methods
    updateProfile,
    refreshUserData,

    // Helper methods
    isEmailVerified,
    isAccountActive,
    getBusinessName,
    getUsername,
    getUserEmail,
    isProfileComplete,

    // Legacy methods for backward compatibility
    register: signup,
    changePassword: resetPassword
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 
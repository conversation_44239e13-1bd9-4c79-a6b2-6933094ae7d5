const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:5000/api';

// Test authentication flow
async function testAuthFlow() {
  console.log('🧪 Testing Authentication Flow...\n');

  const testEmail = `test${Date.now()}@example.com`;
  const testPassword = 'password123';
  const testBusinessName = 'Test Business';
  const testUsername = 'testuser';

  try {
    // Step 1: Test Signup
    console.log('1️⃣ Testing Signup...');
    const signupResponse = await axios.post(`${BASE_URL}/auth/signup`, {
      email: testEmail,
      password: testPassword,
      businessName: testBusinessName,
      username: testUsername
    });

    console.log('✅ Signup successful:', signupResponse.data);
    console.log('📧 Check your email for OTP\n');

    // Step 2: Test Environment Variables
    console.log('2️⃣ Testing Environment Variables...');
    console.log('JWT_SECRET exists:', !!process.env.JWT_SECRET);
    console.log('JWT_REFRESH_SECRET exists:', !!process.env.JWT_REFRESH_SECRET);
    console.log('DATABASE_URL exists:', !!process.env.DATABASE_URL);
    console.log('SUPABASE_URL exists:', !!process.env.SUPABASE_URL);
    console.log('RESEND_API_KEY exists:', !!process.env.RESEND_API_KEY);
    console.log();

    // Step 3: Test OTP Verification (you'll need to manually enter OTP)
    console.log('3️⃣ To test OTP verification, run:');
    console.log(`curl -X POST ${BASE_URL}/auth/verify-email \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{`);
    console.log(`    "email": "${testEmail}",`);
    console.log(`    "otp": "YOUR_OTP_HERE"`);
    console.log(`  }'`);
    console.log();

    // Step 4: Test Login (will fail until OTP is verified)
    console.log('4️⃣ Testing Login (will fail until OTP verified)...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: testEmail,
        password: testPassword
      });
      console.log('✅ Login successful:', loginResponse.data);
    } catch (loginError) {
      console.log('❌ Login failed (expected until OTP verified):', loginError.response?.data?.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Test JWT token generation
function testJWTGeneration() {
  console.log('\n🔐 Testing JWT Generation...');
  
  const jwt = require('jsonwebtoken');
  
  try {
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET not found');
    }
    
    if (!process.env.JWT_REFRESH_SECRET) {
      throw new Error('JWT_REFRESH_SECRET not found');
    }

    const testUserId = 'test-user-id';
    
    const accessToken = jwt.sign(
      { userId: testUserId, type: 'access' },
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );
    
    const refreshToken = jwt.sign(
      { userId: testUserId, type: 'refresh' },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: '7d' }
    );
    
    console.log('✅ JWT generation successful');
    console.log('Access token length:', accessToken.length);
    console.log('Refresh token length:', refreshToken.length);
    
    // Test verification
    const decoded = jwt.verify(accessToken, process.env.JWT_SECRET);
    console.log('✅ JWT verification successful:', decoded);
    
  } catch (error) {
    console.error('❌ JWT test failed:', error.message);
  }
}

// Test database connection
async function testDatabaseConnection() {
  console.log('\n🗄️ Testing Database Connection...');
  
  try {
    const { dbHelpers } = require('./config/supabase');
    
    // Test a simple query
    const result = await dbHelpers.query('SELECT NOW() as current_time');
    console.log('✅ Database connection successful:', result[0]);
    
    // Test table existence
    const tables = await dbHelpers.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('clients', 'pending_registrations', 'otp_verifications', 'login_sessions')
      ORDER BY table_name
    `);
    
    console.log('📊 Authentication tables found:');
    tables.forEach(table => console.log(`  - ${table.table_name}`));
    
    if (tables.length < 4) {
      console.log('⚠️ Some authentication tables are missing. Run the database creation script.');
    }
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  }
}

// Test Supabase Edge Function
async function testEdgeFunction() {
  console.log('\n🌐 Testing Supabase Edge Function...');
  
  try {
    const response = await axios.post(
      `${process.env.SUPABASE_URL}/functions/v1/send-otp-email`,
      {
        email: '<EMAIL>',
        otp: '123456',
        type: 'email_verification',
        userName: 'Test User',
        businessName: 'Test Business'
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Edge function test successful:', response.data);
    
  } catch (error) {
    console.error('❌ Edge function test failed:', error.response?.data || error.message);
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Authentication System Tests\n');
  console.log('=' .repeat(50));
  
  testJWTGeneration();
  await testDatabaseConnection();
  await testEdgeFunction();
  await testAuthFlow();
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 Tests completed!');
  console.log('\nNext steps:');
  console.log('1. If database tables are missing, run: database/create_all_tables.sql');
  console.log('2. If JWT tests failed, check your .env file');
  console.log('3. If edge function failed, deploy it: supabase functions deploy send-otp-email');
  console.log('4. Test the complete signup → OTP → login flow manually');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testAuthFlow,
  testJWTGeneration,
  testDatabaseConnection,
  testEdgeFunction,
  runAllTests
};

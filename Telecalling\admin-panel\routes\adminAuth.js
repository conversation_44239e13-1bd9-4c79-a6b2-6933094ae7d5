const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const { v4: uuidv4 } = require('uuid');
const { dbHelpers } = require('../../backend/config/supabase');
const router = express.Router();

// Rate limiting for admin authentication
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many login attempts',
    message: 'Please try again after 15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Admin login
router.post('/login', authLimiter, async (req, res) => {
  try {
    const { adminId, password } = req.body;

    if (!adminId || !password) {
      return res.status(400).json({ 
        error: 'Missing credentials',
        message: 'Admin ID and password are required'
      });
    }

    console.log('🔍 Processing admin login for:', adminId);

    // Find admin by admin_id
    const admin = await dbHelpers.findOne('admins', { admin_id: adminId });

    if (!admin) {
      console.log('❌ Admin not found:', adminId);
      return res.status(401).json({ 
        error: 'Invalid credentials',
        message: 'Admin ID or password is incorrect'
      });
    }

    if (!admin.is_active) {
      console.log('❌ Admin account is inactive:', adminId);
      return res.status(401).json({ 
        error: 'Account inactive',
        message: 'Admin account is deactivated'
      });
    }

    // Simple password verification - direct comparison
    const isPasswordValid = password === admin.password_hash;
    console.log('🔐 Simple password authentication for:', admin.admin_id);

    if (!isPasswordValid) {
      console.log('❌ Invalid password for admin:', adminId);
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Admin ID or password is incorrect'
      });
    }

    // Generate admin JWT tokens
    const adminTokens = generateAdminTokens(admin.id);

    // Store admin session
    await dbHelpers.insert('admin_sessions', {
      id: uuidv4(),
      admin_id: admin.id,
      session_token: adminTokens.accessToken,
      refresh_token: adminTokens.refreshToken,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      ip_address: req.ip,
      user_agent: req.headers['user-agent'],
      created_at: new Date()
    });

    console.log('✅ Admin logged in successfully:', adminId);

    res.json({
      message: 'Admin login successful',
      admin: {
        id: admin.id,
        adminId: admin.admin_id,
        name: admin.name,
        email: admin.email,
        role: admin.role  // ✅ ADDED: Include role in response
      },
      tokens: adminTokens
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({ 
      error: 'Login failed',
      message: 'An error occurred during admin login'
    });
  }
});

// Admin logout
router.post('/logout', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Deactivate session
      await dbHelpers.update('admin_sessions', {
        is_active: false
      }, { session_token: token });
    }

    res.json({
      message: 'Admin logged out successfully'
    });

  } catch (error) {
    console.error('Admin logout error:', error);
    res.status(500).json({ 
      error: 'Logout failed',
      message: 'An error occurred during admin logout'
    });
  }
});

// Refresh admin token
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ 
        error: 'Missing refresh token',
        message: 'Refresh token is required'
      });
    }

    try {
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);
      
      if (decoded.type !== 'admin_refresh') {
        return res.status(401).json({ 
          error: 'Invalid token type',
          message: 'Invalid refresh token type'
        });
      }

      // Check if session exists and is active
      const session = await dbHelpers.findOne('admin_sessions', { 
        admin_id: decoded.adminId,
        refresh_token: refreshToken,
        is_active: true
      });

      if (!session) {
        return res.status(401).json({ 
          error: 'Invalid session',
          message: 'Invalid or expired session'
        });
      }

      // Get admin details
      const admin = await dbHelpers.findOne('admins', { 
        id: decoded.adminId,
        is_active: true
      });

      if (!admin) {
        return res.status(401).json({ 
          error: 'Admin not found',
          message: 'Admin account not found or inactive'
        });
      }

      // Generate new tokens
      const newTokens = generateAdminTokens(admin.id);

      // Update session with new tokens
      await dbHelpers.update('admin_sessions', {
        session_token: newTokens.accessToken,
        refresh_token: newTokens.refreshToken,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      }, { id: session.id });

      res.json({
        message: 'Tokens refreshed successfully',
        tokens: newTokens
      });

    } catch (jwtError) {
      return res.status(401).json({ 
        error: 'Invalid refresh token',
        message: 'Refresh token is invalid or expired'
      });
    }

  } catch (error) {
    console.error('Admin token refresh error:', error);
    res.status(500).json({ 
      error: 'Token refresh failed',
      message: 'An error occurred during token refresh'
    });
  }
});

// Get admin profile
router.get('/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Access denied',
        message: 'No admin token provided'
      });
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      if (decoded.type !== 'admin_access') {
        return res.status(401).json({ 
          error: 'Invalid token type',
          message: 'Invalid admin token type'
        });
      }

      const admin = await dbHelpers.findOne('admins', { 
        id: decoded.adminId,
        is_active: true
      });

      if (!admin) {
        return res.status(401).json({ 
          error: 'Admin not found',
          message: 'Admin account not found or inactive'
        });
      }

      // Get admin statistics
      const clientCount = await dbHelpers.query(
        'SELECT COUNT(*) as count FROM admin_client_assignments WHERE admin_id = $1',
        [admin.id]
      );

      res.json({
        message: 'Admin profile retrieved successfully',
        admin: {
          id: admin.id,
          adminId: admin.admin_id,
          name: admin.name,
          email: admin.email,
          role: admin.role,  // ✅ ADDED: Include role in profile response
          isActive: admin.is_active,
          createdAt: admin.created_at,
          assignedClients: parseInt(clientCount[0].count)
        }
      });

    } catch (jwtError) {
      return res.status(401).json({ 
        error: 'Invalid token',
        message: 'Admin token is invalid or expired'
      });
    }

  } catch (error) {
    console.error('Get admin profile error:', error);
    res.status(500).json({ 
      error: 'Failed to get profile',
      message: 'An error occurred while retrieving admin profile'
    });
  }
});

// Helper function to generate admin JWT tokens
const generateAdminTokens = (adminId) => {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);
  const sessionId = `admin_${timestamp}_${randomId}`;

  const accessToken = jwt.sign(
    { 
      adminId, 
      type: 'admin_access',
      sessionId: sessionId,
      iat: Math.floor(timestamp / 1000)
    },
    process.env.JWT_SECRET,
    { expiresIn: '8h', noTimestamp: false }
  );
  
  const refreshToken = jwt.sign(
    { 
      adminId, 
      type: 'admin_refresh',
      sessionId: sessionId,
      iat: Math.floor(timestamp / 1000)
    },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: '7d', noTimestamp: false }
  );
  
  return { accessToken, refreshToken };
};

module.exports = router;

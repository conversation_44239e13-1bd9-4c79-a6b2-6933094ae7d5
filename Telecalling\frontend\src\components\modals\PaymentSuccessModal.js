import React from 'react';
import { CheckCircle, X, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PaymentSuccessModal = ({ isOpen, onClose, paymentData }) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleGoToDashboard = () => {
    onClose();
    // Stay on current page - the parent components will refresh their data
    // No need to navigate away from the current dashboard
  };

  return (
    <div className="modal-overlay">
      <div className="modal-container max-w-md" style={{background: 'white'}}>
        <div className="modal-header border-b-0">
          <button onClick={onClose} className="modal-close">
            <X size={20} />
          </button>
        </div>

        <div className="modal-body text-center py-8">
          {/* Success Icon */}
          <div className="mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>

          {/* Success Message */}
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Payment Successful!
          </h2>
          <p className="text-gray-600 mb-6">
            Your subscription has been activated successfully. Welcome to VoiceBot!
          </p>

          {/* Payment Details */}
          {paymentData && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold text-gray-900 mb-3">Payment Details</h3>
              <div className="space-y-2 text-sm">
                {paymentData.subscription && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Plan:</span>
                    <span className="font-medium">
                      {paymentData.subscription.subscription_plans?.display_name}
                    </span>
                  </div>
                )}
                {paymentData.transaction && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount:</span>
                      <span className="font-medium">
                        ₹{paymentData.transaction.amount?.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Payment ID:</span>
                      <span className="font-mono text-xs">
                        {paymentData.transaction.razorpay_payment_id}
                      </span>
                    </div>
                  </>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className="text-green-600 font-medium">Active</span>
                </div>
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-semibold text-blue-900 mb-2">What's Next?</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Access your dashboard to manage your account</li>
              <li>• Set up your phone number and business profile</li>
              <li>• Upload your product catalog</li>
              <li>• Start receiving automated calls</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleGoToDashboard}
              className="w-full btn btn-primary flex items-center justify-center"
            >
              Continue
              <ArrowRight className="w-4 h-4 ml-2" />
            </button>
            
            <button
              onClick={onClose}
              className="w-full btn btn-outline"
            >
              Close
            </button>
          </div>

          {/* Support Note */}
          <div className="mt-6 pt-4 border-t">
            <p className="text-xs text-gray-500">
              Need help getting started? Contact our support team at{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-blue-600 hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessModal;

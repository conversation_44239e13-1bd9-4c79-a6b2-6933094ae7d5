const express = require('express');
const router = express.Router();
const { supabaseAdmin } = require('../config/supabase');
const { sendEmail } = require('../services/emailService');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const { auth } = require('../middleware/auth');

// Customer authentication middleware
const customerAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ success: false, error: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // Get customer from database
    const { data: customer, error } = await supabaseAdmin
      .from('customers')
      .select('*')
      .eq('id', decoded.customerId)
      .eq('is_active', true)
      .single();

    if (error || !customer) {
      return res.status(401).json({ success: false, error: 'Invalid token.' });
    }

    req.customer = customer;
    next();
  } catch (error) {
    console.error('Customer auth error:', error);
    res.status(401).json({ success: false, error: 'Invalid token.' });
  }
};

// Middleware to authenticate customer
const authenticateCustomer = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get customer from database
    const { data: customer, error } = await supabaseAdmin
      .from('customers')
      .select('*')
      .eq('id', decoded.customerId)
      .single();

    if (error || !customer) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }

    req.customer = customer;
    next();
  } catch (error) {
    console.error('Customer authentication error:', error);
    return res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
};

// Generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Generate JWT token for customer
const generateCustomerToken = (customerId) => {
  return jwt.sign(
    { customerId, type: 'customer' },
    process.env.JWT_SECRET,
    { expiresIn: '30d' }
  );
};

// ============================================================================
// CUSTOMER AUTHENTICATION ENDPOINTS
// ============================================================================

// Customer Registration
router.post('/register', async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      password,
      address = '',
      city = '',
      state = '',
      pincode = '',
      dateOfBirth = null,
      gender = '',
      registrationToken = null
    } = req.body;

    console.log('📝 Customer registration:', { email, phone, registrationToken });

    // Validate required fields
    if (!name || !email || !phone) {
      return res.status(400).json({
        success: false,
        error: 'Name, email, and phone are required'
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'Please enter a valid email address'
      });
    }

    // Check if customer already exists
    const { data: existingCustomer } = await supabaseAdmin
      .from('customers')
      .select('id, email, phone')
      .or(`email.eq.${email},phone.eq.${phone}`)
      .single();

    if (existingCustomer) {
      return res.status(409).json({
        success: false,
        error: existingCustomer.email === email ? 'Email already registered' : 'Phone number already registered'
      });
    }

    // Hash password if provided
    let passwordHash = null;
    if (password) {
      passwordHash = await bcrypt.hash(password, 12);
    }

    // Get registration token data if provided
    let tokenData = null;
    let clientId = null;

    if (registrationToken) {
      const { data: token, error: tokenError } = await supabaseAdmin
        .from('customer_registration_tokens')
        .select('*')
        .eq('token', registrationToken)
        .eq('is_used', false)
        .gte('expires_at', new Date().toISOString())
        .single();

      if (tokenError || !token) {
        return res.status(400).json({
          success: false,
          error: 'Invalid or expired registration token'
        });
      }

      tokenData = token;
      clientId = token.client_id;

      // Validate phone number matches token
      if (token.phone_number && token.phone_number !== phone) {
        return res.status(400).json({
          success: false,
          error: 'Phone number does not match registration token'
        });
      }
    }

    // Create customer
    const { data: newCustomer, error: customerError } = await supabaseAdmin
      .from('customers')
      .insert({
        name,
        email,
        phone,
        password_hash: passwordHash,
        address,
        city,
        state,
        pincode,
        date_of_birth: dateOfBirth,
        gender,
        is_email_verified: false,
        preferences: tokenData?.conversation_data || {}
      })
      .select()
      .single();

    if (customerError) {
      console.error('❌ Customer creation error:', customerError);
      return res.status(500).json({
        success: false,
        error: 'Failed to create customer account'
      });
    }

    console.log('✅ Customer created:', newCustomer.id);

    // Create customer-client interaction if token provided
    if (tokenData && clientId) {
      await supabaseAdmin
        .from('customer_interactions')
        .insert({
          customer_id: newCustomer.id,
          client_id: clientId,
          call_log_id: tokenData.call_log_id,
          interaction_type: 'registration',
          interaction_source: 'bot_registration',
          conversation_summary: tokenData.conversation_summary,
          product_interests: tokenData.product_interests || [],
          estimated_value: tokenData.estimated_amount || 0,
          metadata: {
            registration_token: registrationToken,
            ...tokenData.conversation_data
          }
        });

      // Mark token as used
      await supabaseAdmin
        .from('customer_registration_tokens')
        .update({
          is_used: true,
          used_at: new Date().toISOString(),
          customer_id: newCustomer.id
        })
        .eq('token', registrationToken);

      // Update call log
      if (tokenData.call_log_id) {
        await supabaseAdmin
          .from('call_logs')
          .update({ customer_id: newCustomer.id })
          .eq('id', tokenData.call_log_id);
      }
    }

    // Generate email verification OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await supabaseAdmin
      .from('customer_email_tokens')
      .insert({
        customer_id: newCustomer.id,
        email: email,
        token: otp,
        token_type: 'verification',
        expires_at: otpExpiry.toISOString()
      });

    // Send verification email
    try {
      await sendEmail({
        to: email,
        subject: 'Verify Your Email - Customer Portal',
        html: `
          <h2>Welcome ${name}!</h2>
          <p>Your verification code is: <strong>${otp}</strong></p>
          <p>This code will expire in 10 minutes.</p>
          <p>If you didn't create this account, please ignore this email.</p>
        `
      });
    } catch (emailError) {
      console.error('❌ Email sending error:', emailError);
      // Don't fail registration if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Registration successful! Please check your email for verification code.',
      customerId: newCustomer.id,
      requiresVerification: true
    });

  } catch (error) {
    console.error('❌ Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Registration failed'
    });
  }
});

// Validate registration token
router.get('/validate-token/:token', async (req, res) => {
  try {
    const { token } = req.params;

    const { data: tokenData, error } = await supabaseAdmin
      .from('customer_registration_tokens')
      .select(`
        *,
        clients:client_id (
          id,
          business_name,
          email
        )
      `)
      .eq('token', token)
      .eq('is_used', false)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !tokenData) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or expired registration token'
      });
    }

    res.json({
      success: true,
      tokenData: {
        clientId: tokenData.client_id,
        businessName: tokenData.clients?.business_name,
        phoneNumber: tokenData.phone_number,
        callLogId: tokenData.call_log_id
      }
    });
  } catch (error) {
    console.error('Token validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate token'
    });
  }
});

// Customer registration
router.post('/register', async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      address,
      city,
      state,
      pincode,
      dateOfBirth,
      gender,
      registrationToken
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone) {
      return res.status(400).json({
        success: false,
        error: 'Name, email, and phone are required'
      });
    }

    // Check if customer already exists
    const { data: existingCustomer } = await supabaseAdmin
      .from('customers')
      .select('id, email, is_email_verified')
      .eq('email', email)
      .single();

    let customerId;
    let tokenData = null;

    if (registrationToken) {
      // Validate registration token
      const { data: tokenInfo, error: tokenError } = await supabaseAdmin
        .from('customer_registration_tokens')
        .select('*')
        .eq('token', registrationToken)
        .eq('is_used', false)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (tokenError || !tokenInfo) {
        return res.status(400).json({
          success: false,
          error: 'Invalid or expired registration token'
        });
      }

      tokenData = tokenInfo;
    }

    if (existingCustomer) {
      customerId = existingCustomer.id;
      
      // Update customer info if needed
      const { error: updateError } = await supabaseAdmin
        .from('customers')
        .update({
          name,
          phone,
          address,
          city,
          state,
          pincode,
          date_of_birth: dateOfBirth || null,
          gender: gender || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', customerId);

      if (updateError) {
        console.error('Customer update error:', updateError);
        return res.status(500).json({
          success: false,
          error: 'Failed to update customer information'
        });
      }
    } else {
      // Create new customer
      const { data: newCustomer, error: createError } = await supabaseAdmin
        .from('customers')
        .insert({
          name,
          email,
          phone,
          address,
          city,
          state,
          pincode,
          date_of_birth: dateOfBirth || null,
          gender: gender || null,
          is_email_verified: false
        })
        .select()
        .single();

      if (createError) {
        console.error('Customer creation error:', createError);
        return res.status(500).json({
          success: false,
          error: 'Failed to create customer account'
        });
      }

      customerId = newCustomer.id;
    }

    // Create customer interaction if registration token exists
    if (tokenData) {
      const { error: interactionError } = await supabaseAdmin
        .from('customer_interactions')
        .upsert({
          customer_id: customerId,
          client_id: tokenData.client_id,
          call_log_id: tokenData.call_log_id,
          interaction_type: 'registration',
          interaction_source: tokenData.phone_number,
          first_interaction_at: new Date().toISOString(),
          last_interaction_at: new Date().toISOString()
        }, {
          onConflict: 'customer_id,client_id'
        });

      if (interactionError) {
        console.error('Interaction creation error:', interactionError);
      }

      // Mark token as used
      await supabaseAdmin
        .from('customer_registration_tokens')
        .update({
          is_used: true,
          used_at: new Date().toISOString(),
          customer_id: customerId
        })
        .eq('id', tokenData.id);
    }

    // Generate and send email verification OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    const { error: otpError } = await supabaseAdmin
      .from('customer_email_tokens')
      .insert({
        customer_id: customerId,
        email,
        token: otp,
        token_type: 'email_verification',
        expires_at: expiresAt.toISOString()
      });

    if (otpError) {
      console.error('OTP creation error:', otpError);
      return res.status(500).json({
        success: false,
        error: 'Failed to generate verification code'
      });
    }

    // Send verification email
    try {
      await sendEmail({
        to: email,
        subject: 'Verify Your Email - Customer Portal',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Welcome to Customer Portal!</h2>
            <p>Hi ${name},</p>
            <p>Thank you for registering with us. Please use the following verification code to complete your registration:</p>
            <div style="background: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0;">
              <h1 style="color: #667eea; font-size: 32px; margin: 0;">${otp}</h1>
            </div>
            <p>This code will expire in 10 minutes.</p>
            <p>If you didn't request this verification, please ignore this email.</p>
            <p>Best regards,<br>Customer Portal Team</p>
          </div>
        `
      });
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail registration if email fails
    }

    res.json({
      success: true,
      message: 'Registration successful! Please check your email for verification code.',
      customerId
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Registration failed'
    });
  }
});

// Verify email
router.post('/verify-email', async (req, res) => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(400).json({
        success: false,
        error: 'Email and OTP are required'
      });
    }

    // Find valid OTP
    const { data: tokenData, error: tokenError } = await supabaseAdmin
      .from('customer_email_tokens')
      .select('*')
      .eq('email', email)
      .eq('token', otp)
      .eq('token_type', 'email_verification')
      .eq('is_used', false)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (tokenError || !tokenData) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or expired verification code'
      });
    }

    // Mark email as verified
    const { data: customer, error: updateError } = await supabaseAdmin
      .from('customers')
      .update({
        is_email_verified: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', tokenData.customer_id)
      .select()
      .single();

    if (updateError) {
      console.error('Customer verification error:', updateError);
      return res.status(500).json({
        success: false,
        error: 'Failed to verify email'
      });
    }

    // Mark OTP as used
    await supabaseAdmin
      .from('customer_email_tokens')
      .update({
        is_used: true,
        used_at: new Date().toISOString()
      })
      .eq('id', tokenData.id);

    // Generate JWT token
    const token = generateCustomerToken(customer.id);

    res.json({
      success: true,
      message: 'Email verified successfully!',
      token,
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        isEmailVerified: customer.is_email_verified
      }
    });

  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Email verification failed'
    });
  }
});

// Generate registration link for customers (for clients to use)
router.post('/generate-registration-link', auth, async (req, res) => {
  try {
    const { phoneNumber, callLogId, expiryHours = 24 } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({
        success: false,
        error: 'Phone number is required'
      });
    }

    // Generate secure token
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + expiryHours * 60 * 60 * 1000);

    // Store token in database
    const { data: tokenData, error } = await supabaseAdmin
      .from('customer_registration_tokens')
      .insert({
        token,
        client_id: req.user.id,
        call_log_id: callLogId || null,
        phone_number: phoneNumber,
        expires_at: expiresAt.toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Token creation error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to generate registration link'
      });
    }

    const registrationLink = `${process.env.CUSTOMER_PORTAL_URL || 'http://localhost:4000'}/register/${token}`;

    res.json({
      success: true,
      token,
      registrationLink,
      expiresAt: expiresAt.toISOString(),
      message: 'Registration link generated successfully'
    });

  } catch (error) {
    console.error('Generate registration link error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate registration link'
    });
  }
});

// ============================================================================
// BOT INTEGRATION ENDPOINTS
// ============================================================================

// Bot endpoint: Handle customer interest during call
router.post('/bot/customer-interest', async (req, res) => {
  try {
    const {
      phoneNumber,
      customerName,
      clientPlivoNumber,
      callLogId,
      productInterests = [],
      estimatedAmount = 0,
      conversationSummary = '',
      conversationData = {},
      interestLevel = 5 // 1-10 scale
    } = req.body;

    console.log('🤖 Bot customer interest:', { phoneNumber, clientPlivoNumber, customerName });

    if (!phoneNumber || !clientPlivoNumber) {
      return res.status(400).json({
        success: false,
        error: 'Phone number and client Plivo number are required'
      });
    }

    // Find client by Plivo number
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('id, business_name, email, assigned_plivo_number')
      .eq('assigned_plivo_number', clientPlivoNumber)
      .single();

    if (clientError || !client) {
      console.error('❌ Client not found for Plivo number:', clientPlivoNumber);
      return res.status(404).json({
        success: false,
        error: 'Client not found for this number'
      });
    }

    // Check if customer already exists
    const { data: existingCustomer, error: customerError } = await supabaseAdmin
      .from('customers')
      .select('id, name, email, is_email_verified')
      .eq('phone', phoneNumber)
      .single();

    if (existingCustomer && !customerError) {
      console.log('✅ Existing customer found:', existingCustomer.id);

      // Create or update customer-client interaction
      const { data: interaction, error: interactionError } = await supabaseAdmin
        .from('customer_interactions')
        .upsert({
          customer_id: existingCustomer.id,
          client_id: client.id,
          call_log_id: callLogId,
          interaction_type: 'call',
          interaction_source: clientPlivoNumber,
          last_interaction_at: new Date().toISOString(),
          interaction_count: 1, // Will be incremented by trigger
          conversation_summary: conversationSummary,
          product_interests: productInterests,
          estimated_value: estimatedAmount,
          metadata: {
            ...conversationData,
            bot_interaction: true,
            interest_level: interestLevel
          }
        }, {
          onConflict: 'customer_id,client_id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      // Update call log with customer association
      if (callLogId) {
        await supabaseAdmin
          .from('call_logs')
          .update({
            customer_id: existingCustomer.id,
            bot_conversation_data: conversationData,
            customer_interest_level: interestLevel,
            follow_up_required: estimatedAmount > 0
          })
          .eq('id', callLogId);
      }

      return res.json({
        success: true,
        isExistingCustomer: true,
        customerId: existingCustomer.id,
        message: "Customer connected to client",
        loginLink: `${process.env.CUSTOMER_PORTAL_URL || 'http://localhost:4000'}/login`,
        smsText: `Hi ${existingCustomer.name}! Continue with ${client.business_name}: ${process.env.CUSTOMER_PORTAL_URL || 'http://localhost:4000'}/login`
      });
    } else {
      console.log('🆕 New customer - generating registration link');

      // Generate registration token for new customer
      const token = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      const { data: tokenData, error: tokenError } = await supabaseAdmin
        .from('customer_registration_tokens')
        .insert({
          token,
          client_id: client.id,
          call_log_id: callLogId,
          phone_number: phoneNumber,
          expires_at: expiresAt.toISOString(),
          customer_name: customerName,
          product_interests: productInterests,
          estimated_amount: estimatedAmount,
          conversation_summary: conversationSummary,
          conversation_data: conversationData,
          metadata: {
            bot_interaction: true,
            interest_level: interestLevel
          }
        })
        .select()
        .single();

      if (tokenError) {
        console.error('❌ Token creation error:', tokenError);
        return res.status(500).json({
          success: false,
          error: 'Failed to generate registration link'
        });
      }

      // Update call log
      if (callLogId) {
        await supabaseAdmin
          .from('call_logs')
          .update({
            bot_conversation_data: conversationData,
            customer_interest_level: interestLevel,
            follow_up_required: true
          })
          .eq('id', callLogId);
      }

      const registrationLink = `${process.env.CUSTOMER_PORTAL_URL || 'http://localhost:4000'}/register/${token}`;

      return res.json({
        success: true,
        isExistingCustomer: false,
        token,
        registrationLink,
        expiresAt: expiresAt.toISOString(),
        message: 'Registration link generated',
        smsText: `Hi${customerName ? ' ' + customerName : ''}! Complete your order with ${client.business_name}: ${registrationLink} - Valid for 24 hours`
      });
    }

  } catch (error) {
    console.error('❌ Bot customer interest error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process customer interest'
    });
  }
});

// Bot endpoint: Quick customer lookup
router.post('/bot/customer-lookup', async (req, res) => {
  try {
    const { phoneNumber, clientPlivoNumber } = req.body;

    if (!phoneNumber || !clientPlivoNumber) {
      return res.status(400).json({
        success: false,
        error: 'Phone number and client Plivo number are required'
      });
    }

    // Find client
    const { data: client } = await supabaseAdmin
      .from('clients')
      .select('id, business_name')
      .eq('assigned_plivo_number', clientPlivoNumber)
      .single();

    if (!client) {
      return res.status(404).json({
        success: false,
        error: 'Client not found'
      });
    }

    // Check customer
    const { data: customer } = await supabaseAdmin
      .from('customers')
      .select(`
        id, name, email, phone,
        customer_interactions!inner(
          client_id, interaction_count, last_interaction_at
        )
      `)
      .eq('phone', phoneNumber)
      .eq('customer_interactions.client_id', client.id)
      .single();

    res.json({
      success: true,
      customerExists: !!customer,
      customer: customer ? {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        interactionCount: customer.customer_interactions[0]?.interaction_count || 0,
        lastInteraction: customer.customer_interactions[0]?.last_interaction_at
      } : null
    });

  } catch (error) {
    console.error('❌ Bot customer lookup error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to lookup customer'
    });
  }
});

// ============================================================================
// CUSTOMER PROFILE & DATA ENDPOINTS
// ============================================================================

// Get customer profile
router.get('/profile', customerAuth, async (req, res) => {
  try {
    const customerId = req.customer.id;

    // Get customer with client interactions
    const { data: customer, error: customerError } = await supabaseAdmin
      .from('customers')
      .select(`
        *,
        customer_interactions (
          id,
          client_id,
          interaction_type,
          first_interaction_at,
          last_interaction_at,
          interaction_count,
          status,
          conversation_summary,
          product_interests,
          estimated_value,
          clients (
            id,
            business_name,
            business_email,
            assigned_plivo_number
          )
        )
      `)
      .eq('id', customerId)
      .single();

    if (customerError || !customer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found'
      });
    }

    res.json({
      success: true,
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        city: customer.city,
        state: customer.state,
        pincode: customer.pincode,
        dateOfBirth: customer.date_of_birth,
        gender: customer.gender,
        isEmailVerified: customer.is_email_verified,
        isPhoneVerified: customer.is_phone_verified,
        profilePictureUrl: customer.profile_picture_url,
        preferences: customer.preferences,
        communicationPreferences: customer.communication_preferences,
        createdAt: customer.created_at,
        lastLoginAt: customer.last_login_at,
        clientInteractions: customer.customer_interactions.map(interaction => ({
          id: interaction.id,
          clientId: interaction.client_id,
          businessName: interaction.clients.business_name,
          businessEmail: interaction.clients.business_email,
          plivoNumber: interaction.clients.assigned_plivo_number,
          interactionType: interaction.interaction_type,
          firstInteractionAt: interaction.first_interaction_at,
          lastInteractionAt: interaction.last_interaction_at,
          interactionCount: interaction.interaction_count,
          status: interaction.status,
          conversationSummary: interaction.conversation_summary,
          productInterests: interaction.product_interests,
          estimatedValue: interaction.estimated_value
        }))
      }
    });

  } catch (error) {
    console.error('❌ Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get profile'
    });
  }
});

// Update customer profile
router.put('/profile', customerAuth, async (req, res) => {
  try {
    const customerId = req.customer.id;
    const {
      name,
      phone,
      address,
      city,
      state,
      pincode,
      dateOfBirth,
      gender,
      preferences,
      communicationPreferences
    } = req.body;

    const updateData = {};
    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (address !== undefined) updateData.address = address;
    if (city) updateData.city = city;
    if (state) updateData.state = state;
    if (pincode) updateData.pincode = pincode;
    if (dateOfBirth) updateData.date_of_birth = dateOfBirth;
    if (gender) updateData.gender = gender;
    if (preferences) updateData.preferences = preferences;
    if (communicationPreferences) updateData.communication_preferences = communicationPreferences;

    updateData.updated_at = new Date().toISOString();

    const { data: updatedCustomer, error } = await supabaseAdmin
      .from('customers')
      .update(updateData)
      .eq('id', customerId)
      .select()
      .single();

    if (error) {
      console.error('❌ Profile update error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to update profile'
      });
    }

    res.json({
      success: true,
      message: 'Profile updated successfully',
      customer: {
        id: updatedCustomer.id,
        name: updatedCustomer.name,
        email: updatedCustomer.email,
        phone: updatedCustomer.phone,
        address: updatedCustomer.address,
        city: updatedCustomer.city,
        state: updatedCustomer.state,
        pincode: updatedCustomer.pincode,
        dateOfBirth: updatedCustomer.date_of_birth,
        gender: updatedCustomer.gender,
        isEmailVerified: updatedCustomer.is_email_verified,
        preferences: updatedCustomer.preferences,
        communicationPreferences: updatedCustomer.communication_preferences
      }
    });

  } catch (error) {
    console.error('❌ Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update profile'
    });
  }
});

// Get customer orders
router.get('/orders', customerAuth, async (req, res) => {
  try {
    const customerId = req.customer.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { data: orders, error, count } = await supabaseAdmin
      .from('customer_orders')
      .select(`
        *,
        clients (
          id,
          business_name,
          business_email
        )
      `, { count: 'exact' })
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('❌ Get orders error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to get orders'
      });
    }

    res.json({
      success: true,
      orders: orders.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        orderType: order.order_type,
        title: order.title,
        description: order.description,
        products: order.products,
        totalAmount: order.total_amount,
        currency: order.currency,
        status: order.status,
        paymentStatus: order.payment_status,
        scheduledAt: order.scheduled_at,
        completedAt: order.completed_at,
        timeline: order.timeline,
        createdAt: order.created_at,
        client: {
          id: order.clients.id,
          businessName: order.clients.business_name,
          businessEmail: order.clients.business_email
        }
      })),
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit)
      }
    });

  } catch (error) {
    console.error('❌ Get orders error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get orders'
    });
  }
});

// ============================================================================
// CLIENT-SIDE CUSTOMER MANAGEMENT ENDPOINTS
// ============================================================================

// Get customers for client dashboard
router.get('/', auth, async (req, res) => {
  try {
    const clientId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Get customers associated with this client
    const { data: customers, error, count } = await supabaseAdmin
      .from('customer_interactions')
      .select(`
        customers (
          id,
          name,
          email,
          phone,
          is_active,
          created_at,
          last_login_at
        ),
        interaction_count,
        last_interaction_at,
        status,
        conversation_summary,
        product_interests,
        estimated_value
      `, { count: 'exact' })
      .eq('client_id', clientId)
      .order('last_interaction_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('❌ Get customers error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to get customers'
      });
    }

    const formattedCustomers = customers.map(interaction => ({
      id: interaction.customers.id,
      name: interaction.customers.name,
      email: interaction.customers.email,
      phone: interaction.customers.phone,
      isActive: interaction.customers.is_active,
      createdAt: interaction.customers.created_at,
      lastLoginAt: interaction.customers.last_login_at,
      interactionCount: interaction.interaction_count,
      lastInteractionAt: interaction.last_interaction_at,
      status: interaction.status,
      conversationSummary: interaction.conversation_summary,
      productInterests: interaction.product_interests || [],
      estimatedValue: interaction.estimated_value || 0
    }));

    res.json({
      success: true,
      customers: formattedCustomers,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit)
      }
    });

  } catch (error) {
    console.error('❌ Get customers error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get customers'
    });
  }
});

// Get customer stats for client dashboard
router.get('/stats', auth, async (req, res) => {
  try {
    const clientId = req.user.id;

    // Get total customers
    const { count: totalCustomers } = await supabaseAdmin
      .from('customer_interactions')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', clientId);

    // Get active chats
    const { count: activeChats } = await supabaseAdmin
      .from('customer_chats')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', clientId)
      .eq('status', 'active');

    // Get total orders
    const { count: totalOrders } = await supabaseAdmin
      .from('customer_orders')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', clientId);

    // Get this month's registrations
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const { count: thisMonthRegistrations } = await supabaseAdmin
      .from('customer_interactions')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', clientId)
      .gte('first_interaction_at', startOfMonth.toISOString());

    res.json({
      success: true,
      stats: {
        totalCustomers: totalCustomers || 0,
        activeChats: activeChats || 0,
        totalOrders: totalOrders || 0,
        thisMonthRegistrations: thisMonthRegistrations || 0
      }
    });

  } catch (error) {
    console.error('❌ Get customer stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get customer stats'
    });
  }
});

module.exports = router;

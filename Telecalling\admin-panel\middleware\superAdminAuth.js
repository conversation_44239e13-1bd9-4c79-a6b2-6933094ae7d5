const jwt = require('jsonwebtoken');
const { dbHel<PERSON> } = require('../../backend/config/supabase');

// Super Admin authentication middleware
const superAdminAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'No super admin token provided' 
      });
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Ensure it's an admin access token
      if (decoded.type !== 'admin_access') {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Invalid token type' 
        });
      }
      
      // Get admin from database
      const admin = await dbHelpers.findOne('admins', { id: decoded.adminId });
      
      if (!admin || !admin.is_active) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Admin account not found or inactive' 
        });
      }

      // Check if admin has super admin role
      if (admin.role !== 'super_admin') {
        return res.status(403).json({ 
          error: 'Access denied', 
          message: 'Super admin privileges required' 
        });
      }
      
      // Verify session exists and is active
      const session = await dbHelpers.findOne('admin_sessions', { 
        admin_id: decoded.adminId,
        session_token: token,
        is_active: true
      });
      
      if (!session) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Invalid super admin session' 
        });
      }
      
      // Check if session has expired
      if (new Date() > new Date(session.expires_at)) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Super admin session expired' 
        });
      }
      
      // Add super admin info to request
      req.superAdmin = {
        id: admin.id,
        adminId: admin.admin_id,
        name: admin.name,
        email: admin.email,
        role: admin.role,
        isActive: admin.is_active
      };
      
      next();
    } catch (jwtError) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Invalid super admin token' 
      });
    }
  } catch (error) {
    console.error('Super admin auth middleware error:', error);
    return res.status(500).json({ 
      error: 'Authentication error', 
      message: 'Internal server error' 
    });
  }
};

// Middleware to check if user is either admin or super admin
const adminOrSuperAdminAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'No admin token provided' 
      });
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      if (decoded.type !== 'admin_access') {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Invalid token type' 
        });
      }
      
      const admin = await dbHelpers.findOne('admins', { id: decoded.adminId });
      
      if (!admin || !admin.is_active) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Admin account not found or inactive' 
        });
      }

      // Check if admin has admin or super_admin role
      if (!['admin', 'super_admin'].includes(admin.role)) {
        return res.status(403).json({ 
          error: 'Access denied', 
          message: 'Admin privileges required' 
        });
      }
      
      const session = await dbHelpers.findOne('admin_sessions', { 
        admin_id: decoded.adminId,
        session_token: token,
        is_active: true
      });
      
      if (!session) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Invalid admin session' 
        });
      }
      
      if (new Date() > new Date(session.expires_at)) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Admin session expired' 
        });
      }
      
      // Add admin info to request
      req.admin = {
        id: admin.id,
        adminId: admin.admin_id,
        name: admin.name,
        email: admin.email,
        role: admin.role,
        isActive: admin.is_active,
        isSuperAdmin: admin.role === 'super_admin'
      };
      
      next();
    } catch (jwtError) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Invalid admin token' 
      });
    }
  } catch (error) {
    console.error('Admin auth middleware error:', error);
    return res.status(500).json({ 
      error: 'Authentication error', 
      message: 'Internal server error' 
    });
  }
};

// Helper function to log super admin actions
const logSuperAdminAction = async (superAdminId, actionType, targetAdminId = null, targetClientIds = null, actionDetails = null, req = null) => {
  try {
    await dbHelpers.insert('super_admin_audit_log', {
      id: require('uuid').v4(),
      super_admin_id: superAdminId,
      action_type: actionType,
      target_admin_id: targetAdminId,
      target_client_ids: targetClientIds,
      action_details: actionDetails,
      ip_address: req ? req.ip : null,
      user_agent: req ? req.headers['user-agent'] : null,
      created_at: new Date()
    });
  } catch (error) {
    console.error('Error logging super admin action:', error);
  }
};

module.exports = {
  superAdminAuth,
  adminOrSuperAdminAuth,
  logSuperAdminAction
};

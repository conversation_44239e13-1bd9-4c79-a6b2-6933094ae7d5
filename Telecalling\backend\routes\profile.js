const express = require('express');
const bcrypt = require('bcryptjs');
const { dbHel<PERSON>, supabaseAdmin } = require('../config/supabase');
const { auth } = require('../middleware/auth');
const router = express.Router();

// Get client profile
router.get('/', auth, async (req, res) => {
  try {
    const client = await dbHelpers.findOne('clients', { id: req.user.id });

    if (!client) {
      return res.status(404).json({
        error: 'Profile not found',
        message: 'Client profile not found'
      });
    }

    // Get current subscription with plan details
    const { data: subscription, error: subError } = await supabaseAdmin
      .from('client_subscriptions')
      .select(`
        *,
        subscription_plans (
          id,
          name,
          display_name,
          price,
          features,
          max_calls,
          max_products
        )
      `)
      .eq('client_id', req.user.id)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    // Check if subscription has expired
    let currentSubscription = null;
    if (subscription && !subError) {
      const now = new Date();
      const endsAt = new Date(subscription.ends_at);

      if (endsAt > now) {
        currentSubscription = subscription;
      } else {
        // Mark subscription as expired
        await supabaseAdmin
          .from('client_subscriptions')
          .update({ status: 'expired' })
          .eq('id', subscription.id);
      }
    }

    res.json({
      message: 'Profile retrieved successfully',
      user: {
        id: client.id,
        email: client.email,
        username: client.username,
        businessName: client.business_name,
        businessSummary: client.business_summary,
        businessAddress: client.business_address,
        ownerName: client.owner_name,
        mobileNumber: client.mobile_number,
        businessEmail: client.business_email,
        whatsappNumber: client.whatsapp_number,
        assignedPlivoNumber: client.assigned_plivo_number,
        isEmailVerified: client.is_email_verified,
        isActive: client.is_active,
        createdAt: client.created_at,
        lastLoginAt: client.last_login_at
      },
      planSubscription: currentSubscription ? {
        planName: currentSubscription.subscription_plans.display_name,
        planPrice: currentSubscription.subscription_plans.price,
        startDate: currentSubscription.starts_at,
        endDate: currentSubscription.ends_at,
        status: currentSubscription.status,
        planId: currentSubscription.subscription_plans.id,
        planFeatures: currentSubscription.subscription_plans.features,
        maxCalls: currentSubscription.subscription_plans.max_calls,
        maxProducts: currentSubscription.subscription_plans.max_products
      } : null
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to retrieve profile',
      message: 'An error occurred while retrieving profile'
    });
  }
});

// Update client profile
router.put('/', auth, async (req, res) => {
  try {
    const {
      username,
      businessName,
      businessSummary,
      businessAddress,
      ownerName,
      mobileNumber,
      businessEmail,
      whatsappNumber
    } = req.body;

    // Validate required fields
    if (!username) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Username is required'
      });
    }

    // Validate email format if provided
    if (businessEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(businessEmail)) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Invalid business email format'
      });
    }

    // Validate phone numbers format if provided
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (mobileNumber && !phoneRegex.test(mobileNumber.replace(/[\s\-\(\)]/g, ''))) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Invalid mobile number format'
      });
    }

    if (whatsappNumber && !phoneRegex.test(whatsappNumber.replace(/[\s\-\(\)]/g, ''))) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Invalid WhatsApp number format'
      });
    }

    // Update profile
    const updatedClient = await dbHelpers.update('clients', {
      username,
      business_name: businessName,
      business_summary: businessSummary,
      business_address: businessAddress,
      owner_name: ownerName,
      mobile_number: mobileNumber,
      business_email: businessEmail,
      whatsapp_number: whatsappNumber,
      updated_at: new Date()
    }, { id: req.user.id });

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: updatedClient.id,
        email: updatedClient.email,
        username: updatedClient.username,
        businessName: updatedClient.business_name,
        businessSummary: updatedClient.business_summary,
        businessAddress: updatedClient.business_address,
        ownerName: updatedClient.owner_name,
        mobileNumber: updatedClient.mobile_number,
        businessEmail: updatedClient.business_email,
        whatsappNumber: updatedClient.whatsapp_number,
        assignedPlivoNumber: updatedClient.assigned_plivo_number,
        isEmailVerified: updatedClient.is_email_verified,
        isActive: updatedClient.is_active,
        updatedAt: updatedClient.updated_at
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ 
      error: 'Failed to update profile',
      message: 'An error occurred while updating profile'
    });
  }
});

// Reset password
router.post('/reset-password', auth, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Current password and new password are required'
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'New password must be at least 8 characters long'
      });
    }

    // Get current client
    const client = await dbHelpers.findOne('clients', { id: req.user.id });
    
    if (!client || !client.password_hash) {
      return res.status(400).json({ 
        error: 'Password reset not available',
        message: 'Password reset is not available for Google authenticated users'
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, client.password_hash);
    
    if (!isCurrentPasswordValid) {
      return res.status(400).json({ 
        error: 'Invalid password',
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, 12);

    // Update password
    await dbHelpers.update('clients', {
      password_hash: newPasswordHash,
      updated_at: new Date()
    }, { id: req.user.id });

    res.json({
      message: 'Password reset successfully'
    });

  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ 
      error: 'Failed to reset password',
      message: 'An error occurred while resetting password'
    });
  }
});

module.exports = router;

import React from 'react';
import { Link } from 'react-router-dom';
import { Home, ArrowLeft, Search } from 'lucide-react';

const NotFoundPage = () => {
  return (
    <div className="not-found-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '1rem' }}>
      <div style={{ textAlign: 'center', maxWidth: '500px' }}>
        {/* 404 Illustration */}
        <div style={{ marginBottom: '2rem' }}>
          <div style={{ 
            fontSize: '8rem', 
            fontWeight: 'bold', 
            color: '#e5e7eb', 
            lineHeight: '1',
            marginBottom: '1rem'
          }}>
            404
          </div>
          <div style={{ 
            width: '200px', 
            height: '4px', 
            backgroundColor: '#3b82f6', 
            borderRadius: '2px',
            margin: '0 auto'
          }}></div>
        </div>

        {/* Content */}
        <h1 style={{ 
          fontSize: '2rem', 
          fontWeight: 'bold', 
          color: '#1f2937', 
          marginBottom: '1rem' 
        }}>
          Page Not Found
        </h1>
        
        <p style={{ 
          color: '#6b7280', 
          fontSize: '1.125rem', 
          marginBottom: '2rem',
          lineHeight: '1.6'
        }}>
          Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you might have entered the wrong URL.
        </p>

        {/* Action Buttons */}
        <div style={{ 
          display: 'flex', 
          gap: '1rem', 
          justifyContent: 'center', 
          flexWrap: 'wrap',
          marginBottom: '2rem'
        }}>
          <Link 
            to="/" 
            style={{ 
              padding: '0.75rem 1.5rem', 
              backgroundColor: '#3b82f6', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#2563eb'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#3b82f6'}
          >
            <Home size={20} />
            Go Home
          </Link>
          
          <button 
            onClick={() => window.history.back()}
            style={{ 
              padding: '0.75rem 1.5rem', 
              backgroundColor: 'white', 
              color: '#3b82f6', 
              border: '2px solid #3b82f6',
              borderRadius: '8px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'all 0.2s'
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = '#3b82f6';
              e.target.style.color = 'white';
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = 'white';
              e.target.style.color = '#3b82f6';
            }}
          >
            <ArrowLeft size={20} />
            Go Back
          </button>
        </div>

        {/* Helpful Links */}
        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          padding: '2rem',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ 
            fontSize: '1.25rem', 
            fontWeight: '600', 
            color: '#1f2937', 
            marginBottom: '1rem' 
          }}>
            Looking for something specific?
          </h3>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem',
            textAlign: 'left'
          }}>
            <Link 
              to="/dashboard" 
              style={{ 
                padding: '1rem', 
                backgroundColor: '#f8fafc', 
                borderRadius: '6px',
                textDecoration: 'none',
                color: '#374151',
                border: '1px solid #e5e7eb',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = '#f1f5f9';
                e.target.style.borderColor = '#3b82f6';
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = '#f8fafc';
                e.target.style.borderColor = '#e5e7eb';
              }}
            >
              <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: '0 0 0.5rem 0' }}>
                Dashboard
              </h4>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                View your account overview
              </p>
            </Link>

            <Link 
              to="/orders" 
              style={{ 
                padding: '1rem', 
                backgroundColor: '#f8fafc', 
                borderRadius: '6px',
                textDecoration: 'none',
                color: '#374151',
                border: '1px solid #e5e7eb',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = '#f1f5f9';
                e.target.style.borderColor = '#3b82f6';
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = '#f8fafc';
                e.target.style.borderColor = '#e5e7eb';
              }}
            >
              <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: '0 0 0.5rem 0' }}>
                My Orders
              </h4>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                Track your orders and purchases
              </p>
            </Link>

            <Link 
              to="/chats" 
              style={{ 
                padding: '1rem', 
                backgroundColor: '#f8fafc', 
                borderRadius: '6px',
                textDecoration: 'none',
                color: '#374151',
                border: '1px solid #e5e7eb',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = '#f1f5f9';
                e.target.style.borderColor = '#3b82f6';
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = '#f8fafc';
                e.target.style.borderColor = '#e5e7eb';
              }}
            >
              <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: '0 0 0.5rem 0' }}>
                Support Chat
              </h4>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                Get help from our team
              </p>
            </Link>

            <Link 
              to="/profile" 
              style={{ 
                padding: '1rem', 
                backgroundColor: '#f8fafc', 
                borderRadius: '6px',
                textDecoration: 'none',
                color: '#374151',
                border: '1px solid #e5e7eb',
                transition: 'all 0.2s'
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = '#f1f5f9';
                e.target.style.borderColor = '#3b82f6';
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = '#f8fafc';
                e.target.style.borderColor = '#e5e7eb';
              }}
            >
              <h4 style={{ fontSize: '1rem', fontWeight: '500', color: '#1f2937', margin: '0 0 0.5rem 0' }}>
                My Profile
              </h4>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                Manage your account settings
              </p>
            </Link>
          </div>
        </div>

        {/* Contact Info */}
        <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#fef3c7', borderRadius: '6px' }}>
          <p style={{ color: '#92400e', fontSize: '0.875rem', margin: 0 }}>
            <strong>Still need help?</strong> Contact our support team and we'll be happy to assist you.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;

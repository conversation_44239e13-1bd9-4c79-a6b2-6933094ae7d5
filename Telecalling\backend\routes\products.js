const express = require('express');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const { dbHelpers } = require('../config/supabase');
const { auth } = require('../middleware/auth');
const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');
const fileProcessorService = require('../services/fileProcessorService');
const openaiService = require('../services/openaiService');

const router = express.Router();

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key-here'
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/products');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    if (fileProcessorService.isSupported(file.originalname)) {
      cb(null, true);
    } else {
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv', 'text/plain'
      ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images, PDF, Word, and Excel files are allowed.'));
    }
  }
});

// Get all products for client
router.get('/', auth, async (req, res) => {
  try {
    const products = await dbHelpers.query(
      'SELECT * FROM products WHERE client_id = $1 AND is_active = true ORDER BY created_at DESC',
      [req.user.id]
    );

    res.json({
      message: 'Products retrieved successfully',
      products: products.map(product => ({
        id: product.id,
        productName: product.product_name,
        productDetails: product.product_details,
        aliasNames: product.alias_names || [],
        isActive: product.is_active,
        createdAt: product.created_at,
        updatedAt: product.updated_at
      }))
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      error: 'Failed to retrieve products',
      message: 'An error occurred while retrieving products'
    });
  }
});

// Add new product
router.post('/', auth, async (req, res) => {
  try {
    const { productName, productDetails, aliasNames } = req.body;

    if (!productName) {
      return res.status(400).json({
        error: 'Product name is required',
        message: 'Please provide a product name'
      });
    }

    const product = await dbHelpers.insert('products', {
      id: uuidv4(),
      client_id: req.user.id,
      product_name: productName,
      product_details: productDetails || {},
      alias_names: aliasNames || [],
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    });

    res.status(201).json({
      message: 'Product added successfully',
      product: {
        id: product.id,
        productName: product.product_name,
        productDetails: product.product_details,
        aliasNames: product.alias_names
      }
    });

  } catch (error) {
    console.error('Add product error:', error);
    res.status(500).json({
      error: 'Failed to add product',
      message: 'An error occurred while adding the product'
    });
  }
});

// Original add product route (keeping for compatibility)
router.post('/legacy', requireClient, asyncHandler(async (req, res) => {
  const { name, price, category, description, availability = true } = req.body;

  if (!name || !price) {
    throw new AppError('Product name and price are required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const productData = {
    name,
    price: parseFloat(price),
    category: category || 'General',
    description: description || '',
    availability
  };

  const { data: product, error } = await supabaseAdmin
    .from('products')
    .insert({
      id: uuidv4(),
      client_id: client.id,
      product_data: productData,
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.status(201).json({
    message: 'Product added successfully',
    product
  });
}));

// Update product
router.put('/:productId', requireClient, asyncHandler(async (req, res) => {
  const { productId } = req.params;
  const { name, price, category, description, availability } = req.body;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Get existing product
  const { data: existingProduct } = await supabaseAdmin
    .from('products')
    .select('*')
    .eq('id', productId)
    .eq('client_id', client.id)
    .single();

  if (!existingProduct) {
    throw new AppError('Product not found', 404);
  }

  // Update product data
  const updatedProductData = { ...existingProduct.product_data };
  if (name !== undefined) updatedProductData.name = name;
  if (price !== undefined) updatedProductData.price = parseFloat(price);
  if (category !== undefined) updatedProductData.category = category;
  if (description !== undefined) updatedProductData.description = description;
  if (availability !== undefined) updatedProductData.availability = availability;

  const { data: updatedProduct, error } = await supabaseAdmin
    .from('products')
    .update({
      product_data: updatedProductData
    })
    .eq('id', productId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.json({
    message: 'Product updated successfully',
    product: updatedProduct
  });
}));

// Delete product
router.delete('/:productId', requireClient, asyncHandler(async (req, res) => {
  const { productId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: deletedProduct, error } = await supabaseAdmin
    .from('products')
    .delete()
    .eq('id', productId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!deletedProduct) {
    throw new AppError('Product not found', 404);
  }

  res.json({
    message: 'Product deleted successfully'
  });
}));

// Bulk import products
router.post('/bulk-import', requireClient, asyncHandler(async (req, res) => {
  const { products } = req.body;

  if (!Array.isArray(products) || products.length === 0) {
    throw new AppError('Products array is required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Validate and prepare products for insertion
  const productsToInsert = products.map(product => {
    if (!product.name || !product.price) {
      throw new AppError('Each product must have name and price', 400);
    }

    return {
      id: uuidv4(),
      client_id: client.id,
      product_data: {
        name: product.name,
        price: parseFloat(product.price),
        category: product.category || 'General',
        description: product.description || '',
        availability: product.availability !== undefined ? product.availability : true
      },
      created_at: new Date().toISOString()
    };
  });

  const { data: insertedProducts, error } = await supabaseAdmin
    .from('products')
    .insert(productsToInsert)
    .select();

  if (error) {
    throw error;
  }

  res.status(201).json({
    message: `${insertedProducts.length} products imported successfully`,
    products: insertedProducts
  });
}));

// Get product categories
router.get('/categories', requireClient, asyncHandler(async (req, res) => {
  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: products, error } = await supabaseAdmin
    .from('products')
    .select('product_data')
    .eq('client_id', client.id);

  if (error) {
    throw error;
  }

  // Extract unique categories
  const categories = [...new Set(
    products
      .map(p => p.product_data?.category)
      .filter(cat => cat && cat !== '')
  )];

  res.json({
    categories
  });
}));

// Bulk upload products with AI processing
router.post('/bulk-upload', auth, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please upload a file'
      });
    }

    // For now, return a simple response since OpenAI integration requires API key setup
    // In production, this would process the file with OpenAI Vision/GPT-4

    // Simulate AI processing with sample data
    const sampleProducts = [
      {
        productName: "Sample Product 1",
        productDetails: {
          "price": "₹299",
          "category": "Food",
          "description": "Delicious sample item"
        },
        aliasNames: ["sample1", "item1"]
      },
      {
        productName: "Sample Product 2",
        productDetails: {
          "price": "₹499",
          "category": "Beverage",
          "description": "Refreshing sample drink"
        },
        aliasNames: ["sample2", "drink1"]
      }
    ];

    // Insert products into database
    const insertedProducts = [];
    for (const product of sampleProducts) {
      try {
        const productData = {
          id: uuidv4(),
          client_id: req.user.id,
          product_name: product.productName,
          product_details: product.productDetails || {},
          alias_names: product.aliasNames || [],
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        };

        const insertedProduct = await dbHelpers.insert('products', productData);
        insertedProducts.push(insertedProduct);
      } catch (insertError) {
        console.error('Error inserting product:', insertError);
      }
    }

    res.json({
      message: 'Products uploaded successfully (Demo Mode)',
      count: insertedProducts.length,
      note: 'This is demo mode. In production, AI will process your uploaded file.',
      products: insertedProducts.map(p => ({
        id: p.id,
        productName: p.product_name,
        productDetails: p.product_details,
        aliasNames: p.alias_names
      }))
    });

  } catch (error) {
    console.error('Bulk upload error:', error);
    res.status(500).json({
      error: 'Bulk upload failed',
      message: 'An error occurred while processing the file'
    });
  }
});

// AI-Powered Smart Bulk Upload Route
router.post('/ai-bulk-upload', auth, upload.array('files', 10), async (req, res) => {
  try {
    console.log('🚀 Starting AI-powered bulk upload...');
    console.log('📁 Files received:', req.files?.length || 0);
    console.log('👤 User:', req.user?.email);

    if (!req.files || req.files.length === 0) {
      console.log('❌ No files uploaded');
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select files to upload'
      });
    }

    // Check if OpenAI is configured
    console.log('🔑 OpenAI configured:', openaiService.isConfigured());
    if (!openaiService.isConfigured()) {
      console.log('❌ OpenAI API key not found');
      return res.status(500).json({
        error: 'OpenAI not configured',
        message: 'OpenAI API key is required for AI-powered uploads'
      });
    }

    const results = {
      totalFiles: req.files.length,
      processedFiles: 0,
      totalProducts: 0,
      successfulProducts: 0,
      failedProducts: 0,
      products: [],
      errors: []
    };

    // Process each file
    for (const file of req.files) {
      try {
        console.log(`📁 Processing file: ${file.originalname}`);

        // Extract product data using AI
        const extractedProducts = await fileProcessorService.processFile(file.path, file.originalname);

        results.processedFiles++;
        results.totalProducts += extractedProducts.length;

        // Save each product to database
        for (const productData of extractedProducts) {
          try {
            // Generate additional aliases if not provided
            if (!productData.alias1 || !productData.alias2) {
              const aliases = await openaiService.generateSmartAliases(
                productData.name,
                productData.category,
                productData.description
              );
              productData.alias1 = productData.alias1 || aliases.alias1;
              productData.alias2 = productData.alias2 || aliases.alias2;
            }

            // Prepare product details as JSONB with key-value pairs (excluding name since it's in product_name column)
            const productDetails = {
              price: parseFloat(productData.price) || 0,
              category: productData.category || 'General',
              availability: productData.availability || 'available',
              // Add all features as key-value pairs
              ...(productData.features || {})
            };

            console.log(`📋 Product details for ${productData.name}:`, productDetails);

            // Insert product into database
            const newProduct = await dbHelpers.insert('products', {
              id: uuidv4(),
              client_id: req.user.id,
              product_name: productData.name,
              product_details: productDetails,
              alias_names: [productData.alias1, productData.alias2],
              is_active: true,
              created_at: new Date(),
              updated_at: new Date()
            });

            results.successfulProducts++;
            results.products.push({
              id: newProduct.id,
              name: productData.name,
              category: productData.category,
              price: productData.price,
              aliases: [productData.alias1, productData.alias2],
              source: file.originalname
            });

            console.log(`✅ Product saved: ${productData.name}`);

          } catch (productError) {
            console.error(`❌ Error saving product: ${productData.name}`, productError);
            results.failedProducts++;
            results.errors.push({
              product: productData.name,
              file: file.originalname,
              error: productError.message
            });
          }
        }

        // Clean up uploaded file
        fs.unlinkSync(file.path);

      } catch (fileError) {
        console.error(`❌ Error processing file: ${file.originalname}`, fileError);
        results.errors.push({
          file: file.originalname,
          error: fileError.message
        });

        // Clean up uploaded file
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      }
    }

    console.log('🎉 AI bulk upload completed:', results);

    res.json({
      message: 'AI-powered bulk upload completed',
      results: results
    });

  } catch (error) {
    console.error('❌ AI bulk upload error:', error);

    // Clean up any remaining files
    if (req.files) {
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }

    res.status(500).json({
      error: 'AI bulk upload failed',
      message: 'An error occurred during AI-powered processing'
    });
  }
});

// Get supported file types for frontend
router.get('/supported-types', (req, res) => {
  res.json({
    message: 'Supported file types retrieved',
    types: fileProcessorService.getSupportedTypes()
  });
});

module.exports = router;
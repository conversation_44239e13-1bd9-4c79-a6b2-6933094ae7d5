const express = require('express');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { dbHelpers } = require('../../backend/config/supabase');
const { superAdminAuth, logSuperAdminAction } = require('../middleware/superAdminAuth');
const router = express.Router();

// Apply super admin authentication to all routes
router.use(superAdminAuth);

// Get super admin dashboard
router.get('/dashboard', async (req, res) => {
  try {
    const dashboardData = await dbHelpers.query(`
      SELECT
        (SELECT COUNT(*) FROM admins WHERE role = 'admin' AND is_active = true) as total_admins,
        (SELECT COUNT(*) FROM admins WHERE role = 'admin' AND is_active = false) as inactive_admins,
        (SELECT COUNT(*) FROM clients WHERE is_active = true) as total_clients,
        (SELECT COUNT(*) FROM admin_client_assignments) as total_assignments,
        (SELECT COUNT(*) FROM clients c LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id WHERE aca.client_id IS NULL AND c.is_active = true) as unassigned_clients,
        (SELECT COUNT(*) FROM clients WHERE assigned_plivo_number IS NOT NULL) as clients_with_numbers,
        (SELECT COUNT(*) FROM support_tickets WHERE status = 'open') as open_tickets,
        (SELECT COUNT(*) FROM call_logs WHERE DATE(call_timestamp) = CURRENT_DATE) as today_calls,
        (SELECT COUNT(*) FROM sms_logs WHERE DATE(sms_timestamp) = CURRENT_DATE) as today_sms
    `);

    // Get admin workload summary
    const adminWorkload = await dbHelpers.query(`
      SELECT
        a.id,
        a.name,
        a.admin_id,
        COALESCE(ac.client_count, 0) as assigned_clients_count,
        (250 - COALESCE(ac.client_count, 0)) as available_slots
      FROM admins a
      LEFT JOIN (
        SELECT admin_id, COUNT(*) as client_count
        FROM admin_client_assignments
        GROUP BY admin_id
      ) ac ON a.id = ac.admin_id
      WHERE a.role = 'admin' AND a.is_active = true
      ORDER BY assigned_clients_count DESC
      LIMIT 5
    `);

    const dashboard = dashboardData[0];

    res.json({
      message: 'Super admin dashboard data retrieved successfully',
      dashboard: {
        adminManagement: {
          totalAdmins: parseInt(dashboard.total_admins),
          inactiveAdmins: parseInt(dashboard.inactive_admins),
          topAdminsByWorkload: adminWorkload.map(admin => ({
            id: admin.id,
            name: admin.name,
            adminId: admin.admin_id,
            assignedClients: parseInt(admin.assigned_clients_count),
            availableSlots: parseInt(admin.available_slots)
          }))
        },
        clientManagement: {
          totalClients: parseInt(dashboard.total_clients),
          assignedClients: parseInt(dashboard.total_assignments),
          unassignedClients: parseInt(dashboard.unassigned_clients),
          clientsWithNumbers: parseInt(dashboard.clients_with_numbers)
        },
        systemActivity: {
          openTickets: parseInt(dashboard.open_tickets),
          todayActivity: {
            calls: parseInt(dashboard.today_calls),
            sms: parseInt(dashboard.today_sms)
          }
        },
        superAdminInfo: {
          name: req.superAdmin.name,
          email: req.superAdmin.email,
          role: req.superAdmin.role,
          hasUnlimitedAccess: true
        }
      }
    });

  } catch (error) {
    console.error('Get super admin dashboard error:', error);
    res.status(500).json({
      error: 'Failed to retrieve dashboard data',
      message: 'An error occurred while retrieving dashboard data'
    });
  }
});

// Get all admins with their workload
router.get('/admins', async (req, res) => {
  try {
    const { page = 1, limit = 50, search, status } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT a.*, 
             COALESCE(ac.client_count, 0) as assigned_clients_count,
             (250 - COALESCE(ac.client_count, 0)) as available_slots
      FROM admins a
      LEFT JOIN (
        SELECT admin_id, COUNT(*) as client_count
        FROM admin_client_assignments 
        GROUP BY admin_id
      ) ac ON a.id = ac.admin_id
      WHERE a.role = 'admin'
    `;
    let params = [];
    let paramIndex = 1;

    // Add search filter
    if (search) {
      query += ` AND (a.name ILIKE $${paramIndex} OR a.email ILIKE $${paramIndex} OR a.admin_id ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    // Add status filter
    if (status === 'active') {
      query += ` AND a.is_active = true`;
    } else if (status === 'inactive') {
      query += ` AND a.is_active = false`;
    }

    query += ` ORDER BY a.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const admins = await dbHelpers.query(query, params);

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM admins WHERE role = 'admin'`;
    let countParams = [];
    let countParamIndex = 1;

    if (search) {
      countQuery += ` AND (name ILIKE $${countParamIndex} OR email ILIKE $${countParamIndex} OR admin_id ILIKE $${countParamIndex})`;
      countParams.push(`%${search}%`);
      countParamIndex++;
    }

    if (status === 'active') {
      countQuery += ` AND is_active = true`;
    } else if (status === 'inactive') {
      countQuery += ` AND is_active = false`;
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Admins retrieved successfully',
      admins: admins.map(admin => ({
        id: admin.id,
        adminId: admin.admin_id,
        name: admin.name,
        email: admin.email,
        isActive: admin.is_active,
        assignedClientsCount: parseInt(admin.assigned_clients_count),
        availableSlots: parseInt(admin.available_slots),
        createdAt: admin.created_at,
        updatedAt: admin.updated_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get admins error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve admins',
      message: 'An error occurred while retrieving admins'
    });
  }
});

// Create new admin
router.post('/admins', async (req, res) => {
  try {
    const { adminId, name, email, password } = req.body;

    if (!adminId || !name || !email || !password) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Admin ID, name, email, and password are required'
      });
    }

    // Check if admin ID or email already exists
    const existingAdmin = await dbHelpers.query(
      'SELECT id FROM admins WHERE admin_id = $1 OR email = $2',
      [adminId, email]
    );

    if (existingAdmin.length > 0) {
      return res.status(409).json({ 
        error: 'Admin already exists',
        message: 'Admin ID or email already exists'
      });
    }

    // Use simple password storage (no bcrypt hashing)
    const passwordHash = password; // Store password as plain text

    // Create admin (always create as regular admin from super admin panel)
    const newAdmin = await dbHelpers.insert('admins', {
      id: uuidv4(),
      admin_id: adminId,
      password_hash: passwordHash,
      name,
      email,
      role: 'admin', // Super admin can only create regular admins
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Log action
    await logSuperAdminAction(
      req.superAdmin.id,
      'create_admin',
      newAdmin.id,
      null,
      { adminId, name, email },
      req
    );

    res.status(201).json({
      message: 'Admin created successfully',
      admin: {
        id: newAdmin.id,
        adminId: newAdmin.admin_id,
        name: newAdmin.name,
        email: newAdmin.email,
        isActive: newAdmin.is_active,
        createdAt: newAdmin.created_at
      }
    });

  } catch (error) {
    console.error('Create admin error:', error);
    res.status(500).json({ 
      error: 'Failed to create admin',
      message: 'An error occurred while creating admin'
    });
  }
});

// Update admin status
router.put('/admins/:adminId/status', async (req, res) => {
  try {
    const { adminId } = req.params;
    const { isActive } = req.body;

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'isActive must be a boolean value'
      });
    }

    const admin = await dbHelpers.findOne('admins', { id: adminId, role: 'admin' });

    if (!admin) {
      return res.status(404).json({ 
        error: 'Admin not found',
        message: 'Admin not found'
      });
    }

    const updatedAdmin = await dbHelpers.update('admins', {
      is_active: isActive,
      updated_at: new Date()
    }, { id: adminId });

    // Log action
    await logSuperAdminAction(
      req.superAdmin.id,
      isActive ? 'activate_admin' : 'deactivate_admin',
      adminId,
      null,
      { previousStatus: admin.is_active, newStatus: isActive },
      req
    );

    res.json({
      message: `Admin ${isActive ? 'activated' : 'deactivated'} successfully`,
      admin: {
        id: updatedAdmin.id,
        adminId: updatedAdmin.admin_id,
        name: updatedAdmin.name,
        email: updatedAdmin.email,
        isActive: updatedAdmin.is_active
      }
    });

  } catch (error) {
    console.error('Update admin status error:', error);
    res.status(500).json({ 
      error: 'Failed to update admin status',
      message: 'An error occurred while updating admin status'
    });
  }
});

// Get unassigned clients
router.get('/clients/unassigned', async (req, res) => {
  try {
    const { page = 1, limit = 50, search } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT c.id, c.username, c.email, c.business_name, c.created_at
      FROM clients c
      LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
      WHERE aca.client_id IS NULL AND c.is_active = true
    `;
    let params = [];
    let paramIndex = 1;

    if (search) {
      query += ` AND (c.username ILIKE $${paramIndex} OR c.email ILIKE $${paramIndex} OR c.business_name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    query += ` ORDER BY c.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const clients = await dbHelpers.query(query, params);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM clients c
      LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
      WHERE aca.client_id IS NULL AND c.is_active = true
    `;
    let countParams = [];

    if (search) {
      countQuery += ` AND (c.username ILIKE $1 OR c.email ILIKE $1 OR c.business_name ILIKE $1)`;
      countParams.push(`%${search}%`);
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Unassigned clients retrieved successfully',
      clients: clients.map(client => ({
        id: client.id,
        username: client.username,
        email: client.email,
        businessName: client.business_name,
        createdAt: client.created_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get unassigned clients error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve unassigned clients',
      message: 'An error occurred while retrieving unassigned clients'
    });
  }
});

// Assign admin to clients (Super admin assigns which admin manages which clients)
router.post('/clients/assign-admin', async (req, res) => {
  try {
    const { adminId, clientIds } = req.body;

    if (!adminId) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'adminId is required'
      });
    }

    if (!Array.isArray(clientIds) || clientIds.length === 0) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'clientIds must be a non-empty array'
      });
    }

    // Check if admin exists and is active
    const admin = await dbHelpers.findOne('admins', {
      id: adminId,
      role: 'admin',
      is_active: true
    });

    if (!admin) {
      return res.status(404).json({
        error: 'Admin not found',
        message: 'Admin not found or inactive'
      });
    }

    // Super admin has no slot limitations, but regular admins still have 250 limit
    // Get current assignments count for the admin
    const currentAssignments = await dbHelpers.query(
      'SELECT COUNT(*) as count FROM admin_client_assignments WHERE admin_id = $1',
      [adminId]
    );

    const currentCount = parseInt(currentAssignments[0].count);
    const availableSlots = 250 - currentCount;

    if (clientIds.length > availableSlots) {
      return res.status(400).json({
        error: 'Insufficient slots',
        message: `Admin only has ${availableSlots} available slots but trying to assign ${clientIds.length} clients`
      });
    }

    // Check which clients are valid and unassigned
    const validClients = await dbHelpers.query(`
      SELECT c.id, c.username, c.email
      FROM clients c
      LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
      WHERE c.id = ANY($1) AND c.is_active = true AND aca.client_id IS NULL
    `, [clientIds]);

    if (validClients.length === 0) {
      return res.status(400).json({
        error: 'No valid clients',
        message: 'No valid unassigned clients found'
      });
    }

    // Assign clients
    const assignments = [];
    for (const client of validClients) {
      const assignment = await dbHelpers.insert('admin_client_assignments', {
        id: uuidv4(),
        admin_id: adminId,
        client_id: client.id,
        assigned_at: new Date()
      });
      assignments.push(assignment);
    }

    // Log action
    await logSuperAdminAction(
      req.superAdmin.id,
      'assign_clients',
      adminId,
      validClients.map(c => c.id),
      {
        assignedCount: validClients.length,
        clientDetails: validClients.map(c => ({ id: c.id, username: c.username, email: c.email }))
      },
      req
    );

    res.json({
      message: `Successfully assigned ${validClients.length} clients to admin ${admin.name}`,
      admin: {
        id: admin.id,
        name: admin.name,
        adminId: admin.admin_id
      },
      assignedClients: validClients.map(client => ({
        id: client.id,
        username: client.username,
        email: client.email
      })),
      assignedCount: validClients.length,
      skippedCount: clientIds.length - validClients.length
    });

  } catch (error) {
    console.error('Assign clients to admin error:', error);
    res.status(500).json({
      error: 'Failed to assign clients',
      message: 'An error occurred while assigning clients'
    });
  }
});

// Assign admin to a single client
router.post('/clients/:clientId/assign-admin', async (req, res) => {
  try {
    const { clientId } = req.params;
    const { adminId } = req.body;

    if (!adminId) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'adminId is required'
      });
    }

    // Check if client exists
    const client = await dbHelpers.findOne('clients', { id: clientId, is_active: true });
    if (!client) {
      return res.status(404).json({
        error: 'Client not found',
        message: 'Client not found or inactive'
      });
    }

    // Check if admin exists and is active
    const admin = await dbHelpers.findOne('admins', {
      id: adminId,
      role: 'admin',
      is_active: true
    });

    if (!admin) {
      return res.status(404).json({
        error: 'Admin not found',
        message: 'Admin not found or inactive'
      });
    }

    // Check if client is already assigned to another admin
    const existingAssignment = await dbHelpers.findOne('admin_client_assignments', {
      client_id: clientId
    });

    if (existingAssignment) {
      // Update existing assignment
      const updatedAssignment = await dbHelpers.update('admin_client_assignments', {
        admin_id: adminId,
        assigned_at: new Date()
      }, { client_id: clientId });

      // Log action
      await logSuperAdminAction(
        req.superAdmin.id,
        'reassign_client',
        adminId,
        [clientId],
        {
          clientDetails: { id: client.id, username: client.username, email: client.email },
          adminDetails: { id: admin.id, name: admin.name, adminId: admin.admin_id },
          previousAdminId: existingAssignment.admin_id
        },
        req
      );

      res.json({
        message: `Client ${client.username} reassigned to admin ${admin.name}`,
        assignment: {
          client: {
            id: client.id,
            username: client.username,
            email: client.email
          },
          admin: {
            id: admin.id,
            name: admin.name,
            adminId: admin.admin_id
          },
          assignedAt: updatedAssignment.assigned_at,
          action: 'reassigned'
        }
      });
    } else {
      // Create new assignment
      const newAssignment = await dbHelpers.insert('admin_client_assignments', {
        id: uuidv4(),
        admin_id: adminId,
        client_id: clientId,
        assigned_at: new Date()
      });

      // Log action
      await logSuperAdminAction(
        req.superAdmin.id,
        'assign_client',
        adminId,
        [clientId],
        {
          clientDetails: { id: client.id, username: client.username, email: client.email },
          adminDetails: { id: admin.id, name: admin.name, adminId: admin.admin_id }
        },
        req
      );

      res.json({
        message: `Client ${client.username} assigned to admin ${admin.name}`,
        assignment: {
          client: {
            id: client.id,
            username: client.username,
            email: client.email
          },
          admin: {
            id: admin.id,
            name: admin.name,
            adminId: admin.admin_id
          },
          assignedAt: newAssignment.assigned_at,
          action: 'assigned'
        }
      });
    }

  } catch (error) {
    console.error('Assign admin to client error:', error);
    res.status(500).json({
      error: 'Failed to assign admin to client',
      message: 'An error occurred while assigning admin to client'
    });
  }
});

// Remove clients from admin
router.delete('/admins/:adminId/clients/:clientId', async (req, res) => {
  try {
    const { adminId, clientId } = req.params;

    // Check if assignment exists
    const assignment = await dbHelpers.findOne('admin_client_assignments', {
      admin_id: adminId,
      client_id: clientId
    });

    if (!assignment) {
      return res.status(404).json({
        error: 'Assignment not found',
        message: 'Client is not assigned to this admin'
      });
    }

    // Get client details for logging
    const client = await dbHelpers.findOne('clients', { id: clientId });

    // Remove assignment
    await dbHelpers.delete('admin_client_assignments', {
      admin_id: adminId,
      client_id: clientId
    });

    // Log action
    await logSuperAdminAction(
      req.superAdmin.id,
      'remove_client_assignment',
      adminId,
      [clientId],
      {
        clientDetails: { id: client.id, username: client.username, email: client.email }
      },
      req
    );

    res.json({
      message: 'Client assignment removed successfully',
      client: {
        id: client.id,
        username: client.username,
        email: client.email
      }
    });

  } catch (error) {
    console.error('Remove client assignment error:', error);
    res.status(500).json({
      error: 'Failed to remove client assignment',
      message: 'An error occurred while removing client assignment'
    });
  }
});

// Get admin's assigned clients
router.get('/admins/:adminId/clients', async (req, res) => {
  try {
    const { adminId } = req.params;
    const { page = 1, limit = 50, search } = req.query;
    const offset = (page - 1) * limit;

    // Check if admin exists
    const admin = await dbHelpers.findOne('admins', {
      id: adminId,
      role: 'admin'
    });

    if (!admin) {
      return res.status(404).json({
        error: 'Admin not found',
        message: 'Admin not found'
      });
    }

    let query = `
      SELECT c.*, aca.assigned_at
      FROM clients c
      JOIN admin_client_assignments aca ON c.id = aca.client_id
      WHERE aca.admin_id = $1
    `;
    let params = [adminId];
    let paramIndex = 2;

    if (search) {
      query += ` AND (c.username ILIKE $${paramIndex} OR c.email ILIKE $${paramIndex} OR c.business_name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    query += ` ORDER BY aca.assigned_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const clients = await dbHelpers.query(query, params);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total
      FROM clients c
      JOIN admin_client_assignments aca ON c.id = aca.client_id
      WHERE aca.admin_id = $1
    `;
    let countParams = [adminId];

    if (search) {
      countQuery += ` AND (c.username ILIKE $2 OR c.email ILIKE $2 OR c.business_name ILIKE $2)`;
      countParams.push(`%${search}%`);
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Admin clients retrieved successfully',
      admin: {
        id: admin.id,
        adminId: admin.admin_id,
        name: admin.name,
        email: admin.email
      },
      clients: clients.map(client => ({
        id: client.id,
        username: client.username,
        email: client.email,
        businessName: client.business_name,
        assignedPlivoNumber: client.assigned_plivo_number,
        isActive: client.is_active,
        assignedAt: client.assigned_at,
        createdAt: client.created_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get admin clients error:', error);
    res.status(500).json({
      error: 'Failed to retrieve admin clients',
      message: 'An error occurred while retrieving admin clients'
    });
  }
});

// Get audit log
router.get('/audit-log', async (req, res) => {
  try {
    const { page = 1, limit = 50, actionType, targetAdminId } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT sal.*, a.name as super_admin_name, ta.name as target_admin_name
      FROM super_admin_audit_log sal
      LEFT JOIN admins a ON sal.super_admin_id = a.id
      LEFT JOIN admins ta ON sal.target_admin_id = ta.id
      WHERE 1=1
    `;
    let params = [];
    let paramIndex = 1;

    if (actionType) {
      query += ` AND sal.action_type = $${paramIndex}`;
      params.push(actionType);
      paramIndex++;
    }

    if (targetAdminId) {
      query += ` AND sal.target_admin_id = $${paramIndex}`;
      params.push(targetAdminId);
      paramIndex++;
    }

    query += ` ORDER BY sal.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const auditLogs = await dbHelpers.query(query, params);

    // Get total count
    let countQuery = `SELECT COUNT(*) as total FROM super_admin_audit_log WHERE 1=1`;
    let countParams = [];
    let countParamIndex = 1;

    if (actionType) {
      countQuery += ` AND action_type = $${countParamIndex}`;
      countParams.push(actionType);
      countParamIndex++;
    }

    if (targetAdminId) {
      countQuery += ` AND target_admin_id = $${countParamIndex}`;
      countParams.push(targetAdminId);
      countParamIndex++;
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Audit log retrieved successfully',
      auditLogs: auditLogs.map(log => ({
        id: log.id,
        actionType: log.action_type,
        superAdminName: log.super_admin_name,
        targetAdminName: log.target_admin_name,
        targetClientIds: log.target_client_ids,
        actionDetails: log.action_details,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        createdAt: log.created_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get audit log error:', error);
    res.status(500).json({
      error: 'Failed to retrieve audit log',
      message: 'An error occurred while retrieving audit log'
    });
  }
});

// Get all clients with admin assignment info
router.get('/clients', async (req, res) => {
  try {
    const { page = 1, limit = 50, search, assignmentStatus } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT c.*,
             a.name as assigned_admin_name,
             a.admin_id as assigned_admin_id,
             a.email as assigned_admin_email,
             aca.assigned_at
      FROM clients c
      LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
      LEFT JOIN admins a ON aca.admin_id = a.id
      WHERE c.is_active = true
    `;
    let params = [];
    let paramIndex = 1;

    // Add search filter
    if (search) {
      query += ` AND (c.username ILIKE $${paramIndex} OR c.email ILIKE $${paramIndex} OR c.business_name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    // Add assignment status filter
    if (assignmentStatus === 'assigned') {
      query += ` AND aca.admin_id IS NOT NULL`;
    } else if (assignmentStatus === 'unassigned') {
      query += ` AND aca.admin_id IS NULL`;
    }

    query += ` ORDER BY c.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const clients = await dbHelpers.query(query, params);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total
      FROM clients c
      LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
      WHERE c.is_active = true
    `;
    let countParams = [];
    let countParamIndex = 1;

    if (search) {
      countQuery += ` AND (c.username ILIKE $${countParamIndex} OR c.email ILIKE $${countParamIndex} OR c.business_name ILIKE $${countParamIndex})`;
      countParams.push(`%${search}%`);
      countParamIndex++;
    }

    if (assignmentStatus === 'assigned') {
      countQuery += ` AND aca.admin_id IS NOT NULL`;
    } else if (assignmentStatus === 'unassigned') {
      countQuery += ` AND aca.admin_id IS NULL`;
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Clients retrieved successfully',
      clients: clients.map(client => ({
        id: client.id,
        username: client.username,
        email: client.email,
        businessName: client.business_name,
        businessSummary: client.business_summary,
        ownerName: client.owner_name,
        mobileNumber: client.mobile_number,
        businessEmail: client.business_email,
        whatsappNumber: client.whatsapp_number,
        assignedPlivoNumber: client.assigned_plivo_number,
        isActive: client.is_active,
        createdAt: client.created_at,
        lastLoginAt: client.last_login_at,
        assignedAdmin: client.assigned_admin_name ? {
          name: client.assigned_admin_name,
          adminId: client.assigned_admin_id,
          email: client.assigned_admin_email,
          assignedAt: client.assigned_at
        } : null
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      },
      summary: {
        totalClients: total,
        assignedClients: clients.filter(c => c.assigned_admin_name).length,
        unassignedClients: clients.filter(c => !c.assigned_admin_name).length
      }
    });

  } catch (error) {
    console.error('Get super admin clients error:', error);
    res.status(500).json({
      error: 'Failed to retrieve clients',
      message: 'An error occurred while retrieving clients'
    });
  }
});

// Remove admin assignment from client
router.delete('/clients/:clientId/remove-admin', async (req, res) => {
  try {
    const { clientId } = req.params;

    // Check if client exists
    const client = await dbHelpers.findOne('clients', { id: clientId });
    if (!client) {
      return res.status(404).json({
        error: 'Client not found',
        message: 'Client not found'
      });
    }

    // Check if assignment exists
    const assignment = await dbHelpers.findOne('admin_client_assignments', {
      client_id: clientId
    });

    if (!assignment) {
      return res.status(404).json({
        error: 'Assignment not found',
        message: 'Client is not assigned to any admin'
      });
    }

    // Get admin details for logging
    const admin = await dbHelpers.findOne('admins', { id: assignment.admin_id });

    // Remove assignment
    await dbHelpers.delete('admin_client_assignments', {
      client_id: clientId
    });

    // Log action
    await logSuperAdminAction(
      req.superAdmin.id,
      'remove_client_assignment',
      assignment.admin_id,
      [clientId],
      {
        clientDetails: { id: client.id, username: client.username, email: client.email },
        adminDetails: admin ? { id: admin.id, name: admin.name, adminId: admin.admin_id } : null
      },
      req
    );

    res.json({
      message: `Client ${client.username} unassigned from admin`,
      client: {
        id: client.id,
        username: client.username,
        email: client.email
      },
      previousAdmin: admin ? {
        id: admin.id,
        name: admin.name,
        adminId: admin.admin_id
      } : null
    });

  } catch (error) {
    console.error('Remove client assignment error:', error);
    res.status(500).json({
      error: 'Failed to remove client assignment',
      message: 'An error occurred while removing client assignment'
    });
  }
});

module.exports = router;

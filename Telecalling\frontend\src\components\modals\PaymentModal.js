import React, { useState, useEffect } from 'react';
import { X, CreditCard, Check, AlertCircle, Loader } from 'lucide-react';
import subscriptionAPI from '../../services/subscriptionAPI';
import { useAuth } from '../../contexts/AuthContext';

const PaymentModal = ({ isOpen, onClose, selectedPlan, onSuccess }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [billingPeriod, setBillingPeriod] = useState('monthly');
  const [error, setError] = useState('');
  const [plans, setPlans] = useState([]);

  useEffect(() => {
    if (isOpen) {
      fetchPlans();
    }
  }, [isOpen]);

  const fetchPlans = async () => {
    try {
      const response = await subscriptionAPI.getPlans();
      if (response.success) {
        setPlans(response.plans);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
    }
  };

  const getPlanDetails = () => {
    if (!selectedPlan) return null;
    
    // Find plan from API data or use selectedPlan data
    const apiPlan = plans.find(p => p.name === selectedPlan.name);
    return apiPlan || selectedPlan;
  };

  const calculateAmount = (plan) => {
    if (!plan) return 0;
    const basePrice = plan.price || 0;
    // Only monthly billing is allowed
    return basePrice;
  };

  const handlePayment = async () => {
    try {
      console.log('PaymentModal: handlePayment called');
      setLoading(true);
      setError('');

      const plan = getPlanDetails();
      console.log('PaymentModal: plan details:', plan);
      if (!plan) {
        throw new Error('Plan not found');
      }

      // Create payment order
      console.log('PaymentModal: creating order for plan:', plan.id, 'period:', billingPeriod);
      const orderResponse = await subscriptionAPI.createOrder(plan.id, billingPeriod);
      
      if (!orderResponse.success) {
        throw new Error(orderResponse.error || 'Failed to create payment order');
      }

      // Open Razorpay payment modal
      await subscriptionAPI.openPaymentModal(
        {
          ...orderResponse,
          billingPeriod,
          user: {
            name: user?.business_name || user?.username || '',
            email: user?.email || '',
            phone: user?.phone || ''
          }
        },
        (result) => {
          setLoading(false);
          onSuccess(result);
          onClose();
        },
        (error) => {
          setLoading(false);
          setError(error.message || 'Payment failed');
        }
      );
    } catch (error) {
      setLoading(false);
      setError(error.message || 'Failed to initiate payment');
    }
  };

  if (!isOpen) return null;

  const plan = getPlanDetails();
  const amount = calculateAmount(plan);

  return (
    <div className="modal-overlay">
      <div className="modal-container max-w-md" style={{background: 'white'}}>
        <div className="modal-header">
          <h2 className="modal-title">
            <CreditCard className="w-5 h-5" />
            Complete Payment
          </h2>
          <button onClick={onClose} className="modal-close">
            <X size={20} />
          </button>
        </div>

        <div className="modal-body">
          {plan && (
            <>
              {/* Plan Summary */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-lg text-gray-900 mb-2">
                  {plan.display_name || plan.name}
                </h3>
                <p className="text-gray-600 text-sm mb-3">
                  {plan.description}
                </p>
                
                {/* Billing Period - Monthly Only */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Billing Period
                  </label>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <Check className="w-4 h-4 text-blue-600 mr-2" />
                      <span className="text-sm font-medium text-blue-800">Monthly Subscription</span>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      Currently, only monthly subscriptions are available.
                    </p>
                  </div>
                </div>

                {/* Price Display */}
                <div className="border-t pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Monthly Price:</span>
                    <span className="text-2xl font-bold text-gray-900">
                      ₹{amount.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Features List */}
              {plan.features && Array.isArray(plan.features) && (
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">What's included:</h4>
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center">
                    <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                    <span className="text-sm text-red-700">{error}</span>
                  </div>
                </div>
              )}

              {/* Payment Button */}
              <button
                onClick={handlePayment}
                disabled={loading}
                className="w-full btn btn-primary flex items-center justify-center"
              >
                {loading ? (
                  <>
                    <Loader className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="w-4 h-4 mr-2" />
                    Pay ₹{amount.toLocaleString()}
                  </>
                )}
              </button>

              {/* Security Note */}
              <div className="mt-4 text-center">
                <p className="text-xs text-gray-500">
                  Secured by Razorpay • Your payment information is encrypted and secure
                </p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;

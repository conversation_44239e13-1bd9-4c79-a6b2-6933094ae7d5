import React, { useState, useEffect } from 'react';
import { CreditCard, TrendingUp, Crown, Zap, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import subscriptionAPI from '../../services/subscriptionAPI';

const QuickPaymentWidget = () => {
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSubscription();
  }, []);

  const fetchSubscription = async () => {
    try {
      const response = await subscriptionAPI.getCurrentSubscription();
      if (response.success) {
        setSubscription(response.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const currentPlan = subscription?.subscription_plans;
  const isFreePlan = !subscription || currentPlan?.name === 'free';

  // Don't show widget if user has active paid subscription
  if (!isFreePlan) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-lg p-4 text-white">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Crown className="w-5 h-5" />
          <span className="font-semibold">Upgrade Available</span>
        </div>
        <TrendingUp className="w-4 h-4 opacity-75" />
      </div>
      
      <p className="text-blue-100 text-sm mb-4">
        Unlock advanced features and scale your business with our Pro plan.
      </p>
      
      <div className="flex items-center justify-between">
        <div>
          <div className="text-lg font-bold">₹999/month</div>
          <div className="text-xs text-blue-200">500 calls + advanced features</div>
        </div>
        <Link
          to="/dashboard/payments"
          className="bg-white text-blue-600 px-3 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors flex items-center"
        >
          <Zap className="w-3 h-3 mr-1" />
          Upgrade
          <ArrowRight className="w-3 h-3 ml-1" />
        </Link>
      </div>
    </div>
  );
};

export default QuickPaymentWidget;

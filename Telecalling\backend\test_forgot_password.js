const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:5000/api';

// Test forgot password flow
async function testForgotPasswordFlow() {
  console.log('🔐 Testing Forgot Password Flow...\n');

  const testEmail = '<EMAIL>';
  const testPassword = 'newpassword123';

  try {
    // Step 1: Test Forgot Password Request
    console.log('1️⃣ Testing Forgot Password Request...');
    const forgotResponse = await axios.post(`${BASE_URL}/auth/forgot-password`, {
      email: testEmail
    });

    console.log('✅ Forgot password request successful:', forgotResponse.data);
    console.log('📧 Check your email for OTP\n');

    // Step 2: Simulate OTP entry (you'll need to manually get the OTP)
    console.log('2️⃣ To test password reset, you need to:');
    console.log('   a) Check the email for the OTP code');
    console.log('   b) Run the reset password test with the OTP\n');

    console.log('3️⃣ Test Reset Password with OTP:');
    console.log(`curl -X POST ${BASE_URL}/auth/reset-password \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{`);
    console.log(`    "email": "${testEmail}",`);
    console.log(`    "otp": "YOUR_OTP_HERE",`);
    console.log(`    "newPassword": "${testPassword}"`);
    console.log(`  }'`);
    console.log();

    // Step 3: Test with a sample OTP (will fail but shows the flow)
    console.log('4️⃣ Testing Reset Password with sample OTP (will fail)...');
    try {
      const resetResponse = await axios.post(`${BASE_URL}/auth/reset-password`, {
        email: testEmail,
        otp: '123456', // This will fail but shows the validation
        newPassword: testPassword
      });
      console.log('✅ Reset password successful:', resetResponse.data);
    } catch (resetError) {
      console.log('❌ Reset password failed (expected with sample OTP):', resetError.response?.data?.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Test with real OTP (call this function with actual OTP)
async function testResetPasswordWithOTP(email, otp, newPassword) {
  console.log(`🔐 Testing Reset Password with OTP: ${otp}...\n`);

  try {
    const resetResponse = await axios.post(`${BASE_URL}/auth/reset-password`, {
      email,
      otp,
      newPassword
    });

    console.log('✅ Reset password successful:', resetResponse.data);
    
    // Test login with new password
    console.log('\n5️⃣ Testing login with new password...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email,
      password: newPassword
    });

    console.log('✅ Login with new password successful:', {
      user: loginResponse.data.user,
      hasTokens: !!loginResponse.data.tokens
    });

  } catch (error) {
    console.error('❌ Reset password test failed:', error.response?.data || error.message);
  }
}

// Check if user exists first
async function checkUserExists(email) {
  console.log(`🔍 Checking if user exists: ${email}...\n`);
  
  try {
    // Try to login with a dummy password to see if user exists
    await axios.post(`${BASE_URL}/auth/login`, {
      email,
      password: 'dummy_password'
    });
  } catch (error) {
    if (error.response?.data?.error === 'Invalid credentials') {
      console.log('✅ User exists (got invalid credentials error)');
      return true;
    } else if (error.response?.data?.error === 'User not found') {
      console.log('❌ User does not exist');
      return false;
    } else if (error.response?.data?.error === 'Email not verified') {
      console.log('✅ User exists but email not verified');
      return true;
    } else {
      console.log('⚠️ Unexpected error:', error.response?.data?.error);
      return false;
    }
  }
}

// Main test runner
async function runForgotPasswordTests() {
  console.log('🚀 Starting Forgot Password Tests\n');
  console.log('=' .repeat(50));
  
  const testEmail = '<EMAIL>';
  
  // Check if user exists
  const userExists = await checkUserExists(testEmail);
  
  if (!userExists) {
    console.log('\n❌ Cannot test forgot password - user does not exist');
    console.log('Please create a user first using the signup flow');
    return;
  }
  
  // Run forgot password flow
  await testForgotPasswordFlow();
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 Forgot Password Tests completed!');
  console.log('\nNext steps:');
  console.log('1. Check your email for the OTP code');
  console.log('2. Use testResetPasswordWithOTP(email, otp, newPassword) with real OTP');
  console.log('3. Test the frontend forgot password flow');
}

// Export functions for manual testing
module.exports = {
  testForgotPasswordFlow,
  testResetPasswordWithOTP,
  checkUserExists,
  runForgotPasswordTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runForgotPasswordTests().catch(console.error);
}

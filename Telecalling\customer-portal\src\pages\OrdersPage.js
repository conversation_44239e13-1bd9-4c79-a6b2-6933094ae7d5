import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Package, Calendar, Eye, Filter } from 'lucide-react';

const OrdersPage = () => {
  const [orders] = useState([
    {
      id: '1',
      orderNumber: 'ORD-001',
      title: 'Product Inquiry - Electronics',
      description: 'Inquiry about laptop specifications and pricing',
      status: 'completed',
      date: '2024-01-15',
      amount: 2500,
      products: ['Laptop', 'Mouse', 'Keyboard']
    },
    {
      id: '2',
      orderNumber: 'ORD-002',
      title: 'Service Request - Home Appliances',
      description: 'Repair service for washing machine',
      status: 'pending',
      date: '2024-01-20',
      amount: 1800,
      products: ['Washing Machine Service']
    },
    {
      id: '3',
      orderNumber: 'ORD-003',
      title: 'Product Purchase - Mobile Phone',
      description: 'Purchase of smartphone with accessories',
      status: 'processing',
      date: '2024-01-22',
      amount: 25000,
      products: ['iPhone 15', 'Case', 'Screen Protector']
    }
  ]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return { bg: '#dcfce7', color: '#166534' };
      case 'pending':
        return { bg: '#fef3c7', color: '#92400e' };
      case 'processing':
        return { bg: '#dbeafe', color: '#1e40af' };
      default:
        return { bg: '#f3f4f6', color: '#374151' };
    }
  };

  return (
    <div className="orders-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <header style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
            My Orders
          </h1>
          <Link 
            to="/dashboard" 
            style={{ 
              padding: '0.5rem 1rem', 
              backgroundColor: '#3b82f6', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '4px'
            }}
          >
            Back to Dashboard
          </Link>
        </div>
      </header>

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem 1rem' }}>
        {/* Stats */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginBottom: '2rem' }}>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6' }}>{orders.length}</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Total Orders</div>
          </div>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981' }}>{orders.filter(o => o.status === 'completed').length}</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Completed</div>
          </div>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b' }}>{orders.filter(o => o.status === 'pending').length}</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Pending</div>
          </div>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937' }}>₹{orders.reduce((sum, o) => sum + o.amount, 0).toLocaleString()}</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Total Value</div>
          </div>
        </div>

        {/* Orders List */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
          <div style={{ padding: '1.5rem', borderBottom: '1px solid #e5e7eb' }}>
            <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>Order History</h2>
          </div>

          <div style={{ padding: '0' }}>
            {orders.map((order) => {
              const statusStyle = getStatusColor(order.status);
              return (
                <div key={order.id} style={{ padding: '1.5rem', borderBottom: '1px solid #e5e7eb' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                        <Package size={20} style={{ color: '#3b82f6' }} />
                        <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>
                          {order.title}
                        </h3>
                      </div>
                      <p style={{ color: '#6b7280', marginBottom: '0.5rem' }}>{order.description}</p>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                        <span>{order.orderNumber}</span>
                        <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                          <Calendar size={14} />
                          {new Date(order.date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div style={{ 
                        padding: '0.25rem 0.75rem', 
                        borderRadius: '12px', 
                        fontSize: '0.75rem', 
                        fontWeight: '500',
                        backgroundColor: statusStyle.bg,
                        color: statusStyle.color,
                        marginBottom: '0.5rem'
                      }}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </div>
                      <div style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937' }}>
                        ₹{order.amount.toLocaleString()}
                      </div>
                    </div>
                  </div>

                  <div style={{ marginBottom: '1rem' }}>
                    <h4 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginBottom: '0.5rem' }}>Products/Services:</h4>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
                      {order.products.map((product, index) => (
                        <span key={index} style={{ 
                          padding: '0.25rem 0.5rem', 
                          backgroundColor: '#f3f4f6', 
                          borderRadius: '4px', 
                          fontSize: '0.75rem',
                          color: '#374151'
                        }}>
                          {product}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div style={{ display: 'flex', gap: '0.5rem' }}>
                    <Link 
                      to={`/orders/${order.id}`}
                      style={{ 
                        padding: '0.5rem 1rem', 
                        backgroundColor: '#3b82f6', 
                        color: 'white', 
                        textDecoration: 'none', 
                        borderRadius: '4px',
                        fontSize: '0.875rem',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem'
                      }}
                    >
                      <Eye size={14} />
                      View Details
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {orders.length === 0 && (
          <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '3rem', textAlign: 'center' }}>
            <Package size={48} style={{ color: '#9ca3af', margin: '0 auto 1rem' }} />
            <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', marginBottom: '0.5rem' }}>No Orders Yet</h3>
            <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>You haven't placed any orders yet. Start by contacting our team!</p>
            <Link 
              to="/chats" 
              style={{ 
                padding: '0.75rem 1.5rem', 
                backgroundColor: '#3b82f6', 
                color: 'white', 
                textDecoration: 'none', 
                borderRadius: '4px',
                fontSize: '1rem',
                fontWeight: '500'
              }}
            >
              Start a Chat
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrdersPage;

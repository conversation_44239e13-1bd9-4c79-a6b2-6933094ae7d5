const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { db<PERSON><PERSON><PERSON> } = require('../../backend/config/supabase');
const { Resend } = require('resend');
const { adminOrSuperAdminAuth } = require('../middleware/superAdminAuth');
const router = express.Router();

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Apply admin or super admin authentication to all routes
router.use(adminOrSuperAdminAuth);

// Get all support tickets for admin's clients
router.get('/tickets', async (req, res) => {
  try {
    const { page = 1, limit = 50, status, urgencyLevel } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT st.*, c.username as client_name, c.email as client_email, c.business_name,
             a.name as admin_name
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      JOIN clients c ON st.client_id = c.id
      LEFT JOIN admins a ON st.admin_id = a.id
      WHERE aca.admin_id = $1
    `;
    let params = [req.admin.id];
    let paramIndex = 2;

    // Add filters
    if (status) {
      query += ` AND st.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (urgencyLevel) {
      query += ` AND st.urgency_level = $${paramIndex}`;
      params.push(urgencyLevel);
      paramIndex++;
    }

    query += ` ORDER BY st.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const tickets = await dbHelpers.query(query, params);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      WHERE aca.admin_id = $1
    `;
    let countParams = [req.admin.id];
    let countParamIndex = 2;

    if (status) {
      countQuery += ` AND st.status = $${countParamIndex}`;
      countParams.push(status);
      countParamIndex++;
    }

    if (urgencyLevel) {
      countQuery += ` AND st.urgency_level = $${countParamIndex}`;
      countParams.push(urgencyLevel);
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Support tickets retrieved successfully',
      tickets: tickets.map(ticket => ({
        id: ticket.id,
        subject: ticket.subject,
        message: ticket.message,
        urgencyLevel: ticket.urgency_level,
        status: ticket.status,
        clientName: ticket.client_name,
        clientEmail: ticket.client_email,
        businessName: ticket.business_name,
        adminName: ticket.admin_name,
        adminResponse: ticket.admin_response,
        resolvedAt: ticket.resolved_at,
        createdAt: ticket.created_at,
        updatedAt: ticket.updated_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get admin support tickets error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve support tickets',
      message: 'An error occurred while retrieving support tickets'
    });
  }
});

// Get single support ticket
router.get('/tickets/:ticketId', async (req, res) => {
  try {
    const { ticketId } = req.params;

    const ticket = await dbHelpers.query(`
      SELECT st.*, c.username as client_name, c.email as client_email, c.business_name,
             c.mobile_number, c.business_email, c.assigned_plivo_number,
             a.name as admin_name
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      JOIN clients c ON st.client_id = c.id
      LEFT JOIN admins a ON st.admin_id = a.id
      WHERE st.id = $1 AND aca.admin_id = $2
    `, [ticketId, req.admin.id]);

    if (!ticket.length) {
      return res.status(404).json({ 
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    const ticketData = ticket[0];

    res.json({
      message: 'Support ticket retrieved successfully',
      ticket: {
        id: ticketData.id,
        subject: ticketData.subject,
        message: ticketData.message,
        urgencyLevel: ticketData.urgency_level,
        status: ticketData.status,
        clientInfo: {
          name: ticketData.client_name,
          email: ticketData.client_email,
          businessName: ticketData.business_name,
          mobileNumber: ticketData.mobile_number,
          businessEmail: ticketData.business_email,
          assignedPlivoNumber: ticketData.assigned_plivo_number
        },
        adminName: ticketData.admin_name,
        adminResponse: ticketData.admin_response,
        resolvedAt: ticketData.resolved_at,
        createdAt: ticketData.created_at,
        updatedAt: ticketData.updated_at
      }
    });

  } catch (error) {
    console.error('Get support ticket error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve support ticket',
      message: 'An error occurred while retrieving support ticket'
    });
  }
});

// Respond to support ticket
router.put('/tickets/:ticketId/respond', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { response, status = 'in-progress' } = req.body;

    if (!response) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Response is required'
      });
    }

    const validStatuses = ['open', 'in-progress', 'resolved', 'closed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Invalid status'
      });
    }

    // Get ticket details and verify admin access
    const ticket = await dbHelpers.query(`
      SELECT st.*, c.email as client_email, c.username as client_name
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      JOIN clients c ON st.client_id = c.id
      WHERE st.id = $1 AND aca.admin_id = $2
    `, [ticketId, req.admin.id]);

    if (!ticket.length) {
      return res.status(404).json({ 
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    const ticketData = ticket[0];

    // Update ticket
    const updatedTicket = await dbHelpers.update('support_tickets', {
      admin_id: req.admin.id,
      admin_response: response,
      status,
      resolved_at: status === 'resolved' ? new Date() : null,
      updated_at: new Date()
    }, { id: ticketId });

    // Send email notification to client
    try {
      await resend.emails.send({
        from: '<EMAIL>',
        to: ticketData.client_email,
        subject: `Support Ticket Update: ${ticketData.subject}`,
        html: `
          <h2>Support Ticket Update</h2>
          <p>Hello ${ticketData.client_name},</p>
          <p>Your support ticket has been updated:</p>
          <p><strong>Ticket ID:</strong> ${ticketData.id}</p>
          <p><strong>Subject:</strong> ${ticketData.subject}</p>
          <p><strong>Status:</strong> ${status}</p>
          <p><strong>Admin Response:</strong></p>
          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
            ${response.replace(/\n/g, '<br>')}
          </div>
          <p>You can view your ticket details in your dashboard.</p>
          <p>Best regards,<br>Tecnvi AI Support Team</p>
        `
      });
    } catch (emailError) {
      console.error('Failed to send ticket response email:', emailError);
    }

    res.json({
      message: 'Support ticket updated successfully',
      ticket: {
        id: updatedTicket.id,
        status: updatedTicket.status,
        adminResponse: updatedTicket.admin_response,
        resolvedAt: updatedTicket.resolved_at,
        updatedAt: updatedTicket.updated_at
      }
    });

  } catch (error) {
    console.error('Admin respond to ticket error:', error);
    res.status(500).json({ 
      error: 'Failed to update support ticket',
      message: 'An error occurred while updating support ticket'
    });
  }
});

// Get support ticket statistics
router.get('/stats', async (req, res) => {
  try {
    const adminId = req.admin.id;

    const stats = await dbHelpers.query(`
      SELECT 
        COUNT(*) as total_tickets,
        COUNT(CASE WHEN st.status = 'open' THEN 1 END) as open_tickets,
        COUNT(CASE WHEN st.status = 'in-progress' THEN 1 END) as in_progress_tickets,
        COUNT(CASE WHEN st.status = 'resolved' THEN 1 END) as resolved_tickets,
        COUNT(CASE WHEN st.urgency_level = 'urgent' THEN 1 END) as urgent_tickets,
        COUNT(CASE WHEN st.urgency_level = 'high' THEN 1 END) as high_priority_tickets
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      WHERE aca.admin_id = $1
    `, [adminId]);

    const statsData = stats[0];

    res.json({
      message: 'Support statistics retrieved successfully',
      stats: {
        totalTickets: parseInt(statsData.total_tickets),
        openTickets: parseInt(statsData.open_tickets),
        inProgressTickets: parseInt(statsData.in_progress_tickets),
        resolvedTickets: parseInt(statsData.resolved_tickets),
        urgentTickets: parseInt(statsData.urgent_tickets),
        highPriorityTickets: parseInt(statsData.high_priority_tickets)
      }
    });

  } catch (error) {
    console.error('Get support stats error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve support statistics',
      message: 'An error occurred while retrieving support statistics'
    });
  }
});

// Get messages for a support ticket (admin side)
router.get('/tickets/:ticketId/messages', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    // Verify admin has access to this ticket
    const ticket = await dbHelpers.query(`
      SELECT st.*
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      WHERE st.id = $1 AND aca.admin_id = $2
    `, [ticketId, req.admin.id]);

    if (!ticket.length) {
      return res.status(404).json({
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    // Get messages for the ticket
    const messages = await dbHelpers.query(`
      SELECT stm.*,
             CASE
               WHEN stm.sender_type = 'client' THEN c.username
               WHEN stm.sender_type = 'admin' THEN a.name
             END as sender_name
      FROM support_ticket_messages stm
      LEFT JOIN clients c ON stm.sender_type = 'client' AND stm.sender_id = c.id
      LEFT JOIN admins a ON stm.sender_type = 'admin' AND stm.sender_id = a.id
      WHERE stm.ticket_id = $1
      ORDER BY stm.created_at ASC
      LIMIT $2 OFFSET $3
    `, [ticketId, limit, offset]);

    // Mark messages as read by admin
    await dbHelpers.query(`
      UPDATE support_ticket_messages
      SET is_read = true
      WHERE ticket_id = $1 AND sender_type = 'client' AND is_read = false
    `, [ticketId]);

    res.json({
      message: 'Messages retrieved successfully',
      messages: messages.map(msg => ({
        id: msg.id,
        message: msg.message,
        messageType: msg.message_type,
        senderType: msg.sender_type,
        senderName: msg.sender_name,
        attachmentUrl: msg.attachment_url,
        isRead: msg.is_read,
        createdAt: msg.created_at
      }))
    });

  } catch (error) {
    console.error('Get ticket messages error:', error);
    res.status(500).json({
      error: 'Failed to retrieve messages',
      message: 'An error occurred while retrieving ticket messages'
    });
  }
});

// Send message to support ticket (admin side)
router.post('/tickets/:ticketId/messages', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { message, messageType = 'text', attachmentUrl } = req.body;

    if (!message || message.trim() === '') {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Message content is required'
      });
    }

    // Verify admin has access to this ticket
    const ticket = await dbHelpers.query(`
      SELECT st.*
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      WHERE st.id = $1 AND aca.admin_id = $2
    `, [ticketId, req.admin.id]);

    if (!ticket.length) {
      return res.status(404).json({
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    // Create message
    const newMessage = await dbHelpers.insert('support_ticket_messages', {
      id: uuidv4(),
      ticket_id: ticketId,
      sender_type: 'admin',
      sender_id: req.admin.id,
      message: message.trim(),
      message_type: messageType,
      attachment_url: attachmentUrl || null,
      is_read: false,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Assign admin to ticket if not already assigned
    const ticketData = ticket[0];
    if (!ticketData.admin_id) {
      await dbHelpers.update('support_tickets', {
        admin_id: req.admin.id,
        status: 'in-progress',
        updated_at: new Date()
      }, { id: ticketId });
    }

    res.status(201).json({
      message: 'Message sent successfully',
      messageData: {
        id: newMessage.id,
        message: newMessage.message,
        messageType: newMessage.message_type,
        senderType: 'admin',
        senderName: req.admin.name,
        attachmentUrl: newMessage.attachment_url,
        isRead: false,
        createdAt: newMessage.created_at
      }
    });

  } catch (error) {
    console.error('Send ticket message error:', error);
    res.status(500).json({
      error: 'Failed to send message',
      message: 'An error occurred while sending the message'
    });
  }
});

// Update ticket status (resolve/close)
router.put('/tickets/:ticketId/status', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Status is required'
      });
    }

    const validStatuses = ['open', 'in-progress', 'resolved', 'closed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid status'
      });
    }

    // Get ticket details and verify admin access
    const ticket = await dbHelpers.query(`
      SELECT st.*, c.email as client_email, c.username as client_name
      FROM support_tickets st
      JOIN admin_client_assignments aca ON st.client_id = aca.client_id
      JOIN clients c ON st.client_id = c.id
      WHERE st.id = $1 AND aca.admin_id = $2
    `, [ticketId, req.admin.id]);

    if (!ticket.length) {
      return res.status(404).json({
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    const ticketData = ticket[0];

    // Update ticket status
    const updatedTicket = await dbHelpers.update('support_tickets', {
      admin_id: req.admin.id,
      status,
      resolved_at: status === 'resolved' || status === 'closed' ? new Date() : null,
      updated_at: new Date()
    }, { id: ticketId });

    // Send email notification to client if ticket is resolved or closed
    if (status === 'resolved' || status === 'closed') {
      try {
        await resend.emails.send({
          from: '<EMAIL>',
          to: ticketData.client_email,
          subject: `Support Ticket ${status === 'resolved' ? 'Resolved' : 'Closed'}: ${ticketData.subject}`,
          html: `
            <h2>Support Ticket ${status === 'resolved' ? 'Resolved' : 'Closed'}</h2>
            <p>Hello ${ticketData.client_name},</p>
            <p>Your support ticket has been ${status}:</p>
            <p><strong>Ticket ID:</strong> ${ticketData.id}</p>
            <p><strong>Subject:</strong> ${ticketData.subject}</p>
            <p><strong>Status:</strong> ${status.toUpperCase()}</p>
            <p>Thank you for using our support system.</p>
            <p>Best regards,<br>Tecnvi AI Support Team</p>
          `
        });
      } catch (emailError) {
        console.error('Failed to send ticket status email:', emailError);
      }
    }

    res.json({
      message: `Support ticket ${status} successfully`,
      ticket: {
        id: updatedTicket.id,
        status: updatedTicket.status,
        resolvedAt: updatedTicket.resolved_at,
        updatedAt: updatedTicket.updated_at
      }
    });

  } catch (error) {
    console.error('Update ticket status error:', error);
    res.status(500).json({
      error: 'Failed to update ticket status',
      message: 'An error occurred while updating ticket status'
    });
  }
});

module.exports = router;

import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Calendar, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Clock,
  RefreshCw,
  ArrowRight,
  DollarSign,
  Receipt,
  Crown,
  Zap
} from 'lucide-react';
import { Link } from 'react-router-dom';
import subscriptionAPI from '../../services/subscriptionAPI';
import PaymentModal from '../modals/PaymentModal';
import PaymentSuccessModal from '../modals/PaymentSuccessModal';

const BillingSection = () => {
  const [subscription, setSubscription] = useState(null);
  const [plans, setPlans] = useState([]);
  const [recentPayments, setRecentPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentResult, setPaymentResult] = useState(null);

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    try {
      setLoading(true);
      const [subscriptionResponse, plansResponse, paymentsResponse] = await Promise.all([
        subscriptionAPI.getCurrentSubscription(),
        subscriptionAPI.getPlans(),
        subscriptionAPI.getPaymentHistory(1, 3) // Get last 3 payments
      ]);

      if (subscriptionResponse.success) {
        setSubscription(subscriptionResponse.subscription);
      }

      if (plansResponse.success) {
        setPlans(plansResponse.plans);
      }

      if (paymentsResponse.success) {
        setRecentPayments(paymentsResponse.payments);
      }
    } catch (error) {
      console.error('Error fetching billing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = (plan) => {
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = (result) => {
    setPaymentResult(result);
    setShowPaymentModal(false);
    setShowSuccessModal(true);
    fetchBillingData(); // Refresh data
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'Active' },
      expired: { color: 'bg-red-100 text-red-800', icon: AlertCircle, text: 'Expired' },
      free: { color: 'bg-gray-100 text-gray-800', icon: Clock, text: 'Free Plan' },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, text: 'Pending' }
    };

    const config = statusConfig[status] || statusConfig.free;
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </span>
    );
  };

  const getPaymentStatusBadge = (status) => {
    const statusConfig = {
      paid: { color: 'bg-green-100 text-green-800', text: 'Paid' },
      failed: { color: 'bg-red-100 text-red-800', text: 'Failed' },
      pending: { color: 'bg-yellow-100 text-yellow-800', text: 'Pending' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  const currentPlan = subscription?.subscription_plans;
  const isFreePlan = !subscription || currentPlan?.name === 'free';
  const nextPlan = plans.find(p => p.sort_order > (currentPlan?.sort_order || 0));

  return (
    <>
      <div className="bg-white rounded-lg shadow">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <CreditCard className="w-5 h-5 mr-2" />
              Billing & Subscription
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={fetchBillingData}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                title="Refresh"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
              <Link
                to="/dashboard/payments"
                className="btn btn-outline btn-sm"
              >
                View All
              </Link>
            </div>
          </div>

          {/* Current Plan Card */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-6 border border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {isFreePlan ? (
                    <Clock className="w-8 h-8 text-gray-600" />
                  ) : (
                    <Crown className="w-8 h-8 text-blue-600" />
                  )}
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">
                    {currentPlan?.display_name || 'Free Plan'}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {currentPlan?.description || 'Basic features for testing'}
                  </p>
                  {subscription?.ends_at && (
                    <p className="text-xs text-gray-500 mt-1 flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      Renews on {new Date(subscription.ends_at).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-xl font-bold text-gray-900">
                  {currentPlan?.price ? `₹${currentPlan.price.toLocaleString()}` : 'Free'}
                  {currentPlan?.price && <span className="text-sm text-gray-500 font-normal">/mo</span>}
                </div>
                {getStatusBadge(subscription?.status || 'free')}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Upgrade Action */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <ArrowRight className="w-4 h-4 text-gray-400" />
              </div>
              <h5 className="font-medium text-gray-900 mb-1">
                {isFreePlan ? 'Upgrade Plan' : 'Upgrade Further'}
              </h5>
              <p className="text-sm text-gray-600 mb-3">
                {isFreePlan 
                  ? 'Unlock advanced features' 
                  : nextPlan 
                    ? `Upgrade to ${nextPlan.display_name}` 
                    : 'You have the highest plan'
                }
              </p>
              {(isFreePlan || nextPlan) && (
                <button
                  onClick={() => {
                    const targetPlan = isFreePlan 
                      ? plans.find(p => p.name === 'pro') 
                      : nextPlan;
                    if (targetPlan) handleUpgrade(targetPlan);
                  }}
                  className="w-full btn btn-primary btn-sm"
                >
                  {isFreePlan ? 'Upgrade to Pro' : 'Upgrade Now'}
                </button>
              )}
            </div>

            {/* Payment History */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <Receipt className="w-5 h-5 text-green-600" />
                <ArrowRight className="w-4 h-4 text-gray-400" />
              </div>
              <h5 className="font-medium text-gray-900 mb-1">Payment History</h5>
              <p className="text-sm text-gray-600 mb-3">
                {recentPayments.length > 0 
                  ? `${recentPayments.length} recent payment${recentPayments.length !== 1 ? 's' : ''}` 
                  : 'No payments yet'
                }
              </p>
              <Link
                to="/dashboard/payments"
                className="w-full btn btn-outline btn-sm"
              >
                View History
              </Link>
            </div>

            {/* Billing Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <DollarSign className="w-5 h-5 text-purple-600" />
                <ArrowRight className="w-4 h-4 text-gray-400" />
              </div>
              <h5 className="font-medium text-gray-900 mb-1">Billing</h5>
              <p className="text-sm text-gray-600 mb-3">
                {subscription?.status === 'active' 
                  ? 'Auto-renewal enabled' 
                  : 'No active billing'
                }
              </p>
              <Link
                to="/dashboard/payments"
                className="w-full btn btn-outline btn-sm"
              >
                Manage Billing
              </Link>
            </div>
          </div>

          {/* Recent Payments */}
          {recentPayments.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Recent Payments</h4>
              <div className="space-y-2">
                {recentPayments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <CreditCard className="w-4 h-4 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {payment.client_subscriptions?.subscription_plans?.display_name || 'Plan Payment'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(payment.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        ₹{payment.amount?.toLocaleString()}
                      </span>
                      {getPaymentStatusBadge(payment.status)}
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-3 text-center">
                <Link
                  to="/dashboard/payments"
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  View all payments →
                </Link>
              </div>
            </div>
          )}

          {/* Free Plan CTA */}
          {isFreePlan && (
            <div className="mt-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold mb-1">Ready to scale your business?</h4>
                  <p className="text-sm text-blue-100">
                    Upgrade to Pro and get 500 calls/month + advanced features
                  </p>
                </div>
                <button
                  onClick={() => {
                    const proPlan = plans.find(p => p.name === 'pro');
                    if (proPlan) handleUpgrade(proPlan);
                  }}
                  className="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center"
                >
                  <Zap className="w-4 h-4 mr-1" />
                  Upgrade Now
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Payment Modals */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        selectedPlan={selectedPlan}
        onSuccess={handlePaymentSuccess}
      />
      
      <PaymentSuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        paymentData={paymentResult}
      />
    </>
  );
};

export default BillingSection;

{"name": "telecalling-admin-panel", "version": "1.0.0", "description": "Admin Panel Server for Telecalling Project", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["admin", "panel", "telecalling", "voicebot", "management"], "author": "Tecnvi AI", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "resend": "^2.1.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}
#!/usr/bin/env node

/**
 * Super Admin Authentication Test Script
 * 
 * This script tests the super admin authentication system
 * Run: node test_super_admin_auth.js
 */

const axios = require('axios');

const ADMIN_PANEL_URL = 'http://localhost:4000';

// Test credentials (simple password authentication)
const SUPER_ADMIN_CREDS = {
  adminId: 'superadmin001',
  password: 'superadmin123'
};

const REGULAR_ADMIN_CREDS = {
  adminId: 'admin001',
  password: 'admin123'
};

console.log('🔐 Testing Simple Password Authentication (No Bcrypt)');

async function testAuthentication() {
  console.log('🧪 Testing Super Admin Authentication System\n');

  try {
    // Test 1: Super Admin Login
    console.log('1️⃣ Testing Super Admin Login...');
    const superAdminLogin = await axios.post(`${ADMIN_PANEL_URL}/api/admin/auth/login`, SUPER_ADMIN_CREDS);
    
    if (superAdminLogin.status === 200) {
      console.log('✅ Super Admin login successful');
      console.log(`   Admin: ${superAdminLogin.data.admin.name}`);
      console.log(`   Role: ${superAdminLogin.data.admin.role || 'Not specified'}`);
      
      const superAdminToken = superAdminLogin.data.tokens.accessToken;
      
      // Test 2: Super Admin Dashboard Access
      console.log('\n2️⃣ Testing Super Admin Dashboard Access...');
      const superAdminDashboard = await axios.get(`${ADMIN_PANEL_URL}/api/admin/super/dashboard`, {
        headers: { Authorization: `Bearer ${superAdminToken}` }
      });
      
      if (superAdminDashboard.status === 200) {
        console.log('✅ Super Admin dashboard access successful');
        console.log(`   Total Admins: ${superAdminDashboard.data.dashboard.totalAdmins}`);
        console.log(`   Total Clients: ${superAdminDashboard.data.dashboard.totalClients}`);
      }
      
      // Test 3: Super Admin - List All Admins
      console.log('\n3️⃣ Testing Super Admin - List All Admins...');
      const adminsList = await axios.get(`${ADMIN_PANEL_URL}/api/admin/super/admins`, {
        headers: { Authorization: `Bearer ${superAdminToken}` }
      });

      if (adminsList.status === 200) {
        console.log('✅ Super Admin can list all admins');
        console.log(`   Found ${adminsList.data.admins.length} admins`);
        adminsList.data.admins.forEach(admin => {
          console.log(`   - ${admin.name} (${admin.adminId}) - ${admin.assignedClientsCount} clients, ${admin.availableSlots} slots available`);
        });
      }

      // Test 3.1: Super Admin - List All Clients with Admin Assignment Info
      console.log('\n3️⃣.1 Testing Super Admin - List All Clients...');
      const clientsList = await axios.get(`${ADMIN_PANEL_URL}/api/admin/super/clients?limit=5`, {
        headers: { Authorization: `Bearer ${superAdminToken}` }
      });

      if (clientsList.status === 200) {
        console.log('✅ Super Admin can list all clients');
        console.log(`   Found ${clientsList.data.clients.length} clients (showing first 5)`);
        clientsList.data.clients.forEach(client => {
          const adminInfo = client.assignedAdmin ?
            `assigned to ${client.assignedAdmin.name} (${client.assignedAdmin.adminId})` :
            'unassigned';
          console.log(`   - ${client.username} (${client.email}) - ${adminInfo}`);
        });
        console.log(`   Summary: ${clientsList.data.summary.assignedClients} assigned, ${clientsList.data.summary.unassignedClients} unassigned`);
      }

      // Test 3.2: Super Admin - Get Unassigned Clients
      console.log('\n3️⃣.2 Testing Super Admin - Get Unassigned Clients...');
      const unassignedClients = await axios.get(`${ADMIN_PANEL_URL}/api/admin/super/clients/unassigned?limit=3`, {
        headers: { Authorization: `Bearer ${superAdminToken}` }
      });

      if (unassignedClients.status === 200) {
        console.log('✅ Super Admin can list unassigned clients');
        console.log(`   Found ${unassignedClients.data.clients.length} unassigned clients (showing first 3)`);
        unassignedClients.data.clients.forEach(client => {
          console.log(`   - ${client.username} (${client.email}) - created ${new Date(client.createdAt).toLocaleDateString()}`);
        });
      }
      
    } else {
      console.log('❌ Super Admin login failed');
    }

    // Test 4: Regular Admin Login
    console.log('\n4️⃣ Testing Regular Admin Login...');
    const regularAdminLogin = await axios.post(`${ADMIN_PANEL_URL}/api/admin/auth/login`, REGULAR_ADMIN_CREDS);
    
    if (regularAdminLogin.status === 200) {
      console.log('✅ Regular Admin login successful');
      console.log(`   Admin: ${regularAdminLogin.data.admin.name}`);
      
      const regularAdminToken = regularAdminLogin.data.tokens.accessToken;
      
      // Test 5: Regular Admin trying to access Super Admin routes (should fail)
      console.log('\n5️⃣ Testing Regular Admin access to Super Admin routes (should fail)...');
      try {
        await axios.get(`${ADMIN_PANEL_URL}/api/admin/super/dashboard`, {
          headers: { Authorization: `Bearer ${regularAdminToken}` }
        });
        console.log('❌ Regular Admin should NOT have access to super admin routes');
      } catch (error) {
        if (error.response && error.response.status === 403) {
          console.log('✅ Regular Admin correctly denied access to super admin routes');
        } else {
          console.log('❌ Unexpected error:', error.message);
        }
      }
      
      // Test 6: Regular Admin Dashboard Access
      console.log('\n6️⃣ Testing Regular Admin Dashboard Access...');
      const regularAdminDashboard = await axios.get(`${ADMIN_PANEL_URL}/api/admin/panel/dashboard`, {
        headers: { Authorization: `Bearer ${regularAdminToken}` }
      });

      if (regularAdminDashboard.status === 200) {
        console.log('✅ Regular Admin dashboard access successful');
        console.log(`   Assigned Clients: ${regularAdminDashboard.data.dashboard.assignedClients}`);
        console.log(`   Available Slots: ${regularAdminDashboard.data.dashboard.availableSlots}`);
        console.log(`   Role: ${regularAdminDashboard.data.dashboard.adminInfo.role}`);
        console.log(`   Is Super Admin: ${regularAdminDashboard.data.dashboard.adminInfo.isSuperAdmin}`);
        console.log(`   Can Manage All Clients: ${regularAdminDashboard.data.dashboard.capabilities.canManageAllClients}`);
        console.log(`   Can Manage Admins: ${regularAdminDashboard.data.dashboard.capabilities.canManageAdmins}`);
      }
      
    } else {
      console.log('❌ Regular Admin login failed');
    }

    console.log('\n🎉 Authentication tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the admin panel server is running:');
      console.log('   cd Telecalling/admin-panel');
      console.log('   npm start');
    }
  }
}

async function testInvalidCredentials() {
  console.log('\n🔒 Testing Invalid Credentials...');
  
  try {
    // Test wrong super admin password
    await axios.post(`${ADMIN_PANEL_URL}/api/admin/auth/login`, {
      adminId: 'superadmin001',
      password: 'wrongpassword'
    });
    console.log('❌ Should have failed with wrong super admin password');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected wrong super admin password');
    }
  }
  
  try {
    // Test wrong regular admin password
    await axios.post(`${ADMIN_PANEL_URL}/api/admin/auth/login`, {
      adminId: 'admin001',
      password: 'wrongpassword'
    });
    console.log('❌ Should have failed with wrong regular admin password');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected wrong regular admin password');
    }
  }
}

// Run tests
async function runAllTests() {
  await testAuthentication();
  await testInvalidCredentials();
  
  console.log('\n📋 Test Summary:');
  console.log('- Super Admin uses simple password authentication');
  console.log('- Regular Admin uses bcrypt password authentication');
  console.log('- Both use JWT tokens for session management');
  console.log('- Role-based access control working correctly');
  console.log('- Super Admin has unlimited access to all clients and admins');
  console.log('- Super Admin can assign admins to clients');
  console.log('- Regular Admin limited to assigned clients only');
}

// Check if axios is available
try {
  require.resolve('axios');
  runAllTests();
} catch (e) {
  console.log('❌ axios not found. Please install it:');
  console.log('npm install axios');
  console.log('\nThen run: node test_super_admin_auth.js');
}

import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('customer_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('customer_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

class CustomerAPI {
  // Authentication
  async register(customerData, registrationToken = null) {
    try {
      const response = await api.post('/customers/register', {
        ...customerData,
        registrationToken
      });
      return response.data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async verifyEmail(email, otp) {
    try {
      const response = await api.post('/customers/verify-email', {
        email,
        otp
      });
      return response.data;
    } catch (error) {
      console.error('Email verification error:', error);
      throw error;
    }
  }

  async requestLoginOTP(email) {
    try {
      const response = await api.post('/customers/request-login-otp', {
        email
      });
      return response.data;
    } catch (error) {
      console.error('Login OTP request error:', error);
      throw error;
    }
  }

  async verifyLogin(email, otp) {
    try {
      const response = await api.post('/customers/verify-login', {
        email,
        otp
      });
      return response.data;
    } catch (error) {
      console.error('Login verification error:', error);
      throw error;
    }
  }

  async requestPasswordReset(email) {
    try {
      const response = await api.post('/customers/request-password-reset', {
        email
      });
      return response.data;
    } catch (error) {
      console.error('Password reset request error:', error);
      throw error;
    }
  }

  async resetPassword(email, otp, newPassword) {
    try {
      const response = await api.post('/customers/reset-password', {
        email,
        otp,
        newPassword
      });
      return response.data;
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  }

  // Profile Management
  async getProfile() {
    try {
      const response = await api.get('/customers/profile');
      return response.data;
    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  }

  async updateProfile(profileData) {
    try {
      const response = await api.put('/customers/profile', profileData);
      return response.data;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  // Orders
  async getOrders(page = 1, limit = 10) {
    try {
      const response = await api.get(`/customers/orders?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Get orders error:', error);
      throw error;
    }
  }

  async getOrderDetails(orderId) {
    try {
      const response = await api.get(`/customers/orders/${orderId}`);
      return response.data;
    } catch (error) {
      console.error('Get order details error:', error);
      throw error;
    }
  }

  // Chats
  async getChats() {
    try {
      const response = await api.get('/customers/chats');
      return response.data;
    } catch (error) {
      console.error('Get chats error:', error);
      throw error;
    }
  }

  async getChatMessages(chatId, page = 1, limit = 50) {
    try {
      const response = await api.get(`/customers/chats/${chatId}/messages?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Get chat messages error:', error);
      throw error;
    }
  }

  async sendMessage(chatId, content, messageType = 'text') {
    try {
      const response = await api.post(`/customers/chats/${chatId}/messages`, {
        content,
        messageType
      });
      return response.data;
    } catch (error) {
      console.error('Send message error:', error);
      throw error;
    }
  }

  async markMessagesAsRead(chatId) {
    try {
      const response = await api.put(`/customers/chats/${chatId}/mark-read`);
      return response.data;
    } catch (error) {
      console.error('Mark messages as read error:', error);
      throw error;
    }
  }

  // Registration Token Validation
  async validateRegistrationToken(token) {
    try {
      const response = await api.get(`/customers/validate-token/${token}`);
      return response.data;
    } catch (error) {
      console.error('Token validation error:', error);
      throw error;
    }
  }

  // Dashboard Data
  async getDashboardData() {
    try {
      const response = await api.get('/customers/dashboard');
      return response.data;
    } catch (error) {
      console.error('Get dashboard data error:', error);
      throw error;
    }
  }
}

const customerAPI = new CustomerAPI();
export default customerAPI;

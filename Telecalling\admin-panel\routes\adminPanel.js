const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { dbHel<PERSON> } = require('../../backend/config/supabase');
const { adminOrSuperAdminAuth } = require('../middleware/superAdminAuth');
const router = express.Router();

// Apply admin or super admin authentication to all routes
router.use(adminOrSuperAdminAuth);

// Get dashboard overview for admin
router.get('/dashboard', async (req, res) => {
  try {
    const adminId = req.admin.id;
    const isSuperAdmin = req.admin.isSuperAdmin;

    let clientsCount, activeNumbers;

    if (isSuperAdmin) {
      // Super admin sees all clients
      clientsCount = await dbHelpers.query('SELECT COUNT(*) as count FROM clients WHERE is_active = true');
      activeNumbers = await dbHelpers.query('SELECT COUNT(*) as count FROM clients WHERE assigned_plivo_number IS NOT NULL AND is_active = true');
    } else {
      // Regular admin sees only assigned clients
      clientsCount = await dbHelpers.query(
        'SELECT COUNT(*) as count FROM admin_client_assignments WHERE admin_id = $1',
        [adminId]
      );

      activeNumbers = await dbHelpers.query(`
        SELECT COUNT(*) as count
        FROM clients c
        JOIN admin_client_assignments aca ON c.id = aca.client_id
        WHERE aca.admin_id = $1 AND c.assigned_plivo_number IS NOT NULL
      `, [adminId]);
    }

    // Get today's activity
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    let todayActivity;

    if (isSuperAdmin) {
      // Super admin sees all activity
      todayActivity = await dbHelpers.query(`
        SELECT
          (SELECT COUNT(*) FROM call_logs WHERE call_timestamp >= $1 AND call_timestamp < $2) as calls,
          (SELECT COUNT(*) FROM sms_logs WHERE sms_timestamp >= $1 AND sms_timestamp < $2) as sms,
          (SELECT COUNT(*) FROM support_tickets WHERE created_at >= $1 AND created_at < $2) as tickets
      `, [today, tomorrow]);
    } else {
      // Regular admin sees only assigned clients' activity
      todayActivity = await dbHelpers.query(`
        SELECT
          (SELECT COUNT(*) FROM call_logs cl
           JOIN admin_client_assignments aca ON cl.client_id = aca.client_id
           WHERE aca.admin_id = $1 AND cl.call_timestamp >= $2 AND cl.call_timestamp < $3) as calls,
          (SELECT COUNT(*) FROM sms_logs sl
           JOIN admin_client_assignments aca ON sl.client_id = aca.client_id
           WHERE aca.admin_id = $1 AND sl.sms_timestamp >= $2 AND sl.sms_timestamp < $3) as sms,
          (SELECT COUNT(*) FROM support_tickets st
           JOIN admin_client_assignments aca ON st.client_id = aca.client_id
           WHERE aca.admin_id = $1 AND st.created_at >= $2 AND st.created_at < $3) as tickets
      `, [adminId, today, tomorrow]);
    }

    res.json({
      message: 'Admin dashboard data retrieved successfully',
      dashboard: {
        assignedClients: parseInt(clientsCount[0].count),
        activeNumbers: parseInt(activeNumbers[0].count),
        availableSlots: isSuperAdmin ? 'Unlimited' : 250 - parseInt(clientsCount[0].count),
        todayActivity: {
          calls: parseInt(todayActivity[0].calls),
          sms: parseInt(todayActivity[0].sms),
          tickets: parseInt(todayActivity[0].tickets)
        },
        adminInfo: {
          role: req.admin.role,
          isSuperAdmin: isSuperAdmin,
          name: req.admin.name,
          email: req.admin.email
        },
        capabilities: {
          canManageAllClients: isSuperAdmin,
          canManageAdmins: isSuperAdmin,
          canAssignNumbers: true,
          hasSlotLimitation: !isSuperAdmin
        }
      }
    });

  } catch (error) {
    console.error('Get admin dashboard error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve dashboard data',
      message: 'An error occurred while retrieving dashboard data'
    });
  }
});

// Get all clients assigned to admin
router.get('/clients', async (req, res) => {
  try {
    const { page = 1, limit = 50, search, status } = req.query;
    const offset = (page - 1) * limit;
    const isSuperAdmin = req.admin.isSuperAdmin;

    let query, params, paramIndex;

    if (isSuperAdmin) {
      // Super admin sees all clients
      query = `
        SELECT c.*, ps.plan_name, ps.call_minutes_used, ps.sms_sent, ps.calls_made,
               ps.call_minutes_limit, ps.sms_limit, ps.calls_limit,
               a.name as assigned_admin_name, a.admin_id as assigned_admin_id
        FROM clients c
        LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
        LEFT JOIN admins a ON aca.admin_id = a.id
        LEFT JOIN plan_subscriptions ps ON c.id = ps.client_id AND ps.is_active = true
        WHERE 1=1
      `;
      params = [];
      paramIndex = 1;
    } else {
      // Regular admin sees only assigned clients
      query = `
        SELECT c.*, ps.plan_name, ps.call_minutes_used, ps.sms_sent, ps.calls_made,
               ps.call_minutes_limit, ps.sms_limit, ps.calls_limit
        FROM clients c
        JOIN admin_client_assignments aca ON c.id = aca.client_id
        LEFT JOIN plan_subscriptions ps ON c.id = ps.client_id AND ps.is_active = true
        WHERE aca.admin_id = $1
      `;
      params = [req.admin.id];
      paramIndex = 2;
    }

    // Add search filter
    if (search) {
      query += ` AND (c.username ILIKE $${paramIndex} OR c.email ILIKE $${paramIndex} OR c.business_name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    // Add status filter
    if (status === 'active') {
      query += ` AND c.is_active = true`;
    } else if (status === 'inactive') {
      query += ` AND c.is_active = false`;
    } else if (status === 'with_number') {
      query += ` AND c.assigned_plivo_number IS NOT NULL`;
    } else if (status === 'without_number') {
      query += ` AND c.assigned_plivo_number IS NULL`;
    }

    query += ` ORDER BY c.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const clients = await dbHelpers.query(query, params);

    // Get total count
    let countQuery, countParams, countParamIndex;

    if (isSuperAdmin) {
      countQuery = `SELECT COUNT(*) as total FROM clients c WHERE 1=1`;
      countParams = [];
      countParamIndex = 1;
    } else {
      countQuery = `
        SELECT COUNT(*) as total
        FROM clients c
        JOIN admin_client_assignments aca ON c.id = aca.client_id
        WHERE aca.admin_id = $1
      `;
      countParams = [req.admin.id];
      countParamIndex = 2;
    }

    if (search) {
      countQuery += ` AND (c.username ILIKE $${countParamIndex} OR c.email ILIKE $${countParamIndex} OR c.business_name ILIKE $${countParamIndex})`;
      countParams.push(`%${search}%`);
      countParamIndex++;
    }

    if (status === 'active') {
      countQuery += ` AND c.is_active = true`;
    } else if (status === 'inactive') {
      countQuery += ` AND c.is_active = false`;
    } else if (status === 'with_number') {
      countQuery += ` AND c.assigned_plivo_number IS NOT NULL`;
    } else if (status === 'without_number') {
      countQuery += ` AND c.assigned_plivo_number IS NULL`;
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Clients retrieved successfully',
      clients: clients.map(client => ({
        id: client.id,
        username: client.username,
        email: client.email,
        businessName: client.business_name,
        businessSummary: client.business_summary,
        ownerName: client.owner_name,
        mobileNumber: client.mobile_number,
        businessEmail: client.business_email,
        whatsappNumber: client.whatsapp_number,
        assignedPlivoNumber: client.assigned_plivo_number,
        isActive: client.is_active,
        createdAt: client.created_at,
        lastLoginAt: client.last_login_at,
        ...(isSuperAdmin && {
          assignedAdmin: {
            name: client.assigned_admin_name,
            adminId: client.assigned_admin_id
          }
        }),
        planInfo: {
          planName: client.plan_name,
          limits: {
            callMinutes: client.call_minutes_limit,
            sms: client.sms_limit,
            calls: client.calls_limit
          },
          usage: {
            callMinutes: client.call_minutes_used || 0,
            sms: client.sms_sent || 0,
            calls: client.calls_made || 0
          }
        }
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get admin clients error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve clients',
      message: 'An error occurred while retrieving clients'
    });
  }
});

// Assign Plivo number to client
router.put('/clients/:clientId/assign-number', async (req, res) => {
  try {
    const { plivoNumber } = req.body;
    const { clientId } = req.params;

    if (!plivoNumber) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Plivo number is required'
      });
    }

    // Validate phone number format
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(plivoNumber.replace(/[\s\-\(\)]/g, ''))) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Invalid phone number format'
      });
    }

    // Check if client is assigned to this admin (or if super admin)
    if (!req.admin.isSuperAdmin) {
      const assignment = await dbHelpers.findOne('admin_client_assignments', {
        admin_id: req.admin.id,
        client_id: clientId
      });

      if (!assignment) {
        return res.status(403).json({
          error: 'Access denied',
          message: 'Client not assigned to this admin'
        });
      }
    }

    // Check if number is already assigned
    const existingAssignment = await dbHelpers.findOne('clients', {
      assigned_plivo_number: plivoNumber
    });

    if (existingAssignment && existingAssignment.id !== clientId) {
      return res.status(409).json({ 
        error: 'Number already assigned',
        message: 'This Plivo number is already assigned to another client'
      });
    }

    // Update client with assigned number
    const updatedClient = await dbHelpers.update('clients', {
      assigned_plivo_number: plivoNumber,
      updated_at: new Date()
    }, { id: clientId });

    res.json({
      message: 'Plivo number assigned successfully',
      client: {
        id: updatedClient.id,
        username: updatedClient.username,
        email: updatedClient.email,
        assignedPlivoNumber: updatedClient.assigned_plivo_number
      }
    });

  } catch (error) {
    console.error('Assign Plivo number error:', error);
    res.status(500).json({ 
      error: 'Failed to assign Plivo number',
      message: 'An error occurred while assigning Plivo number'
    });
  }
});

// Remove Plivo number from client
router.delete('/clients/:clientId/remove-number', async (req, res) => {
  try {
    const { clientId } = req.params;

    // Check if client is assigned to this admin (or if super admin)
    if (!req.admin.isSuperAdmin) {
      const assignment = await dbHelpers.findOne('admin_client_assignments', {
        admin_id: req.admin.id,
        client_id: clientId
      });

      if (!assignment) {
        return res.status(403).json({
          error: 'Access denied',
          message: 'Client not assigned to this admin'
        });
      }
    }

    // Remove assigned number
    const updatedClient = await dbHelpers.update('clients', {
      assigned_plivo_number: null,
      updated_at: new Date()
    }, { id: clientId });

    res.json({
      message: 'Plivo number removed successfully',
      client: {
        id: updatedClient.id,
        username: updatedClient.username,
        email: updatedClient.email,
        assignedPlivoNumber: updatedClient.assigned_plivo_number
      }
    });

  } catch (error) {
    console.error('Remove Plivo number error:', error);
    res.status(500).json({ 
      error: 'Failed to remove Plivo number',
      message: 'An error occurred while removing Plivo number'
    });
  }
});

// Get client details
router.get('/clients/:clientId', async (req, res) => {
  try {
    const { clientId } = req.params;

    // Check if client is assigned to this admin (or if super admin)
    if (!req.admin.isSuperAdmin) {
      const assignment = await dbHelpers.findOne('admin_client_assignments', {
        admin_id: req.admin.id,
        client_id: clientId
      });

      if (!assignment) {
        return res.status(403).json({
          error: 'Access denied',
          message: 'Client not assigned to this admin'
        });
      }
    }

    // Get client details with plan subscription
    const clientData = await dbHelpers.query(`
      SELECT c.*, ps.plan_name, ps.plan_price, ps.call_minutes_limit, ps.sms_limit, ps.calls_limit,
             ps.call_minutes_used, ps.sms_sent, ps.calls_made, ps.start_date, ps.end_date
      FROM clients c
      LEFT JOIN plan_subscriptions ps ON c.id = ps.client_id AND ps.is_active = true
      WHERE c.id = $1
    `, [clientId]);

    if (!clientData.length) {
      return res.status(404).json({
        error: 'Client not found',
        message: 'Client not found'
      });
    }

    const client = clientData[0];

    // Get recent activity
    const recentCalls = await dbHelpers.query(`
      SELECT caller_number, call_timestamp, duration_minutes, call_status
      FROM call_logs
      WHERE client_id = $1
      ORDER BY call_timestamp DESC
      LIMIT 10
    `, [clientId]);

    const recentSms = await dbHelpers.query(`
      SELECT recipient_number, sms_timestamp, status
      FROM sms_logs
      WHERE client_id = $1
      ORDER BY sms_timestamp DESC
      LIMIT 10
    `, [clientId]);

    res.json({
      message: 'Client details retrieved successfully',
      client: {
        id: client.id,
        username: client.username,
        email: client.email,
        businessName: client.business_name,
        businessSummary: client.business_summary,
        businessAddress: client.business_address,
        ownerName: client.owner_name,
        mobileNumber: client.mobile_number,
        businessEmail: client.business_email,
        whatsappNumber: client.whatsapp_number,
        assignedPlivoNumber: client.assigned_plivo_number,
        isActive: client.is_active,
        createdAt: client.created_at,
        lastLoginAt: client.last_login_at,
        planInfo: {
          planName: client.plan_name,
          planPrice: client.plan_price,
          startDate: client.start_date,
          endDate: client.end_date,
          limits: {
            callMinutes: client.call_minutes_limit,
            sms: client.sms_limit,
            calls: client.calls_limit
          },
          usage: {
            callMinutes: client.call_minutes_used || 0,
            sms: client.sms_sent || 0,
            calls: client.calls_made || 0
          }
        },
        recentActivity: {
          calls: recentCalls,
          sms: recentSms
        }
      }
    });

  } catch (error) {
    console.error('Get client details error:', error);
    res.status(500).json({
      error: 'Failed to retrieve client details',
      message: 'An error occurred while retrieving client details'
    });
  }
});

module.exports = router;

import api from './api';

class SubscriptionAPI {
  // Get all subscription plans
  async getPlans() {
    try {
      const response = await api.get('/subscriptions/plans');
      return response.data;
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      throw error;
    }
  }

  // Get current user subscription
  async getCurrentSubscription() {
    try {
      const response = await api.get('/subscriptions/current');
      return response.data;
    } catch (error) {
      console.error('Error fetching current subscription:', error);
      throw error;
    }
  }

  // Create payment order
  async createOrder(planId, billingPeriod = 'monthly') {
    try {
      console.log('createOrder called with:', { planId, billingPeriod });
      const response = await api.post('/subscriptions/create-order', {
        planId,
        billingPeriod
      });
      console.log('createOrder response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating payment order:', error);
      console.error('Error details:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error message:', error.message);

      // Return a more user-friendly error
      const errorMessage = error.response?.data?.message || error.response?.data?.error || error.message;

      // Show specific error messages based on status
      if (error.response?.status === 500) {
        throw new Error('Payment system is currently unavailable. Please try again later or contact support.');
      } else if (error.response?.status === 404) {
        throw new Error('Selected plan not found. Please refresh the page and try again.');
      } else {
        throw new Error(`Payment order creation failed: ${errorMessage}`);
      }
    }
  }

  // Verify payment
  async verifyPayment(paymentData) {
    try {
      const response = await api.post('/subscriptions/verify-payment', paymentData);
      return response.data;
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw error;
    }
  }

  // Get payment history
  async getPaymentHistory(page = 1, limit = 10) {
    try {
      const response = await api.get(`/subscriptions/payments?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  }

  // Cancel subscription (dev mode only)
  async cancelSubscription(subscriptionId) {
    try {
      const response = await api.post(`/subscriptions/cancel/${subscriptionId}`);
      return response.data;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw error;
    }
  }

  // Load Razorpay script
  loadRazorpayScript() {
    return new Promise((resolve) => {
      const existingScript = document.getElementById('razorpay-script');
      
      if (existingScript) {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.id = 'razorpay-script';
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      
      document.body.appendChild(script);
    });
  }

  // Open Razorpay payment modal
  async openPaymentModal(orderData, onSuccess, onFailure) {
    try {
      console.log('openPaymentModal called with:', orderData);
      console.log('Razorpay key:', process.env.REACT_APP_RAZORPAY_KEY_ID);

      const scriptLoaded = await this.loadRazorpayScript();

      if (!scriptLoaded) {
        throw new Error('Failed to load Razorpay script');
      }

      const options = {
        key: process.env.REACT_APP_RAZORPAY_KEY_ID || 'rzp_test_dummy_key',
        amount: orderData.order.amount,
        currency: orderData.order.currency,
        name: 'VoiceBot',
        description: `${orderData.plan.name} Subscription`,
        order_id: orderData.order.id,
        handler: async (response) => {
          try {
            const verificationData = {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              plan_id: orderData.plan.id,
              billing_period: orderData.billingPeriod || 'monthly'
            };

            const result = await this.verifyPayment(verificationData);
            
            if (result.success) {
              onSuccess(result);
            } else {
              onFailure(new Error(result.error || 'Payment verification failed'));
            }
          } catch (error) {
            onFailure(error);
          }
        },
        prefill: {
          name: orderData.user?.name || '',
          email: orderData.user?.email || '',
          contact: orderData.user?.phone || ''
        },
        theme: {
          color: '#3B82F6'
        },
        modal: {
          ondismiss: () => {
            onFailure(new Error('Payment cancelled by user'));
          }
        }
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error) {
      onFailure(error);
    }
  }
}

export default new SubscriptionAPI();

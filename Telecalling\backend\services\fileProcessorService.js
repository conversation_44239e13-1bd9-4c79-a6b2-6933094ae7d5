const fs = require('fs');
const path = require('path');
const xlsx = require('xlsx');
const csv = require('csv-parser');
const mammoth = require('mammoth');
const pdfParse = require('pdf-parse');
const openaiService = require('./openaiService');

class FileProcessorService {
  constructor() {
    this.supportedTypes = {
      images: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
      documents: ['.pdf', '.doc', '.docx'],
      spreadsheets: ['.xlsx', '.xls', '.csv'],
      text: ['.txt']
    };
  }

  /**
   * Process uploaded file and extract product data
   */
  async processFile(filePath, originalName) {
    try {
      console.log('📁 Processing file:', originalName);
      
      const ext = path.extname(originalName).toLowerCase();
      const fileType = this.getFileType(ext);

      if (!fileType) {
        throw new Error(`Unsupported file type: ${ext}`);
      }

      let products = [];

      switch (fileType) {
        case 'image':
          products = await this.processImage(filePath);
          break;
        case 'csv':
          products = await this.processCSV(filePath);
          break;
        case 'excel':
          products = await this.processExcel(filePath);
          break;
        case 'word':
          products = await this.processWord(filePath);
          break;
        case 'pdf':
          products = await this.processPDF(filePath);
          break;
        case 'text':
          products = await this.processText(filePath);
          break;
        default:
          throw new Error(`Unsupported file type: ${fileType}`);
      }

      console.log(`✅ Extracted ${products.length} products from ${originalName}`);
      return products;

    } catch (error) {
      console.error('❌ Error processing file:', error);
      throw error;
    }
  }

  /**
   * Process image file using OpenAI Vision
   */
  async processImage(filePath) {
    return await openaiService.extractProductDataFromImage(filePath);
  }

  /**
   * Process CSV file
   */
  async processCSV(filePath) {
    return new Promise((resolve, reject) => {
      const products = [];
      const csvData = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          csvData.push(row);
        })
        .on('end', async () => {
          try {
            // Convert CSV data to text for OpenAI processing
            const textContent = this.csvToText(csvData);
            const extractedProducts = await openaiService.extractProductDataFromText(textContent, 'CSV');
            resolve(extractedProducts);
          } catch (error) {
            reject(error);
          }
        })
        .on('error', reject);
    });
  }

  /**
   * Process Excel file
   */
  async processExcel(filePath) {
    try {
      const workbook = xlsx.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = xlsx.utils.sheet_to_json(worksheet);

      // Convert Excel data to text for OpenAI processing
      const textContent = this.excelToText(jsonData);
      return await openaiService.extractProductDataFromText(textContent, 'Excel');
    } catch (error) {
      throw new Error(`Error processing Excel file: ${error.message}`);
    }
  }

  /**
   * Process Word document
   */
  async processWord(filePath) {
    try {
      const result = await mammoth.extractRawText({ path: filePath });
      const textContent = result.value;
      return await openaiService.extractProductDataFromText(textContent, 'Word document');
    } catch (error) {
      throw new Error(`Error processing Word document: ${error.message}`);
    }
  }

  /**
   * Process PDF file
   */
  async processPDF(filePath) {
    try {
      const dataBuffer = fs.readFileSync(filePath);
      const pdfData = await pdfParse(dataBuffer);
      const textContent = pdfData.text;
      return await openaiService.extractProductDataFromText(textContent, 'PDF');
    } catch (error) {
      throw new Error(`Error processing PDF: ${error.message}`);
    }
  }

  /**
   * Process text file
   */
  async processText(filePath) {
    try {
      const textContent = fs.readFileSync(filePath, 'utf8');
      return await openaiService.extractProductDataFromText(textContent, 'text file');
    } catch (error) {
      throw new Error(`Error processing text file: ${error.message}`);
    }
  }

  /**
   * Convert CSV data to readable text with emphasis on key-value pairs
   */
  csvToText(csvData) {
    let text = 'Product Data from CSV - Extract ALL columns as key-value pairs:\n\n';

    // Show column headers first
    if (csvData.length > 0) {
      const headers = Object.keys(csvData[0]);
      text += `Available Columns: ${headers.join(', ')}\n\n`;
    }

    csvData.forEach((row, index) => {
      text += `Product ${index + 1}:\n`;
      Object.entries(row).forEach(([key, value]) => {
        if (value && value.toString().trim()) {
          text += `${key}: ${value}\n`;
        }
      });
      text += '\n';
    });

    text += '\nIMPORTANT: Extract each column as a separate key-value pair in the features object (EXCEPT the product name column). If there are 4 feature columns, create 4 key-value pairs. If there are 10 feature columns, create 10 key-value pairs. The product name goes in the "name" field, not in features.\n';

    return text;
  }

  /**
   * Convert Excel data to readable text with emphasis on key-value pairs
   */
  excelToText(jsonData) {
    let text = 'Product Data from Excel - Extract ALL columns as key-value pairs:\n\n';

    // Show column headers first
    if (jsonData.length > 0) {
      const headers = Object.keys(jsonData[0]);
      text += `Available Columns: ${headers.join(', ')}\n\n`;
    }

    jsonData.forEach((row, index) => {
      text += `Product ${index + 1}:\n`;
      Object.entries(row).forEach(([key, value]) => {
        if (value && value.toString().trim()) {
          text += `${key}: ${value}\n`;
        }
      });
      text += '\n';
    });

    text += '\nIMPORTANT: Extract each column as a separate key-value pair in the features object (EXCEPT the product name column). If the Excel has 4 feature columns, create 4 key-value pairs. If it has 10 feature columns, create 10 key-value pairs. Use the exact column names as keys. The product name goes in the "name" field, not in features.\n';

    return text;
  }

  /**
   * Get file type category
   */
  getFileType(ext) {
    if (this.supportedTypes.images.includes(ext)) return 'image';
    if (this.supportedTypes.documents.includes(ext)) {
      if (ext === '.pdf') return 'pdf';
      return 'word';
    }
    if (this.supportedTypes.spreadsheets.includes(ext)) {
      if (ext === '.csv') return 'csv';
      return 'excel';
    }
    if (this.supportedTypes.text.includes(ext)) return 'text';
    return null;
  }

  /**
   * Check if file type is supported
   */
  isSupported(filename) {
    const ext = path.extname(filename).toLowerCase();
    return this.getFileType(ext) !== null;
  }

  /**
   * Get supported file types for frontend
   */
  getSupportedTypes() {
    return {
      images: this.supportedTypes.images,
      documents: this.supportedTypes.documents,
      spreadsheets: this.supportedTypes.spreadsheets,
      text: this.supportedTypes.text,
      all: [
        ...this.supportedTypes.images,
        ...this.supportedTypes.documents,
        ...this.supportedTypes.spreadsheets,
        ...this.supportedTypes.text
      ]
    };
  }
}

module.exports = new FileProcessorService();

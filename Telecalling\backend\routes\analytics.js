const express = require('express');
const { dbHelpers, supabaseAdmin } = require('../config/supabase');
const { auth } = require('../middleware/auth');
const router = express.Router();

// Get call logs for client
router.get('/call-logs', auth, async (req, res) => {
  try {
    const { page = 1, limit = 50, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT * FROM call_logs 
      WHERE client_id = $1
    `;
    let params = [req.user.id];
    let paramIndex = 2;

    // Add date filters if provided
    if (startDate) {
      query += ` AND call_timestamp >= $${paramIndex}`;
      params.push(new Date(startDate));
      paramIndex++;
    }

    if (endDate) {
      query += ` AND call_timestamp <= $${paramIndex}`;
      params.push(new Date(endDate));
      paramIndex++;
    }

    query += ` ORDER BY call_timestamp DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const callLogs = await dbHelpers.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total FROM call_logs 
      WHERE client_id = $1
    `;
    let countParams = [req.user.id];
    let countParamIndex = 2;

    if (startDate) {
      countQuery += ` AND call_timestamp >= $${countParamIndex}`;
      countParams.push(new Date(startDate));
      countParamIndex++;
    }

    if (endDate) {
      countQuery += ` AND call_timestamp <= $${countParamIndex}`;
      countParams.push(new Date(endDate));
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Call logs retrieved successfully',
      callLogs: callLogs.map(log => ({
        id: log.id,
        callerNumber: log.caller_number,
        callTimestamp: log.call_timestamp,
        durationMinutes: log.duration_minutes,
        botConversationSummary: log.bot_conversation_summary,
        callStatus: log.call_status,
        createdAt: log.created_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get call logs error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve call logs',
      message: 'An error occurred while retrieving call logs'
    });
  }
});

// Get SMS logs for client
router.get('/sms-logs', auth, async (req, res) => {
  try {
    const { page = 1, limit = 50, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT
        sl.*,
        COALESCE(
          json_agg(
            json_build_object(
              'id', sps.id,
              'file_name', sps.file_name,
              'file_size', sps.file_size,
              'storage_url', sps.storage_url,
              'upload_timestamp', sps.upload_timestamp
            )
          ) FILTER (WHERE sps.id IS NOT NULL),
          '[]'::json
        ) as pdf_files
      FROM sms_logs sl
      LEFT JOIN sms_pdf_storage sps ON sl.id = sps.sms_log_id AND sps.is_active = TRUE
      WHERE sl.client_id = $1
    `;
    let params = [req.user.id];
    let paramIndex = 2;

    // Add date filters if provided
    if (startDate) {
      query += ` AND sms_timestamp >= $${paramIndex}`;
      params.push(new Date(startDate));
      paramIndex++;
    }

    if (endDate) {
      query += ` AND sms_timestamp <= $${paramIndex}`;
      params.push(new Date(endDate));
      paramIndex++;
    }

    query += ` GROUP BY sl.id, sl.client_id, sl.recipient_number, sl.message_content, sl.message_summary,
               sl.file_attachments, sl.sms_timestamp, sl.status, sl.created_at,
               sl.pdf_attachments, sl.pdf_storage_urls, sl.total_pdf_attachments
               ORDER BY sl.sms_timestamp DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const smsLogs = await dbHelpers.query(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(DISTINCT sl.id) as total FROM sms_logs sl
      LEFT JOIN sms_pdf_storage sps ON sl.id = sps.sms_log_id AND sps.is_active = TRUE
      WHERE sl.client_id = $1
    `;
    let countParams = [req.user.id];
    let countParamIndex = 2;

    if (startDate) {
      countQuery += ` AND sms_timestamp >= $${countParamIndex}`;
      countParams.push(new Date(startDate));
      countParamIndex++;
    }

    if (endDate) {
      countQuery += ` AND sms_timestamp <= $${countParamIndex}`;
      countParams.push(new Date(endDate));
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'SMS logs retrieved successfully',
      smsLogs: smsLogs.map(log => ({
        id: log.id,
        recipientNumber: log.recipient_number,
        messageContent: log.message_content,
        messageSummary: log.message_summary,
        fileAttachments: log.file_attachments,
        pdfFiles: log.pdf_files || [],
        totalPdfAttachments: log.total_pdf_attachments || 0,
        pdfStorageUrls: log.pdf_storage_urls || [],
        smsTimestamp: log.sms_timestamp,
        status: log.status,
        createdAt: log.created_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get SMS logs error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve SMS logs',
      message: 'An error occurred while retrieving SMS logs'
    });
  }
});

// Get usage statistics for client
router.get('/usage-stats', auth, async (req, res) => {
  try {
    // Get current plan subscription
    const planSubscription = await dbHelpers.findOne('plan_subscriptions', { 
      client_id: req.user.id,
      is_active: true
    });

    if (!planSubscription) {
      return res.status(404).json({ 
        error: 'Plan not found',
        message: 'No active plan subscription found'
      });
    }

    // Get call statistics for current month
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const callStats = await dbHelpers.query(`
      SELECT 
        COUNT(*) as total_calls,
        COALESCE(SUM(duration_minutes), 0) as total_minutes
      FROM call_logs 
      WHERE client_id = $1 AND call_timestamp >= $2
    `, [req.user.id, currentMonth]);

    const smsStats = await dbHelpers.query(`
      SELECT COUNT(*) as total_sms
      FROM sms_logs 
      WHERE client_id = $1 AND sms_timestamp >= $2
    `, [req.user.id, currentMonth]);

    // Get daily usage for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyCallStats = await dbHelpers.query(`
      SELECT 
        DATE(call_timestamp) as date,
        COUNT(*) as calls,
        COALESCE(SUM(duration_minutes), 0) as minutes
      FROM call_logs 
      WHERE client_id = $1 AND call_timestamp >= $2
      GROUP BY DATE(call_timestamp)
      ORDER BY date DESC
    `, [req.user.id, thirtyDaysAgo]);

    const dailySmsStats = await dbHelpers.query(`
      SELECT 
        DATE(sms_timestamp) as date,
        COUNT(*) as sms_count
      FROM sms_logs 
      WHERE client_id = $1 AND sms_timestamp >= $2
      GROUP BY DATE(sms_timestamp)
      ORDER BY date DESC
    `, [req.user.id, thirtyDaysAgo]);

    res.json({
      message: 'Usage statistics retrieved successfully',
      planSubscription: {
        planName: planSubscription.plan_name,
        planPrice: planSubscription.plan_price,
        startDate: planSubscription.start_date,
        endDate: planSubscription.end_date,
        limits: {
          callMinutes: planSubscription.call_minutes_limit,
          sms: planSubscription.sms_limit,
          calls: planSubscription.calls_limit
        },
        usage: {
          callMinutes: planSubscription.call_minutes_used,
          sms: planSubscription.sms_sent,
          calls: planSubscription.calls_made
        }
      },
      currentMonthStats: {
        totalCalls: parseInt(callStats[0].total_calls),
        totalMinutes: parseInt(callStats[0].total_minutes),
        totalSms: parseInt(smsStats[0].total_sms)
      },
      dailyStats: {
        calls: dailyCallStats,
        sms: dailySmsStats
      }
    });

  } catch (error) {
    console.error('Get usage stats error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve usage statistics',
      message: 'An error occurred while retrieving usage statistics'
    });
  }
});

// Get dashboard overview
router.get('/dashboard-overview', auth, async (req, res) => {
  try {
    // Get current subscription with plan details
    const { data: subscription, error: subError } = await supabaseAdmin
      .from('client_subscriptions')
      .select(`
        *,
        subscription_plans (
          id,
          name,
          display_name,
          price,
          features,
          max_calls,
          max_products
        )
      `)
      .eq('client_id', req.user.id)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    // Check if subscription has expired
    let currentSubscription = null;
    if (subscription && !subError) {
      const now = new Date();
      const endsAt = new Date(subscription.ends_at);

      if (endsAt > now) {
        currentSubscription = subscription;
      } else {
        // Mark subscription as expired
        await supabaseAdmin
          .from('client_subscriptions')
          .update({ status: 'expired' })
          .eq('id', subscription.id);
      }
    }

    // Get total products count
    const { count: totalProducts } = await supabaseAdmin
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', req.user.id)
      .eq('is_active', true);

    // Get today's stats
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayStats = await dbHelpers.query(`
      SELECT
        (SELECT COUNT(*) FROM call_logs WHERE client_id = $1 AND call_timestamp >= $2 AND call_timestamp < $3) as today_calls,
        (SELECT COALESCE(SUM(duration_minutes), 0) FROM call_logs WHERE client_id = $1 AND call_timestamp >= $2 AND call_timestamp < $3) as today_minutes,
        (SELECT COUNT(*) FROM sms_logs WHERE client_id = $1 AND sms_timestamp >= $2 AND sms_timestamp < $3) as today_sms
    `, [req.user.id, today, tomorrow]);

    // Get this week's stats
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);

    const weekStats = await dbHelpers.query(`
      SELECT
        (SELECT COUNT(*) FROM call_logs WHERE client_id = $1 AND call_timestamp >= $2) as week_calls,
        (SELECT COALESCE(SUM(duration_minutes), 0) FROM call_logs WHERE client_id = $1 AND call_timestamp >= $2) as week_minutes,
        (SELECT COUNT(*) FROM sms_logs WHERE client_id = $1 AND sms_timestamp >= $2) as week_sms
    `, [req.user.id, weekStart]);

    // Get total counts for overview cards
    const { count: totalCalls } = await supabaseAdmin
      .from('call_logs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', req.user.id);

    const { count: totalSms } = await supabaseAdmin
      .from('sms_logs')
      .select('*', { count: 'exact', head: true })
      .eq('client_id', req.user.id);

    // Get usage data from client_usage_tracking if available
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const { data: usageData } = await supabaseAdmin
      .from('client_usage_tracking')
      .select('*')
      .eq('client_id', req.user.id)
      .eq('usage_month', currentMonth.toISOString().split('T')[0])
      .single();

    res.json({
      message: 'Dashboard overview retrieved successfully',
      planInfo: currentSubscription ? {
        planName: currentSubscription.subscription_plans.display_name,
        planPrice: currentSubscription.subscription_plans.price,
        planId: currentSubscription.subscription_plans.id,
        status: currentSubscription.status,
        endsAt: currentSubscription.ends_at,
        usage: {
          callMinutes: {
            used: usageData?.call_minutes_used || 0,
            limit: currentSubscription.subscription_plans.features?.call_minutes_limit || 0,
            percentage: usageData && currentSubscription.subscription_plans.features?.call_minutes_limit
              ? Math.round((usageData.call_minutes_used / currentSubscription.subscription_plans.features.call_minutes_limit) * 100)
              : 0
          },
          sms: {
            used: usageData?.sms_count_used || 0,
            limit: currentSubscription.subscription_plans.features?.sms_limit || 0,
            percentage: usageData && currentSubscription.subscription_plans.features?.sms_limit
              ? Math.round((usageData.sms_count_used / currentSubscription.subscription_plans.features.sms_limit) * 100)
              : 0
          },
          calls: {
            used: totalCalls || 0,
            limit: currentSubscription.subscription_plans.max_calls || 0,
            percentage: totalCalls && currentSubscription.subscription_plans.max_calls
              ? Math.round((totalCalls / currentSubscription.subscription_plans.max_calls) * 100)
              : 0
          }
        }
      } : null,
      todayStats: {
        calls: parseInt(todayStats[0]?.today_calls || 0),
        minutes: parseInt(todayStats[0]?.today_minutes || 0),
        sms: parseInt(todayStats[0]?.today_sms || 0)
      },
      weekStats: {
        calls: parseInt(weekStats[0]?.week_calls || 0),
        minutes: parseInt(weekStats[0]?.week_minutes || 0),
        sms: parseInt(weekStats[0]?.week_sms || 0)
      },
      totalStats: {
        products: totalProducts || 0,
        calls: totalCalls || 0,
        sms: totalSms || 0
      }
    });

  } catch (error) {
    console.error('Get dashboard overview error:', error);
    res.status(500).json({
      error: 'Failed to retrieve dashboard overview',
      message: 'An error occurred while retrieving dashboard overview'
    });
  }
});

module.exports = router;

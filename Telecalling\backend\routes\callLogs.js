const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireClient } = require('../middleware/auth');

const router = express.Router();

// Get all call logs for a client
router.get('/', requireClient, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, startDate, endDate, callerNumber } = req.query;
  const offset = (page - 1) * limit;

  // Get client ID (req.user.id is already the client ID)
  const clientId = req.user.id;

  // Verify client exists
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('id', clientId)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  let query = supabaseAdmin
    .from('call_logs')
    .select('*')
    .eq('client_id', clientId)
    .order('call_time', { ascending: false })
    .range(offset, offset + limit - 1);

  if (startDate) {
    query = query.gte('call_time', new Date(startDate).toISOString());
  }

  if (endDate) {
    query = query.lte('call_time', new Date(endDate).toISOString());
  }

  if (callerNumber) {
    query = query.eq('caller_number', callerNumber);
  }

  const { data: callLogs, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('call_logs')
    .select('*', { count: 'exact', head: true })
    .eq('client_id', clientId);

  if (startDate) {
    countQuery = countQuery.gte('call_time', new Date(startDate).toISOString());
  }

  if (endDate) {
    countQuery = countQuery.lte('call_time', new Date(endDate).toISOString());
  }

  if (callerNumber) {
    countQuery = countQuery.eq('caller_number', callerNumber);
  }

  const { count } = await countQuery;

  res.json({
    callLogs: callLogs || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Get single call log
router.get('/:callId', requireClient, asyncHandler(async (req, res) => {
  const { callId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: callLog, error } = await supabaseAdmin
    .from('call_logs')
    .select('*')
    .eq('id', callId)
    .eq('client_id', clientId)
    .single();

  if (error) {
    throw error;
  }

  if (!callLog) {
    throw new AppError('Call log not found', 404);
  }

  res.json({
    callLog
  });
}));

// Create call log (used by Plivo webhook)
router.post('/', asyncHandler(async (req, res) => {
  const {
    clientId,
    callerNumber,
    duration,
    summary,
    transcriptUrl,
    wasEstimateSent = false,
    wasPaymentSent = false,
    callStatus = 'completed'
  } = req.body;

  if (!clientId || !callerNumber) {
    throw new AppError('Client ID and caller number are required', 400);
  }

  const { data: callLog, error } = await supabaseAdmin
    .from('call_logs')
    .insert({
      id: uuidv4(),
      client_id: clientId,
      caller_number: callerNumber,
      call_time: new Date().toISOString(),
      duration: parseInt(duration) || 0,
      summary: summary || '',
      transcript_url: transcriptUrl || null,
      was_estimate_sent: wasEstimateSent,
      was_payment_sent: wasPaymentSent,
      call_status: callStatus
    })
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.status(201).json({
    message: 'Call log created successfully',
    callLog
  });
}));

// Update call log
router.put('/:callId', requireClient, asyncHandler(async (req, res) => {
  const { callId } = req.params;
  const {
    summary,
    transcriptUrl,
    wasEstimateSent,
    wasPaymentSent,
    callStatus
  } = req.body;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const updateData = {};
  if (summary !== undefined) updateData.summary = summary;
  if (transcriptUrl !== undefined) updateData.transcript_url = transcriptUrl;
  if (wasEstimateSent !== undefined) updateData.was_estimate_sent = wasEstimateSent;
  if (wasPaymentSent !== undefined) updateData.was_payment_sent = wasPaymentSent;
  if (callStatus !== undefined) updateData.call_status = callStatus;

  const { data: updatedCallLog, error } = await supabaseAdmin
    .from('call_logs')
    .update(updateData)
    .eq('id', callId)
    .eq('client_id', clientId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!updatedCallLog) {
    throw new AppError('Call log not found', 404);
  }

  res.json({
    message: 'Call log updated successfully',
    callLog: updatedCallLog
  });
}));

// Delete call log
router.delete('/:callId', requireClient, asyncHandler(async (req, res) => {
  const { callId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: deletedCallLog, error } = await supabaseAdmin
    .from('call_logs')
    .delete()
    .eq('id', callId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!deletedCallLog) {
    throw new AppError('Call log not found', 404);
  }

  res.json({
    message: 'Call log deleted successfully'
  });
}));

// Get call statistics
router.get('/stats/overview', requireClient, asyncHandler(async (req, res) => {
  const { period = '7d' } = req.query;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case '24h':
      startDate.setHours(startDate.getHours() - 24);
      break;
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(startDate.getDate() - 90);
      break;
    default:
      startDate.setDate(startDate.getDate() - 7);
  }

  // Get call logs in the period
  const { data: callLogs, error } = await supabaseAdmin
    .from('call_logs')
    .select('*')
    .eq('client_id', client.id)
    .gte('call_time', startDate.toISOString())
    .lte('call_time', endDate.toISOString());

  if (error) {
    throw error;
  }

  const logs = callLogs || [];

  // Calculate statistics
  const totalCalls = logs.length;
  const uniqueCallers = new Set(logs.map(log => log.caller_number)).size;
  const totalDuration = logs.reduce((sum, log) => sum + (log.duration || 0), 0);
  const averageDuration = totalCalls > 0 ? totalDuration / totalCalls : 0;
  const estimatesSent = logs.filter(log => log.was_estimate_sent).length;
  const paymentsSent = logs.filter(log => log.was_payment_sent).length;

  // Group by date for chart data
  const callsByDate = {};
  logs.forEach(log => {
    const date = new Date(log.call_time).toISOString().split('T')[0];
    callsByDate[date] = (callsByDate[date] || 0) + 1;
  });

  res.json({
    stats: {
      totalCalls,
      uniqueCallers,
      totalDuration,
      averageDuration: Math.round(averageDuration),
      estimatesSent,
      paymentsSent,
      callsByDate
    }
  });
}));

module.exports = router; 
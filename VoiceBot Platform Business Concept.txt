VoiceBot Platform: Business Concept & Solution Documentation

Executive Summary
We are building a VoiceBot + WhatsApp Automation Platform for small and medium businesses (SMBs) that enables them to instantly set up their own smart voice assistant to handle customer inquiries, send quotes, take appointments, and complete transactions — all using a dedicated mobile number, with no technical skills required.

This platform brings enterprise-grade automation tools to local businesses like Kirana stores, salons, clinics, shops, and vendors, empowering them to digitize customer engagement with voice and messaging.


Problem We’re Solving
Most SMBs in India still rely on manual phone calls and WhatsApp to handle customer queries. This results in:
Missed customer calls


No tracking of client conversations


Manual handling of product queries and price negotiations


No appointment booking system


No automated estimates or payments


Inconsistent communication and poor customer service


They also lack technical expertise and the budget to build automation systems.

Our Solution
We offer each business a dedicated smart voice assistant reachable via a mobile number. This assistant is capable of:
Answering customer calls 24x7 using AI


Handling product-related inquiries using dynamic product catalogs


Providing business summaries and service information


Booking appointments using built-in or Google Calendar sync


Sending WhatsApp messages post-call (links, invoices, payment)


Generating and sending automated estimates/quotations


Collecting feedback and storing call summaries


Providing a dashboard for business owners to manage it all



🔧 How It Works (User Workflow
1. Signup & Onboarding
A business owner visits our website and signs up


They fill in their business name, contact details, and product/service catalog in a standard form


They choose bot features they want (appointments, quotes, payment, etc.)


2. Number Assignment & Bot Activation
Our backend team receives the onboarding request


From the admin panel, we assign a mobile number (via Plivo) to the business


We link the business profile to that number and activate the voice bot


3. Customer Calls the Bot
When any customer calls that number:


The bot dynamically loads the business’s data


Responds with product info, prices, availability, etc.


Can book appointments, send payment links, or quotations as needed


4. Post-Call Engagement
The bot sends relevant follow-up WhatsApp messages (estimate, invoice, confirmation)


Conversation summaries are logged and stored for the business owner to view


5. Client Dashboard
Businesses can:


View call logs and transcripts


Update products and services


Change bot features


Access appointment calendar


View estimates and invoices sent



 Target Users
Small & Medium Businesses (SMBs)


Retail stores & showrooms


Doctors, clinics, salons


Tuition classes and coaching centers


Service professionals (plumbers, electricians)


Local food joints & caterers



Core Features
Feature
Description
Voice Bot
AI assistant answers customer queries
Product Catalog Bot
Responds to calls with real-time product/service data
Business Summary
Gives intro about the business on call
Appointment Booking
Integrated appointment system with slot selection
WhatsApp Messaging
Sends receipts, links, invoices post-call
Estimate Generator
Auto-generates quotations based on customer needs
Payment Link Sender
Sends secure payment link after discussion
Dashboard
Track all call logs, appointments, WhatsApp logs, invoices
Feature Upgrades
Customers can add/remove features anytime from dashboard


Security & Access
Secure login and account management via Supabase Auth


Role-based access:


Client Role – For business owners


Admin Role – For internal ops team


All data is scoped to each client


Payment/WhatsApp integration with encryption & API tokens




Revenue Model
Monthly Subscription Plans
Plan
Price
Features
Basic
₹499/month
Voice bot + WhatsApp message
Pro
₹1499/month
+ Appointment booking + Estimates
Business
₹2999/month
+ Invoicing + Payment Links + Dashboard
Enterprise
₹4999/month
Custom integrations + CRM sync + Analytics

Add-ons:
Extra numbers


Extra minutes (call handling volume)


Custom bot flow design


Custom language support (e.g., Marathi, Hindi)



 Integrations
Plivo – Voice call handling, number management


Supabase – Auth, DB, file storage


WhatsApp Business Cloud API – Post-call messaging


Google Calendar API – Optional calendar sync for appointments


Razorpay/Stripe – Payment links



 Tech Stack
Layer
Tech
Frontend
React 




Backend
Node.js + Express
Database
Supabase (PostgreSQL + Auth)
Hosting
Render / Railway / Vercel
Telephony
Plivo WebSockets
Messaging
WhatsApp Business API






1. Landing Page (Public)
Route: /
Sections:
Hero Banner with tagline ("Your Personal VoiceBot for Business")


Features (Call handling, WhatsApp automation, Estimates, Appointments, etc.)


Demo Video or Call Demo CTA


Pricing Cards with CTA buttons


Testimonials / Case Studies


Footer (About, Contact, Terms, Privacy, etc.)


CTAs:
[Sign Up] → /signup


[Log In] → /login


[Request Demo] → /demo



 2. Sign Up Page
Route: /signup
Fields:
Email


Full Name


Password


Confirm Password


Checkbox for agreeing to terms



 3. Login Page
Route: /login
Fields:
Email


Password


Forgot Password


[Log In with Google] (optional)



4. User Onboarding Flow
Route: /onboarding (multi-step wizard)
Step 1: Basic Info
Shop Name


Business Type


WhatsApp Number


Region (City/State)


Step 2: Product/Service Setup
Add Product (Name, Price, Category)


Upload CSV (Optional)


Edit / Delete Option


Step 3: Bot Configuration
Enable/disable features:


Appointment


Estimate


WhatsApp messaging


Payments


Business Summary (text area)


Step 4: Final Review & Submit

5. Client Dashboard
Route: /dashboard (after login)
Sidebar Navigation:
🏠 Overview


📞 Call Logs


📦 Product Catalog


📅 Appointments


Estimates & Invoices


WhatsApp Logs


Bot Features


Support


 Upgrade/Payment



5.1 Overview Page
Total Calls This Week


Unique Callers


Calls Missed


WhatsApp Follow-ups Sent


Upcoming Appointments



5.2 Call Logs Page
/dashboard/call-logs
Date, Caller Number, Duration


Transcript / Summary (text)


Buttons: [Send WhatsApp], [Send Estimate], [Send Payment]



5.3 Product Catalog Page
/dashboard/products
List of Products/Services


Add/Edit/Delete Product


CSV Upload/Export



5.4 Appointments Page
/dashboard/appointments
Calendar View


Available Slots Table


Booked By, Status


Manual Add/Edit Slots



5.5 WhatsApp Logs Page
/dashboard/whatsapp-logs
Sent Message Logs


Timestamps, Status (Delivered/Read)


Type (Payment, Estimate, Reminder)



5.6 Estimates & Invoices Page
/dashboard/invoices
Auto-generated or Manual Send


Downloadable PDFs


Filters by date/customer


Option to resend



5.7 Bot Features Page
/dashboard/features
Toggle features on/off


Appointments


WhatsApp


Invoices


Payments


Feature descriptions & pricing info



5.8 Support Page
/dashboard/support
Open Ticket


Live Chat (or WhatsApp link)


FAQ / Knowledge Base


Contact Email



5.9 Upgrade/Payments Page
/dashboard/upgrade
Current Plan Info


Plan Comparison Table


Payment Gateway Integration (Razorpay or Stripe)


Billing History


Download Invoice Button



 6. Admin Dashboard (Internal Only)
Route: /admin
Sidebar:
 View Clients


 Assign Numbers


 Bot Config Viewer


 All Call Logs


 Support Panel


 WhatsApp Broadcast


 System Analytics


Key Admin Actions:
View pending signups


Assign Plivo Number to client


Activate/deactivate accounts


View call traffic per client


Force update a bot config


View support tickets



 7. Pages for Public Site
Routes:
/about


/contact


/terms


/privacy


/pricing



 8. Payment Success/Failure Pages
Routes:
/payment/success


/payment/failure



 9. Password Reset Flow
Routes:
/forgot-password


/reset-password



 10. Responsive Design (Mobile UI Ready)
Make sure each of the above pages is mobile-friendly, especially:
Call logs


Estimates


Appointments


Upgrade/payment page



 11. Error & State Pages
/404 – Page Not Found


/403 – Access Denied


/loading – Optional loader


/maintenance – For downtime




Supabase Tables: - 
 1. profiles (Extended Users Table)

Column
Type
Notes
id
UUID (PK)
FK to auth.users.id
role
TEXT
'admin', 'client'
full_name
TEXT


created_at
TIMESTAMP
Default: NOW()


 2. clients (Business Info)
Column
Type
Notes
id
UUID (PK)
Unique client ID
user_id
UUID
FK to profiles.id
shop_name
TEXT


business_type
TEXT
e.g., Grocery, Salon
business_summary
TEXT
Short business intro
whatsapp_number
TEXT
Used for messaging
plivo_number
TEXT
Dedicated number
status
TEXT
'pending', 'active', 'inactive'
config_completed
BOOLEAN
Tracks if onboarding finished
created_at
TIMESTAMP





 3. products (JSONB Model)
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
product_data
JSONB
Flexible product object
created_at
TIMESTAMP




4. bot_features (Feature Toggles)
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
appointments_enabled
BOOLEAN
Default: FALSE
whatsapp_enabled
BOOLEAN


payment_enabled
BOOLEAN


estimate_enabled
BOOLEAN


invoice_enabled
BOOLEAN


multi_language
BOOLEAN


call_summary_enabled
BOOLEAN


created_at
TIMESTAMP




5. appointment_slots
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
slot_time
TIMESTAMP


is_booked
BOOLEAN


booked_by
TEXT
Caller number or email
created_at
TIMESTAMP




6. call_logs
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
caller_number
TEXT


call_time
TIMESTAMP
Default: NOW()
duration
INT
Call duration in seconds
summary
TEXT
Bot-generated or manual
transcript_url
TEXT
URL of transcript (optional)
was_estimate_sent
BOOLEAN


was_payment_sent
BOOLEAN



 9. whatsapp_logs
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
to_number
TEXT


message_type
TEXT
estimate, reminder, invoice etc.
content
TEXT
Message content
status
TEXT
sent, delivered, read
timestamp
TIMESTAMP




8. emails_sent
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
to_email
TEXT


subject
TEXT


body
TEXT
HTML/text email body
status
TEXT
sent, failed, queued
created_at
TIMESTAMP




9. invoices
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
to_number
TEXT


amount
NUMERIC


pdf_url
TEXT
Storage URL
sent_at
TIMESTAMP


paid
BOOLEAN



10. payments
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
to_number
TEXT


amount
NUMERIC


payment_link
TEXT
URL to payment
status
TEXT
pending, paid, failed
paid_at
TIMESTAMP
Nullable
created_at
TIMESTAMP






11. Plan_subscriptions

Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
plan_name
TEXT
basic, pro, biz, enterprise
start_date
TIMESTAMP


end_date
TIMESTAMP


status
TEXT
active, cancelled, etc.
auto_renew
BOOLEAN




🛠 13. admin_actions
Column
Type
Notes
id
UUID (PK)


admin_id
UUID
FK to profiles.id
client_id
UUID
FK to clients.id
action_type
TEXT
assign_number, approve, ban, etc.
details
JSONB
Action details
created_at
TIMESTAMP




13. support_tickets
Column
Type
Notes
id
UUID (PK)


client_id
UUID
FK to clients.id
subject
TEXT


description
TEXT


status
TEXT
open, closed, in_progress
priority
TEXT
low, medium, high
created_at
TIMESTAMP




Our Main Call Function: -
Objective
We want one centralized bot service that can serve many businesses — by dynamically reading business-specific data depending on which phone number was called.

 High-Level Principle
One universal bot engine.
One dynamic database.
Many phone numbers mapped to different businesses.
A single codebase routes requests based on the called_number.

 Example Case
Let’s say:
Plivo Numbers:


+91-9988770023 → Aaradhana Enterprises


+91-9988770054 → Fish Shop


Customer calls +91-9988770054


Our bot answers using Fish Shop’s catalog, intro, and features



Technical Flow Explained (Step-by-Step)

Step 1: Incoming Call from Customer
A customer calls any Plivo number (say +91-9988770054)


Plivo hits our public webhook URL:
 POST https://yourbot.com/api/plivo-webhook


It includes:
json
CopyEdit
{
  "From": "+91-9876543210",
  "To": "+91-9988770054"
}


Step 2: Lookup in Database
At this point, your bot backend (Node.js) will:
Query:


SELECT * FROM clients WHERE plivo_number = '+91-9988770054';

It finds that this number belongs to Fish Shop.


We now have client_id, shop_name, and all required config IDs.



Step 3: Load Client Configuration
Based on the client_id, we now dynamically load:
 Product catalog from products


 Bot feature config from bot_features


 Invoice/estimate templates if needed


 Appointment slots from appointment_slots


 Business summary from clients.business_summary


 Previous call logs if needed



Step 4: Respond to Caller Using the Right Data
Our bot engine (in Express, using Plivo XML or real-time WebSocket) uses the fetched data to:
Greet based on Fish Shop’s intro


Respond to product queries from Fish Shop’s catalog


Offer to book appointment or send WhatsApp based on Fish Shop’s config


Send estimate, invoice, or payment links using Fish Shop’s template


All logic is dynamic, based on DB data.

Step 5: Post-Call Handling
After call ends, our backend:
Stores call summary in call_logs with client_id


Sends WhatsApp if enabled using WhatsApp Cloud API


Log this to whatsapp_logs


If payment is initiated → store in payments


If invoice sent → store in invoices



Step 6: Dashboard Sync
Client (Fish Shop) logs into /dashboard
Sees the call log from this call


Sees WhatsApp/invoice history


Can update product catalog or enable/disable features



Summary of Key Tables Used in Flow
Table
Purpose
clients
Maps Plivo number → client ID
products
Loads product info dynamically
bot_features
Controls which features are active
appointment_slots
Fetches available booking times
call_logs
Stores summary + metadata of call
whatsapp_logs
Logs post-call WhatsApp messages
invoices, payments
Tracks all follow-up activity


How We Support 1000+ Clients with One Bot
Single Webhook Endpoint
Single Bot Logic Engine
One Plivo account with multiple numbers
Dynamic routing using plivo_number → client_id
Single database, scoped per client_id
This makes it:
Fast to scale


Easy to debug


Highly secure via RLS


Maintainable (we don’t create 1000 bots)




Flow Diagram :- 





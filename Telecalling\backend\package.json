{"name": "voicebot-backend", "version": "1.0.0", "description": "VoiceBot Platform Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.6.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^7.0.5", "openai": "^4.104.0", "pdf-parse": "^1.1.1", "postgres": "^3.4.7", "razorpay": "^2.9.6", "resend": "^4.6.0", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["voicebot", "api", "express", "supabase"], "author": "VoiceBot Team", "license": "MIT"}
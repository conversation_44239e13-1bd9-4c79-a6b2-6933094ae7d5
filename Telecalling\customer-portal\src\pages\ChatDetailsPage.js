import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, Send, User, Bot } from 'lucide-react';

const ChatDetailsPage = () => {
  const { id } = useParams();
  const [newMessage, setNewMessage] = useState('');
  
  // Mock chat data
  const chat = {
    id: id,
    title: 'Product Support',
    clientName: 'TechStore Solutions',
    status: 'active'
  };

  const [messages] = useState([
    {
      id: '1',
      sender: 'client',
      content: 'Hello! I need help with laptop specifications.',
      timestamp: '2024-01-20 10:00 AM',
      senderName: 'Support Team'
    },
    {
      id: '2',
      sender: 'customer',
      content: 'Hi! I\'m looking for a gaming laptop under ₹80,000. Can you help me with some options?',
      timestamp: '2024-01-20 10:05 AM',
      senderName: 'You'
    },
    {
      id: '3',
      sender: 'client',
      content: 'Absolutely! For gaming laptops under ₹80,000, I can recommend a few excellent options. What games do you primarily play?',
      timestamp: '2024-01-20 10:10 AM',
      senderName: 'Support Team'
    },
    {
      id: '4',
      sender: 'customer',
      content: 'I mainly play AAA games like Cyberpunk 2077, Call of Duty, and some video editing work.',
      timestamp: '2024-01-20 10:15 AM',
      senderName: 'You'
    },
    {
      id: '5',
      sender: 'client',
      content: 'Perfect! For those requirements, I\'d suggest looking at laptops with RTX 3060 or better graphics cards. Let me prepare a detailed list with specifications and pricing.',
      timestamp: '2024-01-20 10:20 AM',
      senderName: 'Support Team'
    }
  ]);

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim()) {
      // TODO: Implement send message API call
      console.log('Sending message:', newMessage);
      setNewMessage('');
    }
  };

  return (
    <div className="chat-details-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <header style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <Link 
              to="/chats" 
              style={{ 
                padding: '0.5rem', 
                backgroundColor: '#f3f4f6', 
                borderRadius: '4px',
                textDecoration: 'none',
                color: '#374151',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <ArrowLeft size={20} />
            </Link>
            <div>
              <h1 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
                {chat.title}
              </h1>
              <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                with {chat.clientName}
              </p>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <span style={{ 
              padding: '0.25rem 0.75rem', 
              backgroundColor: chat.status === 'active' ? '#dcfce7' : '#f3f4f6', 
              color: chat.status === 'active' ? '#166534' : '#6b7280', 
              borderRadius: '12px', 
              fontSize: '0.75rem',
              fontWeight: '500'
            }}>
              {chat.status === 'active' ? 'Active' : 'Closed'}
            </span>
          </div>
        </div>
      </header>

      {/* Chat Messages */}
      <div style={{ flex: 1, maxWidth: '1200px', margin: '0 auto', width: '100%', padding: '1rem', display: 'flex', flexDirection: 'column' }}>
        <div style={{ 
          flex: 1, 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)', 
          display: 'flex', 
          flexDirection: 'column',
          minHeight: '500px'
        }}>
          {/* Messages Container */}
          <div style={{ 
            flex: 1, 
            padding: '1rem', 
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: '1rem'
          }}>
            {messages.map((message) => (
              <div 
                key={message.id} 
                style={{ 
                  display: 'flex', 
                  justifyContent: message.sender === 'customer' ? 'flex-end' : 'flex-start',
                  alignItems: 'flex-start',
                  gap: '0.5rem'
                }}
              >
                {message.sender === 'client' && (
                  <div style={{ 
                    width: '32px', 
                    height: '32px', 
                    backgroundColor: '#3b82f6', 
                    borderRadius: '50%', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    flexShrink: 0
                  }}>
                    <User size={16} style={{ color: 'white' }} />
                  </div>
                )}
                
                <div style={{ 
                  maxWidth: '70%',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: message.sender === 'customer' ? 'flex-end' : 'flex-start'
                }}>
                  <div style={{ 
                    padding: '0.75rem 1rem',
                    borderRadius: '12px',
                    backgroundColor: message.sender === 'customer' ? '#3b82f6' : '#f3f4f6',
                    color: message.sender === 'customer' ? 'white' : '#1f2937',
                    fontSize: '0.875rem',
                    lineHeight: '1.4'
                  }}>
                    {message.content}
                  </div>
                  <div style={{ 
                    fontSize: '0.75rem', 
                    color: '#9ca3af', 
                    marginTop: '0.25rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem'
                  }}>
                    <span>{message.senderName}</span>
                    <span>•</span>
                    <span>{message.timestamp}</span>
                  </div>
                </div>

                {message.sender === 'customer' && (
                  <div style={{ 
                    width: '32px', 
                    height: '32px', 
                    backgroundColor: '#10b981', 
                    borderRadius: '50%', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    flexShrink: 0
                  }}>
                    <User size={16} style={{ color: 'white' }} />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Message Input */}
          <div style={{ 
            borderTop: '1px solid #e5e7eb', 
            padding: '1rem'
          }}>
            <form onSubmit={handleSendMessage} style={{ display: 'flex', gap: '0.5rem' }}>
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message..."
                style={{
                  flex: 1,
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '0.875rem',
                  outline: 'none'
                }}
                onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
              <button
                type="submit"
                disabled={!newMessage.trim()}
                style={{
                  padding: '0.75rem 1rem',
                  backgroundColor: newMessage.trim() ? '#3b82f6' : '#9ca3af',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: newMessage.trim() ? 'pointer' : 'not-allowed',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500'
                }}
              >
                <Send size={16} />
                Send
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatDetailsPage;

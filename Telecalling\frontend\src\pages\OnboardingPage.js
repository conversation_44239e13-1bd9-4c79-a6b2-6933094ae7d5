import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { 
  Store, 
  Phone, 
  MessageSquare, 
  Settings, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Package,
  Calendar,
  CreditCard
} from 'lucide-react';
import { clientAPI } from '../services/api';
import Button from '../components/common/Button';

const OnboardingPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [businessData, setBusinessData] = useState({});
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();

  const steps = [
    {
      id: 1,
      title: "Business Information",
      description: "Tell us about your business",
      icon: <Store className="w-6 h-6" />
    },
    {
      id: 2,
      title: "Contact Details",
      description: "How customers can reach you",
      icon: <Phone className="w-6 h-6" />
    },
    {
      id: 3,
      title: "Bot Features",
      description: "Configure your AI assistant",
      icon: <Settings className="w-6 h-6" />
    },
    {
      id: 4,
      title: "Complete Setup",
      description: "Review and submit",
      icon: <CheckCircle className="w-6 h-6" />
    }
  ];

  const businessTypes = [
    "Electronics & Mobile Shop",
    "Beauty Salon & Spa",
    "Auto Parts & Services",
    "Clothing & Fashion",
    "Restaurant & Food",
    "Medical & Healthcare",
    "Home Services",
    "Education & Training",
    "Real Estate",
    "Other"
  ];

  const regions = [
    "Delhi NCR",
    "Mumbai",
    "Bangalore",
    "Chennai",
    "Hyderabad",
    "Pune",
    "Kolkata",
    "Ahmedabad",
    "Jaipur",
    "Other"
  ];

  const handleStepSubmit = (data) => {
    setBusinessData(prev => ({ ...prev, ...data }));
    
    if (currentStep < 4) {
      setCurrentStep(prev => prev + 1);
      reset();
    } else {
      handleFinalSubmit();
    }
  };

  const handleFinalSubmit = async () => {
    setLoading(true);
    try {
      const finalData = {
        ...businessData,
        features: businessData.features || {}
      };

      const result = await clientAPI.create(finalData);
      
      if (result.data) {
        toast.success('Onboarding completed successfully!');
        navigate('/dashboard');
      }
    } catch (error) {
      toast.error(error.response?.data?.error || 'Failed to complete onboarding');
    } finally {
      setLoading(false);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <form onSubmit={handleSubmit(handleStepSubmit)} className="space-y-6">
            <div>
              <label className="form-label">Business/Shop Name *</label>
              <input
                {...register('shopName', {
                  required: 'Business name is required',
                  minLength: {
                    value: 2,
                    message: 'Business name must be at least 2 characters'
                  }
                })}
                type="text"
                className={`form-input ${errors.shopName ? 'error' : ''}`}
                placeholder="e.g., Kumar Electronics, Sharma Beauty Salon"
              />
              {errors.shopName && (
                <p className="form-error">{errors.shopName.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">Business Type *</label>
              <select
                {...register('businessType', {
                  required: 'Please select your business type'
                })}
                className={`form-input form-select ${errors.businessType ? 'error' : ''}`}
              >
                <option value="">Select your business type</option>
                {businessTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              {errors.businessType && (
                <p className="form-error">{errors.businessType.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">Business Summary</label>
              <textarea
                {...register('businessSummary')}
                className="form-input form-textarea"
                placeholder="Briefly describe what your business offers (optional)"
                rows={3}
              />
              <p className="text-xs text-gray-500 mt-1">
                This will be used by your AI assistant to introduce your business to callers
              </p>
            </div>

            <div>
              <label className="form-label">Region/City *</label>
              <select
                {...register('region', {
                  required: 'Please select your region'
                })}
                className={`form-input form-select ${errors.region ? 'error' : ''}`}
              >
                <option value="">Select your region</option>
                {regions.map(region => (
                  <option key={region} value={region}>{region}</option>
                ))}
              </select>
              {errors.region && (
                <p className="form-error">{errors.region.message}</p>
              )}
            </div>

            <div className="flex justify-end">
              <Button type="submit">
                Next Step <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </form>
        );

      case 2:
        return (
          <form onSubmit={handleSubmit(handleStepSubmit)} className="space-y-6">
            <div>
              <label className="form-label">WhatsApp Business Number *</label>
              <input
                {...register('whatsappNumber', {
                  required: 'WhatsApp number is required',
                  pattern: {
                    value: /^[6-9]\d{9}$/,
                    message: 'Please enter a valid 10-digit mobile number'
                  }
                })}
                type="tel"
                className={`form-input ${errors.whatsappNumber ? 'error' : ''}`}
                placeholder="9876543210"
                maxLength={10}
              />
              {errors.whatsappNumber && (
                <p className="form-error">{errors.whatsappNumber.message}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                This number will be used to send automated messages to customers
              </p>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">What happens next?</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Our team will assign you a dedicated phone number</li>
                <li>• We'll configure your AI voice assistant</li>
                <li>• You'll receive setup instructions via email</li>
                <li>• Your VoiceBot will be live within 24 hours</li>
              </ul>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={goToPreviousStep}>
                <ArrowLeft className="w-4 h-4 mr-2" /> Previous
              </Button>
              <Button type="submit">
                Next Step <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </form>
        );

      case 3:
        return (
          <form onSubmit={handleSubmit(handleStepSubmit)} className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Configure Your AI Assistant Features
              </h3>
              <p className="text-gray-600">
                Select the features you want to enable for your voice bot
              </p>

              <div className="grid grid-cols-1 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-start">
                    <input
                      {...register('features.appointmentsEnabled')}
                      type="checkbox"
                      className="form-checkbox mt-1"
                      id="appointments"
                    />
                    <div className="ml-3">
                      <label htmlFor="appointments" className="flex items-center font-medium text-gray-900">
                        <Calendar className="w-5 h-5 mr-2 text-purple-600" />
                        Appointment Booking
                      </label>
                      <p className="text-sm text-gray-600 mt-1">
                        Allow customers to book appointments through voice calls
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start">
                    <input
                      {...register('features.estimateEnabled')}
                      type="checkbox"
                      className="form-checkbox mt-1"
                      id="estimates"
                    />
                    <div className="ml-3">
                      <label htmlFor="estimates" className="flex items-center font-medium text-gray-900">
                        <Package className="w-5 h-5 mr-2 text-blue-600" />
                        Estimate Generation
                      </label>
                      <p className="text-sm text-gray-600 mt-1">
                        Send product/service estimates via WhatsApp after calls
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start">
                    <input
                      {...register('features.paymentEnabled')}
                      type="checkbox"
                      className="form-checkbox mt-1"
                      id="payments"
                    />
                    <div className="ml-3">
                      <label htmlFor="payments" className="flex items-center font-medium text-gray-900">
                        <CreditCard className="w-5 h-5 mr-2 text-green-600" />
                        Payment Links
                      </label>
                      <p className="text-sm text-gray-600 mt-1">
                        Generate and send payment links to customers
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start">
                    <input
                      {...register('features.whatsappEnabled')}
                      type="checkbox"
                      className="form-checkbox mt-1"
                      id="whatsapp"
                      defaultChecked
                    />
                    <div className="ml-3">
                      <label htmlFor="whatsapp" className="flex items-center font-medium text-gray-900">
                        <MessageSquare className="w-5 h-5 mr-2 text-green-600" />
                        WhatsApp Integration
                      </label>
                      <p className="text-sm text-gray-600 mt-1">
                        Automatic follow-up messages after calls (Recommended)
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={goToPreviousStep}>
                <ArrowLeft className="w-4 h-4 mr-2" /> Previous
              </Button>
              <Button type="submit">
                Next Step <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </form>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                Almost Ready!
              </h3>
              <p className="text-gray-600">
                Review your information and complete the setup
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <div>
                <h4 className="font-medium text-gray-900">Business Information</h4>
                <p className="text-gray-600">{businessData.shopName}</p>
                <p className="text-sm text-gray-500">{businessData.businessType} • {businessData.region}</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">WhatsApp Number</h4>
                <p className="text-gray-600">+91 {businessData.whatsappNumber}</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-900">Enabled Features</h4>
                <div className="flex flex-wrap gap-2 mt-2">
                  {businessData.features?.appointmentsEnabled && (
                    <span className="badge badge-primary">Appointments</span>
                  )}
                  {businessData.features?.estimateEnabled && (
                    <span className="badge badge-primary">Estimates</span>
                  )}
                  {businessData.features?.paymentEnabled && (
                    <span className="badge badge-primary">Payments</span>
                  )}
                  {businessData.features?.whatsappEnabled && (
                    <span className="badge badge-success">WhatsApp</span>
                  )}
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">Next Steps</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>1. Our team will review your application within 24 hours</li>
                <li>2. You'll receive a dedicated phone number via email</li>
                <li>3. We'll configure your AI assistant with your business details</li>
                <li>4. Your VoiceBot will be live and ready to handle calls!</li>
              </ul>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={goToPreviousStep}>
                <ArrowLeft className="w-4 h-4 mr-2" /> Previous
              </Button>
              <Button onClick={handleFinalSubmit} loading={loading}>
                Complete Setup <CheckCircle className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Phone className="w-10 h-10 text-blue-600 mr-3" />
            <span className="text-3xl font-bold text-gray-900">VoiceBot</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome to VoiceBot!
          </h1>
          <p className="text-gray-600 mt-2">
            Let's set up your AI voice assistant in just a few steps
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'border-gray-300 text-gray-500'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : (
                    step.icon
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-full h-1 mx-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2">
            {steps.map(step => (
              <div key={step.id} className="text-center" style={{ width: '200px' }}>
                <h3 className={`text-sm font-medium ${
                  currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                }`}>
                  {step.title}
                </h3>
                <p className="text-xs text-gray-500 mt-1">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Step {currentStep}: {steps[currentStep - 1].title}
              </h2>
              <p className="text-gray-600 mt-1">
                {steps[currentStep - 1].description}
              </p>
            </div>
            
            {renderStepContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage; 
.signup-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.signup-modal-container {
  background: white;
  border-radius: 24px;
  overflow: hidden;
  display: flex;
  width: 900px;
  height: 650px;
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  padding: 0;
}

.modal-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

/* Right side - Image Section */
.modal-image-section {
  flex: 1.2;
  position: relative;
  overflow: hidden;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.modal-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}

.image-overlay {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  background: transparent;
  border-radius: 16px;
}

/* Decorative white shapes */
.decorative-shape {
  position: absolute;
  background: #ffffff;
  z-index: 10;
  
}

.top-left-shape {
  top: -15px;
  left: -15px;
  width: 75px;
  height: 55px;
  border-radius: 0 0 8px 0;
}

.top-left-shape::after {
  content: '';
  position: absolute;
  top: 54px;
  left: 15px;
  width: 40px;
  height: 31px;
  background: #ffffff;
  border-radius: 0 0 8px 0;
}

.bottom-right-shape {
  bottom: -15px;
  right: -15px;
  width: 95px;
  height: 55px;
  border-radius: 8px 0 0 0;
}

.bottom-right-shape::before {
  content: '';
  position: absolute;
  top: -31px;
  right: 15px;
  width: 60px;
  height: 32px;
  background: #ffffff;
  border-radius: 8px 0 0 0;
}

.overlay-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px 30px;
  z-index: 2;
}

.overlay-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 24px;
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  max-width: 280px;
}

.overlay-card h3 {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
  line-height: 1.3;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.overlay-card p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.bottom-overlay {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 16px;
  padding: 24px;
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  max-width: 280px;
  align-self: flex-end;
}

.bottom-overlay h3 {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 8px 0;
  line-height: 1.3;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.bottom-overlay p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin: 0;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Left side - Form Section */
.modal-form-section {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}

.form-content {
  padding: 20px 24px 15px 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

/* Header */
.modal-header {
  text-align: center;
  margin-bottom: 15px;
  margin-top: -21px;
}

.modal-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 6px;
}

.logo-text {
  font-size: 22px;
  font-weight: 700;
  color: #1a1a1a;
}

.modal-tagline {
  font-size: 13px;
  color: #666;
  margin: 0;
}

/* Toggle buttons */
.auth-toggle {
  display: flex;
  gap: 0;
  margin-bottom: 19px;
}

.toggle-btn {
  flex: 1;
  margin-left: 5px;
  padding: 10px 20px;
  border: 1px solid #1a1a1a;
  background: transparent;
  color: #666;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
}

.toggle-btn.active {
  background: #1a1a1a;
  color: white;
  border: 1px solid #1a1a1a;
}

.toggle-btn:not(.active):hover {
  background: #f5f5f5;
  color: #1a1a1a;
  border: 1px solid #1a1a1a;
}

/* Form main content */
.form-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 6px 0;
  text-align: left;
}

.form-subtitle {
  font-size: 13px;
  color: #666;
  margin: 0 0 15px 0;
}

/* Social buttons */
.social-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 7px;
}

.social-btn {
  flex: 1;
  padding: 10px;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.social-btn:hover {
  border-color: #007bbf;
  background: #f8fcff;
}

.x-icon {
  font-size: 16px;
  font-weight: bold;
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 2px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e5e5e5;
}

.divider span {
  background: white;
  padding: 0 12px;
  color: #666;
  font-size: 13px;
  position: relative;
}

/* Form */
.signup-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modal-form-group {
  position: relative;
  margin-bottom: 3px;
  margin-bottom: 0.5rem;
}

.modal-form-group input {
  width: 100%;
  padding: 12px 14px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  font-size: 13px;
  transition: all 0.2s ease;
  background: #fafafa;
  box-sizing: border-box;
}

.modal-form-group input:focus {
  outline: none;
  border-color: #007bbf;
  background: white;
  box-shadow: 0 0 0 3px rgba(0, 123, 191, 0.1);
}

.modal-form-group input:focus + label,
.modal-form-group input:not(:placeholder-shown) + label {
  top: -8px;
  left: 12px;
  font-size: 11px;
  color: #007bbf;
  background: white;
  padding: 0 4px;
}

.modal-form-group label {
  position: absolute;
  top: 12px;
  left: 14px;
  font-size: 13px;
  color: #999;
  font-weight: 400;
  transition: all 0.2s ease;
  pointer-events: none;
  background: transparent;
}

.modal-form-group input::placeholder {
  color: transparent;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0px 0 3px 0;
}

.form-check input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: #007bbf;
}

.form-check label {
  font-size: 13px;
  color: #666;
  cursor: pointer;
}

.submit-btn {
  background: #1a1a1a;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1px;
}

.submit-btn:hover {
  background: #333;
  transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 768px) {
  .signup-modal-container {
    flex-direction: column;
    width: 95vw;
    height: 80vh;
    max-height: 80vh;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .signup-modal-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: var(--modal-bg-image);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: blur(8px);
    z-index: 1;
  }

  .signup-modal-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    z-index: 2;
  }

  .modal-image-section {
    display: none;
  }

  .modal-form-section {
    flex: 1;
    order: 1;
    padding: 15px;
    background: transparent;
    position: relative;
    z-index: 3;
  }

  .form-content {
    padding: 15px 18px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .overlay-content {
    display: none;
  }

  .overlay-card,
  .bottom-overlay {
    display: none;
  }

  .form-title {
    font-size: 20px;
  }

  .modal-close-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

@media (max-width: 480px) {
  .signup-modal-container {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }

  .modal-close-btn {
    position: fixed;
    top: 15px;
    right: 15px;
    width: 36px;
    height: 36px;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .modal-form-section {
    padding: 12px;
  }

  .form-content {
    padding: 12px 14px;
  }

  .social-buttons {
    gap: 6px;
  }

  .social-btn {
    padding: 8px;
  }
} 

/* Form Footer - Already have account section */
.form-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

.form-footer p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  font-weight: 400;
  line-height: 1.5;
}

.form-footer .link-btn {
  background: none;
  border: none;
  color: #667eea;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  margin-left: 4px;
}

.form-footer .link-btn:hover {
  color: #5a67d8;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-footer .link-btn:active {
  transform: translateY(0);
}

/* Field errors */
.field-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
  font-weight: 500;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

/* Form options styling */
.form-options {
  margin: 16px 0;
}

.form-options .form-check {
  justify-content: flex-start;
  gap: 8px;
  margin-bottom: 16px;
}

.form-options .form-check label {
  font-size: 13px;
  color: #64748b;
  cursor: pointer;
  line-height: 1.4;
}

.form-options .form-check input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
  margin: 0;
  flex-shrink: 0;
}

/* Responsive adjustments for form footer */
@media (max-width: 768px) {
  .form-footer {
    margin-top: 16px;
    padding-top: 16px;
  }
  
  .form-footer p {
    font-size: 13px;
  }
  
  .form-footer .link-btn {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .form-footer {
    margin-top: 14px;
    padding-top: 14px;
  }
  
  .form-footer p {
    font-size: 12px;
  }
  
  .form-footer .link-btn {
    font-size: 12px;
  }
} 
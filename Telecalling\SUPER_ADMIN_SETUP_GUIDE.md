# 🔐 Super Admin Setup Guide

## 📋 **Overview**

The Super Admin system provides centralized control over admin management and client assignments within the existing admin panel structure. Super admins can:

- ✅ **Manage Admins** - Create, activate/deactivate regular admins
- ✅ **Assign Admins to Clients** - Assign which admin manages which clients
- ✅ **Unlimited Access** - View and manage ALL clients and admins (no 250 limit)
- ✅ **Client-Admin Relationships** - See which admin is assigned to each client
- ✅ **Admin Workload Management** - Monitor admin capacity and redistribute clients
- ✅ **Audit Logging** - Track all super admin actions
- ✅ **Unified Interface** - Works within existing admin panel on port 4000

## 🗄️ **Step 1: Database Setup**

### Run Super Admin Schema
```sql
-- Execute this in Supabase SQL Editor
-- File: Telecalling/database/super_admin_setup.sql
```

This will:
- ✅ Add `role` column to admins table
- ✅ Create super admin account
- ✅ Set up audit logging
- ✅ Create helper functions

### Default Super Admin Account Created:
- **Admin ID**: `superadmin001`
- **Password**: `superadmin123` (simple password, no encryption)
- **Email**: `<EMAIL>`
- **Role**: `super_admin`

**Note**: Super admin uses simple password authentication (no bcrypt hashing) for easier management.

## 🚀 **Step 2: Start Admin Panel**

The super admin functionality is integrated into the existing admin panel:

```bash
cd Telecalling/admin-panel
npm start
```

**Output**: `🚀 Admin Panel Server running on port 4000`

## 🔐 **Step 3: Super Admin Authentication**

### Login as Super Admin
```bash
curl -X POST http://localhost:4000/api/admin/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "adminId": "superadmin001",
    "password": "superadmin123"
  }'
```

**Expected Response**:
```json
{
  "message": "Admin login successful",
  "admin": {
    "id": "uuid-here",
    "adminId": "superadmin001",
    "name": "Super Administrator",
    "email": "<EMAIL>"
  },
  "tokens": {
    "accessToken": "jwt-token-here",
    "refreshToken": "refresh-token-here"
  }
}
```

## 📊 **Step 4: Super Admin API Routes**

### **Super Admin Dashboard** (`/api/admin/super`)
- ✅ `GET /dashboard` - Super admin overview
- ✅ `GET /admins` - List all admins with workload
- ✅ `POST /admins` - Create new admin
- ✅ `PUT /admins/:id/status` - Activate/deactivate admin

### **Client-Admin Assignment Management**
- ✅ `GET /clients` - Get all clients with admin assignment info
- ✅ `GET /clients/unassigned` - Get unassigned clients
- ✅ `POST /clients/assign-admin` - Assign admin to multiple clients
- ✅ `POST /clients/:id/assign-admin` - Assign admin to single client
- ✅ `DELETE /clients/:id/remove-admin` - Remove admin assignment from client
- ✅ `GET /admins/:id/clients` - Get admin's assigned clients

### **Audit & Monitoring**
- ✅ `GET /audit-log` - View super admin action history

## 🎯 **Step 5: Super Admin Operations**

### Get Super Admin Dashboard
```bash
curl -X GET http://localhost:4000/api/admin/super/dashboard \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

### List All Admins
```bash
curl -X GET "http://localhost:4000/api/admin/super/admins?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

### Create New Admin
```bash
curl -X POST http://localhost:4000/api/admin/super/admins \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "adminId": "admin002",
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

### Get Unassigned Clients
```bash
curl -X GET "http://localhost:4000/api/admin/super/clients/unassigned?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

### Get All Clients with Admin Assignment Info
```bash
curl -X GET "http://localhost:4000/api/admin/super/clients?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

### Assign Admin to Single Client
```bash
curl -X POST http://localhost:4000/api/admin/super/clients/CLIENT_UUID/assign-admin \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "adminId": "admin-uuid-here"
  }'
```

### Assign Admin to Multiple Clients
```bash
curl -X POST http://localhost:4000/api/admin/super/clients/assign-admin \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "adminId": "admin-uuid-here",
    "clientIds": ["client-uuid-1", "client-uuid-2", "client-uuid-3"]
  }'
```

### Remove Admin Assignment from Client
```bash
curl -X DELETE http://localhost:4000/api/admin/super/clients/CLIENT_UUID/remove-admin \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

### Deactivate Admin
```bash
curl -X PUT http://localhost:4000/api/admin/super/admins/ADMIN_UUID/status \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "isActive": false
  }'
```

## 🔒 **Step 6: Regular Admin vs Super Admin Access**

### **Regular Admin** (`role: 'admin'`)
- ✅ Can access assigned clients only (max 250)
- ✅ Can manage Plivo numbers for assigned clients
- ✅ Can view support tickets for assigned clients
- ❌ Cannot see other admins or unassigned clients
- ❌ Cannot assign admins to clients

### **Super Admin** (`role: 'super_admin'`)
- ✅ **UNLIMITED ACCESS** to ALL clients and admins
- ✅ Can create and manage admin accounts
- ✅ Can assign/reassign admins to clients
- ✅ Can manage Plivo numbers for any client
- ✅ Can view comprehensive dashboard with admin workload
- ✅ Can see which admin is assigned to each client
- ✅ All actions are logged in audit trail
- ✅ No 250-client limitation

## 📈 **Step 7: Enhanced Dashboard Features**

### Regular Admin Dashboard
```json
{
  "dashboard": {
    "assignedClients": 45,
    "activeNumbers": 30,
    "availableSlots": 205,
    "adminInfo": {
      "role": "admin",
      "isSuperAdmin": false
    }
  }
}
```

### Super Admin Dashboard
```json
{
  "dashboard": {
    "adminManagement": {
      "totalAdmins": 5,
      "inactiveAdmins": 1,
      "topAdminsByWorkload": [
        {
          "id": "admin-uuid",
          "name": "John Doe",
          "adminId": "admin002",
          "assignedClients": 180,
          "availableSlots": 70
        }
      ]
    },
    "clientManagement": {
      "totalClients": 1250,
      "assignedClients": 1200,
      "unassignedClients": 50,
      "clientsWithNumbers": 800
    },
    "systemActivity": {
      "openTickets": 25,
      "todayActivity": {
        "calls": 450,
        "sms": 320
      }
    },
    "superAdminInfo": {
      "name": "Super Administrator",
      "role": "super_admin",
      "hasUnlimitedAccess": true
    }
  }
}
```

## 🔍 **Step 8: Audit Trail**

### View Audit Log
```bash
curl -X GET "http://localhost:4000/api/admin/super/audit-log?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN"
```

**Tracked Actions**:
- `create_admin` - New admin creation
- `activate_admin` / `deactivate_admin` - Admin status changes
- `assign_clients` - Client assignments
- `remove_client_assignment` - Client unassignments

## 🛡️ **Step 9: Security Features**

### **Role-Based Access Control**
- Super admin routes require `super_admin` role
- Regular admin routes accept both `admin` and `super_admin` roles
- JWT tokens include role information

### **Authentication Methods**
- **Super Admin**: Simple password authentication (no bcrypt hashing)
- **Regular Admin**: Bcrypt hashed password authentication
- Both use same JWT token system for session management

### **Data Isolation**
- Regular admins see only assigned clients
- Super admins see all data
- All actions are logged with IP and user agent

### **Session Management**
- Same JWT system as regular admins
- 8-hour access tokens, 7-day refresh tokens
- Session tracking in database

## 🚨 **Step 10: Troubleshooting**

### Common Issues:

1. **Super admin login fails**
   ```sql
   -- Check if super admin exists
   SELECT * FROM admins WHERE admin_id = 'superadmin001';
   ```

2. **Role not recognized**
   ```sql
   -- Update admin role
   UPDATE admins SET role = 'super_admin' WHERE admin_id = 'superadmin001';
   ```

3. **Access denied errors**
   - Ensure JWT token is valid
   - Check admin role in database
   - Verify session is active

## ✅ **Step 11: Verification Checklist**

- [ ] Super admin schema executed successfully
- [ ] Super admin can login with superadmin001/superadmin123
- [ ] Super admin can access dashboard
- [ ] Super admin can view all admins
- [ ] Super admin can create new admin
- [ ] Super admin can assign clients to admin
- [ ] Regular admin still works with assigned clients only
- [ ] Audit log captures super admin actions

## 🎯 **Step 12: Production Deployment**

1. **Change Default Passwords**
   ```sql
   -- For regular admin (bcrypt hash)
   UPDATE admins SET password_hash = '$2b$12$NEW_HASH_HERE'
   WHERE admin_id = 'admin001';

   -- For super admin (simple password)
   UPDATE admins SET password_hash = 'your_new_simple_password'
   WHERE admin_id = 'superadmin001';
   ```

2. **Set Environment Variables**
   ```env
   JWT_SECRET=your_production_jwt_secret
   JWT_REFRESH_SECRET=your_production_refresh_secret
   ```

3. **Enable HTTPS**
4. **Set up monitoring for audit logs**

The super admin system is now fully integrated and ready for use! 🚀

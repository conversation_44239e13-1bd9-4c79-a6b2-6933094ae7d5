# SMS PDF Storage Setup Guide

## Quick Setup Instructions

### 1. Execute the SQL File
Run the clean SQL file to set up PDF storage:

```bash
# Connect to your PostgreSQL database and run:
psql -d your_database_name -f SMS_PDF_STORAGE_CLEAN.sql
```

### 2. Verify Installation
After running the SQL, verify everything is set up correctly:

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('sms_logs', 'sms_pdf_storage');

-- Check if new columns were added to sms_logs
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'sms_logs' AND column_name LIKE 'pdf%';

-- Test the view
SELECT * FROM sms_logs_with_pdfs LIMIT 5;
```

### 3. Test with Sample Data
Create a test SMS with PDF attachment:

```sql
-- Replace with actual client_id from your clients table
SELECT create_sample_sms_with_pdf(
    'your-client-id-here'::UUID,
    '+1234567890',
    'Your estimate is ready! Please find the PDF attached.',
    'estimate_sample.pdf'
);
```

## Storage Bucket Structure

### File Organization:
```
sms-pdfs/
├── {client_id_1}/
│   ├── {sms_log_id_1}/
│   │   ├── estimate.pdf
│   │   └── invoice.pdf
│   └── {sms_log_id_2}/
│       └── quote.pdf
└── {client_id_2}/
    └── {sms_log_id_3}/
        └── receipt.pdf
```

### Example Paths:
- **Bucket Path**: `sms-pdfs/123e4567-e89b-12d3-a456-************/456e7890-e89b-12d3-a456-************/estimate.pdf`
- **Public URL**: `https://your-storage-domain.com/sms-pdfs/123e4567-e89b-12d3-a456-************/456e7890-e89b-12d3-a456-************/estimate.pdf`

## Bot Integration Example

### How Bot Should Store SMS with PDF:

```javascript
// 1. Bot creates SMS log
const smsResult = await db.query(`
    INSERT INTO sms_logs (client_id, recipient_number, message_content, status)
    VALUES ($1, $2, $3, 'sent')
    RETURNING id
`, [clientId, recipientNumber, messageContent]);

const smsLogId = smsResult.rows[0].id;

// 2. Bot uploads PDF to storage bucket
const pdfPath = `sms-pdfs/${clientId}/${smsLogId}/estimate.pdf`;
const publicUrl = await uploadToStorageBucket(pdfFile, pdfPath);

// 3. Bot records PDF in database
await db.query(`
    SELECT add_pdf_to_sms_storage_bucket($1, $2, $3, $4, $5)
`, [smsLogId, clientId, 'estimate.pdf', pdfFileSize, publicUrl]);
```

## Frontend Display

The frontend will automatically show PDF attachments in the SMS logs table with:
- PDF file icon
- Clickable file name
- File size display
- Direct download/view links

## Troubleshooting

### Common Issues:

1. **Syntax Error**: Make sure you're using the `SMS_PDF_STORAGE_CLEAN.sql` file
2. **Missing References**: Ensure `sms_logs` and `clients` tables exist before running
3. **Permission Issues**: Make sure your database user has CREATE TABLE permissions

### Verification Commands:

```sql
-- Check if function exists
SELECT proname FROM pg_proc WHERE proname = 'add_pdf_to_sms_storage_bucket';

-- Check if view exists
SELECT viewname FROM pg_views WHERE viewname = 'sms_logs_with_pdfs';

-- Count existing SMS logs
SELECT COUNT(*) FROM sms_logs;

-- Count PDF storage records
SELECT COUNT(*) FROM sms_pdf_storage;
```

## Next Steps

1. ✅ Run `SMS_PDF_STORAGE_CLEAN.sql`
2. ✅ Verify tables and functions are created
3. ✅ Test with sample data
4. ✅ Update your bot code to use the new storage structure
5. ✅ Configure your storage bucket (AWS S3, Google Cloud Storage, etc.)
6. ✅ Update storage URLs in your environment variables

## Support

If you encounter any issues:
1. Check the PostgreSQL logs for detailed error messages
2. Verify all prerequisite tables exist
3. Ensure proper database permissions
4. Contact: <EMAIL>

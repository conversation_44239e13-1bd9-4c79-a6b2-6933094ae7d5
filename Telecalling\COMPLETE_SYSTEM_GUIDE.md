# 🎯 Complete Customer Registration & Tracking System

## 🚀 **Bot Integration - Exact Links to Send**

### **For New Customers (First Time Callers):**
```
Registration Link: https://your-domain.com:4000/register/{SECURE_TOKEN}
SMS Text: "Hi! Complete your order with {BUSINESS_NAME}: {REGISTRATION_LINK} - Valid for 24 hours"
```

### **For Existing Customers (Returning Callers):**
```
Login Link: https://your-domain.com:4000/login
SMS Text: "Hi {CUSTOMER_NAME}! Continue with {BUSINESS_NAME}: https://your-domain.com:4000/login"
```

## 🤖 **Bot API Integration**

### **1. When Customer Shows Interest During Call:**
```javascript
POST http://localhost:5000/api/customers/bot/customer-interest

Body:
{
  "phoneNumber": "+91XXXXXXXXXX",
  "customerName": "<PERSON>",
  "clientPlivoNumber": "+91YYYYYYYYYY",
  "callLogId": "uuid-from-call-log",
  "productInterests": ["Laptop", "Mouse"],
  "estimatedAmount": 25000,
  "conversationSummary": "Customer interested in gaming laptop",
  "conversationData": {
    "budget": "80000",
    "usage": "gaming",
    "preferences": ["RTX graphics", "16GB RAM"]
  },
  "interestLevel": 8
}

Response:
{
  "success": true,
  "isExistingCustomer": false,
  "registrationLink": "http://localhost:4000/register/abc123...",
  "smsText": "Hi John! Complete your order with TechStore: http://localhost:4000/register/abc123... - Valid for 24 hours"
}
```

### **2. Quick Customer Lookup:**
```javascript
POST http://localhost:5000/api/customers/bot/customer-lookup

Body:
{
  "phoneNumber": "+91XXXXXXXXXX",
  "clientPlivoNumber": "+91YYYYYYYYYY"
}

Response:
{
  "success": true,
  "customerExists": true,
  "customer": {
    "id": "uuid",
    "name": "John Doe",
    "email": "<EMAIL>",
    "interactionCount": 3,
    "lastInteraction": "2024-01-20T10:30:00Z"
  }
}
```

## 📊 **Complete Database Schema**

Run this SQL in your Supabase dashboard:

```sql
-- See complete_customer_system.sql file for full schema
-- Key tables created:
-- 1. customers (enhanced with authentication)
-- 2. customer_interactions (multi-client support)
-- 3. customer_registration_tokens (with conversation data)
-- 4. customer_orders (order management)
-- 5. customer_chats (chat system)
-- 6. chat_messages (messaging)
-- 7. customer_email_tokens (OTP system)
-- 8. customer_sessions (session management)
```

## 🎯 **Complete Workflow Examples**

### **Scenario 1: New Customer Calls**
1. **Customer calls** → Bot answers using Plivo
2. **Bot discusses products** → Customer shows interest
3. **Bot calls API:**
   ```javascript
   POST /api/customers/bot/customer-interest
   // Returns registration link
   ```
4. **Bot sends SMS:** "Hi John! Complete your order with TechStore: [LINK]"
5. **Customer clicks link** → Registers with pre-filled data
6. **Customer gets email OTP** → Verifies and logs in
7. **Customer can now:** View orders, chat with business, manage profile

### **Scenario 2: Existing Customer Calls Different Business**
1. **Customer calls new number** → Bot recognizes phone number
2. **Bot calls API:**
   ```javascript
   POST /api/customers/bot/customer-interest
   // Returns isExistingCustomer: true
   ```
3. **Bot sends SMS:** "Hi John! Continue with NewBusiness: http://localhost:4000/login"
4. **Customer logs in** → Now sees both businesses in dashboard
5. **Customer can chat** with both businesses separately

## 🔧 **Client Dashboard Integration**

### **New "Customers" Tab Added:**
- **Registration Link Generator** - Enter phone number, get secure link
- **Customer Statistics** - Total customers, active chats, pending registrations
- **Customer Portal Access** - Direct link to customer portal
- **Customer List** - Recent customers with interaction history

### **Usage:**
1. Login to client dashboard at `http://localhost:3000`
2. Click "Customers" tab
3. Enter customer phone number
4. Click "Generate Link"
5. Copy and send via SMS/WhatsApp during or after call

## 🎨 **Customer Portal Features**

### **Complete Authentication System:**
- **Email-based registration** with OTP verification
- **Login with OTP** (no passwords needed)
- **Token-based registration** from bot links
- **Session management** with 30-day persistence

### **Customer Dashboard:**
- **Multi-client view** - See all businesses interacted with
- **Order history** - Track all orders and inquiries
- **Chat system** - Separate chats per business
- **Profile management** - Update personal details

### **Pages Created:**
- `/` - Landing page
- `/register/{token}` - Registration with pre-filled data
- `/login` - OTP-based login
- `/verify-email` - Email verification
- `/dashboard` - Customer overview
- `/profile` - Profile management
- `/orders` - Order history
- `/orders/{id}` - Order details
- `/chats` - Chat list
- `/chats/{id}` - Chat interface

## 🚀 **How to Start Everything**

### **1. Database Setup:**
```sql
-- Run complete_customer_system.sql in Supabase
```

### **2. Backend (Port 5000):**
```bash
cd Telecalling/backend
npm install
npm start
```

### **3. Client Dashboard (Port 3000):**
```bash
cd Telecalling/frontend
npm install
npm start
```

### **4. Customer Portal (Port 4000):**
```bash
cd Telecalling/customer-portal
npm install
npm start
```

### **5. Admin Panel (Port 4000):**
```bash
cd Telecalling/admin-panel
npm install
npm start
```

## 📱 **Bot Integration Code Example**

```javascript
// In your bot code (when customer shows interest):
const customerInterestResponse = await fetch('http://localhost:5000/api/customers/bot/customer-interest', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    phoneNumber: callerNumber,
    customerName: extractedName,
    clientPlivoNumber: receivedNumber,
    callLogId: currentCallId,
    productInterests: discussedProducts,
    estimatedAmount: quotedAmount,
    conversationSummary: aiGeneratedSummary,
    conversationData: fullConversationData,
    interestLevel: calculateInterestLevel()
  })
});

const result = await customerInterestResponse.json();

if (result.success) {
  // Send SMS with the link
  await sendSMS(callerNumber, result.smsText);
  
  // Log the interaction
  console.log(`${result.isExistingCustomer ? 'Existing' : 'New'} customer: ${result.smsText}`);
}
```

## 🎯 **Key Features Implemented**

### ✅ **Bot Integration:**
- Customer interest detection API
- Automatic link generation
- Existing customer recognition
- Conversation data storage

### ✅ **Multi-Client Support:**
- Customers can interact with multiple businesses
- Separate chat threads per business
- Unified customer dashboard
- Cross-business customer insights

### ✅ **Authentication System:**
- Email OTP verification
- Secure token-based registration
- Session management
- Password-less login

### ✅ **Customer Portal:**
- Professional UI design
- Order management
- Chat system
- Profile management
- Multi-client dashboard

### ✅ **Client Dashboard:**
- Customer management section
- Registration link generator
- Customer statistics
- Portal access

## 🔐 **Security Features**

- **Secure tokens** with 24-hour expiry
- **Rate limiting** on authentication endpoints
- **Session management** with automatic cleanup
- **Input validation** on all endpoints
- **SQL injection protection** via parameterized queries

## 📈 **Analytics & Tracking**

- **Customer interaction counts**
- **Conversation summaries**
- **Product interest tracking**
- **Estimated order values**
- **Multi-client relationship mapping**

## 🎉 **System is Now Complete!**

Your telecalling system now has:
1. **Complete bot integration** with exact API endpoints
2. **Multi-client customer support** 
3. **Professional customer portal**
4. **Enhanced client dashboard**
5. **Secure authentication system**
6. **Real-time chat capabilities**
7. **Order management system**
8. **Comprehensive analytics**

The system is ready for production use with your bot integration!

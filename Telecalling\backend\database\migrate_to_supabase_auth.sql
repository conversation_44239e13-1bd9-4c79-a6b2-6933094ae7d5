-- Migration script to add Supabase Auth support to existing system
-- Run this after the main auth_schema.sql

-- Add supabase_auth_id column to clients table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'clients' AND column_name = 'supabase_auth_id'
    ) THEN
        ALTER TABLE clients ADD COLUMN supabase_auth_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
        
        -- Add index for better performance
        CREATE INDEX IF NOT EXISTS idx_clients_supabase_auth_id ON clients(supabase_auth_id);
        
        -- Make password_hash optional (for users who will use Supabase Auth only)
        ALTER TABLE clients ALTER COLUMN password_hash DROP NOT NULL;
        
        RAISE NOTICE 'Added supabase_auth_id column to clients table';
    ELSE
        RAISE NOTICE 'supabase_auth_id column already exists in clients table';
    END IF;
END $$;

-- Update pending_registrations indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pending_registrations_email ON pending_registrations(email);
CREATE INDEX IF NOT EXISTS idx_pending_registrations_expires_at ON pending_registrations(expires_at);

-- Add index for otp_verifications pending_registration_id if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_otp_verifications_pending_registration_id ON otp_verifications(pending_registration_id);

-- Update the cleanup function to handle both auth methods
CREATE OR REPLACE FUNCTION cleanup_expired_auth_data()
RETURNS void AS $$
BEGIN
    -- Clean up expired pending registrations
    DELETE FROM pending_registrations 
    WHERE expires_at < NOW();
    
    -- Clean up expired OTPs
    DELETE FROM otp_verifications 
    WHERE expires_at < NOW() - INTERVAL '24 hours';
    
    -- Clean up expired password reset tokens
    DELETE FROM password_reset_tokens 
    WHERE expires_at < NOW() - INTERVAL '24 hours';
    
    -- Clean up expired login sessions
    DELETE FROM login_sessions 
    WHERE expires_at < NOW() - INTERVAL '7 days';
    
    RAISE NOTICE 'Cleaned up expired authentication data';
END;
$$ language 'plpgsql';

-- Create a function to help with migration from custom auth to Supabase Auth
CREATE OR REPLACE FUNCTION migrate_user_to_supabase_auth(
    client_email TEXT,
    supabase_user_id UUID
)
RETURNS boolean AS $$
DECLARE
    client_record RECORD;
BEGIN
    -- Find the client record
    SELECT * INTO client_record FROM clients WHERE email = client_email;
    
    IF NOT FOUND THEN
        RAISE NOTICE 'Client with email % not found', client_email;
        RETURN FALSE;
    END IF;
    
    -- Update the client record with Supabase Auth ID
    UPDATE clients 
    SET supabase_auth_id = supabase_user_id,
        updated_at = NOW()
    WHERE email = client_email;
    
    RAISE NOTICE 'Migrated client % to Supabase Auth (ID: %)', client_email, supabase_user_id;
    RETURN TRUE;
END;
$$ language 'plpgsql';

-- Create a function to check authentication compatibility
CREATE OR REPLACE FUNCTION check_auth_compatibility()
RETURNS TABLE(
    client_id UUID,
    email TEXT,
    has_password_hash BOOLEAN,
    has_supabase_auth_id BOOLEAN,
    auth_status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.email,
        (c.password_hash IS NOT NULL) as has_password_hash,
        (c.supabase_auth_id IS NOT NULL) as has_supabase_auth_id,
        CASE 
            WHEN c.supabase_auth_id IS NOT NULL AND c.password_hash IS NOT NULL THEN 'HYBRID'
            WHEN c.supabase_auth_id IS NOT NULL THEN 'SUPABASE_ONLY'
            WHEN c.password_hash IS NOT NULL THEN 'CUSTOM_ONLY'
            ELSE 'NO_AUTH'
        END as auth_status
    FROM clients c
    ORDER BY c.created_at DESC;
END;
$$ language 'plpgsql';

-- Create RLS policies for the new auth system
-- Note: These policies assume you have proper RLS setup in Supabase

-- Update RLS policies for clients table to support both auth methods
DROP POLICY IF EXISTS "Users can view their own profile" ON clients;
DROP POLICY IF EXISTS "Users can update their own profile" ON clients;

CREATE POLICY "Users can view their own profile" ON clients
    FOR SELECT USING (
        auth.uid() = supabase_auth_id OR 
        (supabase_auth_id IS NULL AND auth.uid()::text = id::text)
    );

CREATE POLICY "Users can update their own profile" ON clients
    FOR UPDATE USING (
        auth.uid() = supabase_auth_id OR 
        (supabase_auth_id IS NULL AND auth.uid()::text = id::text)
    );

-- Update other RLS policies to use the new auth method
DROP POLICY IF EXISTS "Users can view their own OTP verifications" ON otp_verifications;
DROP POLICY IF EXISTS "Users can insert their own OTP verifications" ON otp_verifications;

CREATE POLICY "Users can view their own OTP verifications" ON otp_verifications
    FOR SELECT USING (
        client_id IN (
            SELECT id FROM clients 
            WHERE auth.uid() = supabase_auth_id OR 
                  (supabase_auth_id IS NULL AND auth.uid()::text = id::text)
        )
    );

CREATE POLICY "Users can insert their own OTP verifications" ON otp_verifications
    FOR INSERT WITH CHECK (
        client_id IN (
            SELECT id FROM clients 
            WHERE auth.uid() = supabase_auth_id OR 
                  (supabase_auth_id IS NULL AND auth.uid()::text = id::text)
        )
    );

-- Create a view for easier querying of user authentication status
CREATE OR REPLACE VIEW user_auth_status AS
SELECT 
    c.id as client_id,
    c.email,
    c.supabase_auth_id,
    c.is_email_verified,
    c.is_active,
    c.created_at,
    CASE 
        WHEN c.supabase_auth_id IS NOT NULL AND c.password_hash IS NOT NULL THEN 'hybrid'
        WHEN c.supabase_auth_id IS NOT NULL THEN 'supabase'
        WHEN c.password_hash IS NOT NULL THEN 'custom'
        ELSE 'none'
    END as auth_method,
    (c.password_hash IS NOT NULL) as has_custom_auth,
    (c.supabase_auth_id IS NOT NULL) as has_supabase_auth
FROM clients c;

-- Grant necessary permissions
GRANT SELECT ON user_auth_status TO authenticated;
GRANT SELECT ON user_auth_status TO anon;

-- Add comments for documentation
COMMENT ON COLUMN clients.supabase_auth_id IS 'Links to Supabase Auth user ID for unified authentication';
COMMENT ON COLUMN clients.password_hash IS 'Password hash for custom auth (optional when using Supabase Auth)';
COMMENT ON FUNCTION migrate_user_to_supabase_auth IS 'Helper function to migrate existing users to Supabase Auth';
COMMENT ON FUNCTION check_auth_compatibility IS 'Check which authentication method each user is using';
COMMENT ON VIEW user_auth_status IS 'View showing authentication status and method for all users';

-- Final notice
DO $$ 
BEGIN
    RAISE NOTICE 'Migration script completed successfully!';
    RAISE NOTICE 'You can now use both custom JWT auth and Supabase Auth simultaneously.';
    RAISE NOTICE 'Run SELECT * FROM check_auth_compatibility() to see user auth status.';
END $$; 
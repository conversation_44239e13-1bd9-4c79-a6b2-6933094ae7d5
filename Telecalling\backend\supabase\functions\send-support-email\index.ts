import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
}

interface SupportEmailRequest {
  ticketId: string
  subject: string
  message: string
  urgencyLevel: string
  clientName: string
  clientEmail: string
  businessName: string
  createdAt: string
}

const getEmailTemplate = (data: SupportEmailRequest): string => {
  const urgencyColor = {
    'low': '#10b981',
    'medium': '#f59e0b', 
    'high': '#ef4444',
    'urgent': '#dc2626'
  }[data.urgencyLevel] || '#6b7280'

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Support Ticket</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
        <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700;">🎫 New Support Ticket</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Telecalling Support System</p>
      </div>

      <div style="background: #f8fafc; padding: 25px; border-radius: 12px; border-left: 4px solid ${urgencyColor}; margin-bottom: 25px;">
        <h2 style="margin: 0 0 15px 0; color: #1e293b; font-size: 20px;">${data.subject}</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
          <div>
            <strong style="color: #64748b;">Ticket ID:</strong><br>
            <span style="font-family: monospace; background: #e2e8f0; padding: 4px 8px; border-radius: 4px;">${data.ticketId}</span>
          </div>
          <div>
            <strong style="color: #64748b;">Priority:</strong><br>
            <span style="background: ${urgencyColor}; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase;">${data.urgencyLevel}</span>
          </div>
        </div>
      </div>

      <div style="background: white; padding: 25px; border-radius: 12px; border: 1px solid #e2e8f0; margin-bottom: 25px;">
        <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 18px; border-bottom: 2px solid #f1f5f9; padding-bottom: 10px;">👤 Client Information</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
          <div>
            <strong style="color: #64748b;">Name:</strong><br>
            <span style="color: #1e293b;">${data.clientName}</span>
          </div>
          <div>
            <strong style="color: #64748b;">Email:</strong><br>
            <a href="mailto:${data.clientEmail}" style="color: #667eea; text-decoration: none;">${data.clientEmail}</a>
          </div>
          <div style="grid-column: span 2;">
            <strong style="color: #64748b;">Business:</strong><br>
            <span style="color: #1e293b;">${data.businessName}</span>
          </div>
        </div>
      </div>

      <div style="background: white; padding: 25px; border-radius: 12px; border: 1px solid #e2e8f0; margin-bottom: 25px;">
        <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 18px; border-bottom: 2px solid #f1f5f9; padding-bottom: 10px;">💬 Message</h3>
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; white-space: pre-wrap; font-size: 15px; line-height: 1.6;">${data.message}</div>
      </div>

      <div style="background: #1e293b; color: white; padding: 20px; border-radius: 12px; text-align: center;">
        <p style="margin: 0; font-size: 14px; opacity: 0.8;">
          📅 Created: ${new Date(data.createdAt).toLocaleString()}<br>
          🤖 This email was automatically generated from the Telecalling Support System
        </p>
      </div>

      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
        <p style="color: #64748b; font-size: 14px; margin: 0;">
          Tecnvi AI - Telecalling Support System<br>
          <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
        </p>
      </div>

    </body>
    </html>
  `
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY not found')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const requestData: SupportEmailRequest = await req.json()
    
    console.log('📧 Processing support email request:', {
      ticketId: requestData.ticketId,
      subject: requestData.subject,
      urgencyLevel: requestData.urgencyLevel,
      clientEmail: requestData.clientEmail
    })

    const htmlContent = getEmailTemplate(requestData)
    const subject = `[SUPPORT] ${requestData.urgencyLevel.toUpperCase()} - ${requestData.subject}`

    // Send email using Resend
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Telecalling Support <<EMAIL>>',
        to: ['<EMAIL>'],
        subject: subject,
        html: htmlContent,
        text: `New Support Ticket: ${requestData.subject}\n\nFrom: ${requestData.clientName} (${requestData.clientEmail})\nBusiness: ${requestData.businessName}\nPriority: ${requestData.urgencyLevel}\n\nMessage:\n${requestData.message}\n\nTicket ID: ${requestData.ticketId}\nCreated: ${new Date(requestData.createdAt).toLocaleString()}`
      }),
    })

    if (!emailResponse.ok) {
      const errorData = await emailResponse.text()
      console.error('Resend API error:', errorData)
      return new Response(
        JSON.stringify({ error: 'Failed to send email' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const emailData = await emailResponse.json()
    console.log('✅ Support email sent successfully:', emailData)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Support email sent successfully',
        emailId: emailData.id
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('❌ Error in send-support-email function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

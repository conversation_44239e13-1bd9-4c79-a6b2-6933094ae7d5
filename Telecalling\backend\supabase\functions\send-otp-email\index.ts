import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

interface EmailRequest {
  email: string
  otp: string
  type: 'email_verification' | 'password_reset' | 'login_2fa'
  userName?: string
  businessName?: string
}

// Email templates
const getEmailTemplate = (type: string, otp: string, userName?: string, businessName?: string) => {
  const baseStyles = `
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .container { max-width: 600px; margin: 0 auto; padding: 20px; }
      .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
      .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
      .otp-box { background: white; border: 2px solid #667eea; padding: 20px; text-align: center; border-radius: 10px; margin: 20px 0; }
      .otp-code { font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 8px; }
      .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
      .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
  `

  const templates = {
    email_verification: `
      ${baseStyles}
      <div class="container">
        <div class="header">
          <h1>🎉 Welcome to VoiceBot Platform!</h1>
          <p>Verify your email to get started</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>Thank you for signing up for VoiceBot Platform${businessName ? ` for ${businessName}` : ''}. To complete your registration, please verify your email address.</p>
          
          <div class="otp-box">
            <p><strong>Your verification code is:</strong></p>
            <div class="otp-code">${otp}</div>
          </div>
          
          <p>Enter this code in the verification form to activate your account.</p>
          
          <div class="warning">
            <strong>⚠️ Important:</strong> This code will expire in 10 minutes for security reasons.
          </div>
          
          <p>If you didn't create an account with us, please ignore this email.</p>
        </div>
        <div class="footer">
          <p>VoiceBot Platform - Revolutionizing Communication</p>
          <p>This is an automated message, please do not reply.</p>
        </div>
      </div>
    `,
    
    password_reset: `
      ${baseStyles}
      <div class="container">
        <div class="header">
          <h1>🔒 Password Reset Request</h1>
          <p>Secure your account</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>We received a request to reset your password for your VoiceBot Platform account${businessName ? ` (${businessName})` : ''}.</p>
          
          <div class="otp-box">
            <p><strong>Your password reset code is:</strong></p>
            <div class="otp-code">${otp}</div>
          </div>
          
          <p>Enter this code to reset your password.</p>
          
          <div class="warning">
            <strong>⚠️ Security Notice:</strong> This code will expire in 15 minutes. If you didn't request this password reset, please ignore this email and consider changing your password.
          </div>
          
          <p>For security reasons, this link can only be used once.</p>
        </div>
        <div class="footer">
          <p>VoiceBot Platform - Your Security is Our Priority</p>
          <p>This is an automated message, please do not reply.</p>
        </div>
      </div>
    `,
    
    login_2fa: `
      ${baseStyles}
      <div class="container">
        <div class="header">
          <h1>🔐 Login Verification</h1>
          <p>Two-factor authentication</p>
        </div>
        <div class="content">
          <h2>Hello ${userName || 'there'}!</h2>
          <p>Someone is trying to log in to your VoiceBot Platform account${businessName ? ` (${businessName})` : ''}.</p>
          
          <div class="otp-box">
            <p><strong>Your login verification code is:</strong></p>
            <div class="otp-code">${otp}</div>
          </div>
          
          <p>Enter this code to complete your login.</p>
          
          <div class="warning">
            <strong>⚠️ Security Alert:</strong> This code will expire in 5 minutes. If this wasn't you, please secure your account immediately.
          </div>
          
          <p>If you didn't attempt to log in, please ignore this email and consider changing your password.</p>
        </div>
        <div class="footer">
          <p>VoiceBot Platform - Advanced Security</p>
          <p>This is an automated message, please do not reply.</p>
        </div>
      </div>
    `
  }

  return templates[type] || templates.email_verification
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Validate request method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if Resend API key is configured
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not configured')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { email, otp, type, userName, businessName }: EmailRequest = await req.json()

    // Validate required fields
    if (!email || !otp || !type) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: email, otp, type' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return new Response(
        JSON.stringify({ error: 'Invalid email format' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Validate OTP format (6 digits)
    if (!/^\d{6}$/.test(otp)) {
      return new Response(
        JSON.stringify({ error: 'Invalid OTP format. Must be 6 digits' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Validate type
    const validTypes = ['email_verification', 'password_reset', 'login_2fa']
    if (!validTypes.includes(type)) {
      return new Response(
        JSON.stringify({ error: `Invalid type. Must be one of: ${validTypes.join(', ')}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Prepare email subject and content
    const subjects = {
      email_verification: 'Verify Your Email - VoiceBot Platform',
      password_reset: 'Password Reset Code - VoiceBot Platform',
      login_2fa: 'Login Verification Code - VoiceBot Platform'
    }

    const subject = subjects[type]
    const htmlContent = getEmailTemplate(type, otp, userName, businessName)

    // Send email using Resend
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'VoiceBot Platform <<EMAIL>>',
        to: [email],
        subject: subject,
        html: htmlContent,
        text: `Your ${type.replace('_', ' ')} code is: ${otp}. This code will expire in a few minutes.`
      }),
    })

    if (!emailResponse.ok) {
      const errorData = await emailResponse.text()
      console.error('Resend API error:', errorData)
      return new Response(
        JSON.stringify({ error: 'Failed to send email' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const emailData = await emailResponse.json()
    console.log('Email sent successfully:', emailData)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'OTP email sent successfully',
        emailId: emailData.id
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in send-otp-email function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}) 
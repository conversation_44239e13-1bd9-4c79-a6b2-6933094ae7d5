# 🚀 Complete API Routes Documentation

## 🌐 **Server Configuration**

### **Client Server (Port 5000)**
- **URL**: `http://localhost:5000`
- **Purpose**: Client-facing APIs for dashboard, authentication, and services
- **Authentication**: JWT tokens for clients

### **Admin Panel Server (Port 4000)**
- **URL**: `http://localhost:4000`
- **Purpose**: Admin-only APIs for client management and support
- **Authentication**: JWT tokens for admins (separate from client auth)

---

## 🔐 **Client Server APIs (Port 5000)**

### **Authentication Routes** (`/api/auth`)

#### **POST** `/api/auth/signup`
- **Purpose**: Client registration
- **Body**: `{ username, email, password }`
- **Response**: User data + JWT tokens

#### **POST** `/api/auth/login`
- **Purpose**: Client login
- **Body**: `{ email, password }`
- **Response**: User data + JWT tokens

#### **POST** `/api/auth/google`
- **Purpose**: Google OAuth login
- **Body**: `{ supabaseUser, accessToken, refreshToken }`
- **Response**: User data + JWT tokens

#### **POST** `/api/auth/forgot-password`
- **Purpose**: Request password reset
- **Body**: `{ email }`
- **Response**: Success message

#### **POST** `/api/auth/reset-password`
- **Purpose**: Reset password with token
- **Body**: `{ token, newPassword }`
- **Response**: Success message

---

### **Profile Management** (`/api/profile`)

#### **GET** `/api/profile`
- **Purpose**: Get client profile with plan info
- **Auth**: Required (Client JWT)
- **Response**: Profile data + plan subscription

#### **PUT** `/api/profile`
- **Purpose**: Update client profile
- **Auth**: Required (Client JWT)
- **Body**: `{ username, businessName, businessSummary, businessAddress, ownerName, mobileNumber, businessEmail, whatsappNumber }`
- **Response**: Updated profile data

#### **POST** `/api/profile/reset-password`
- **Purpose**: Reset password (authenticated)
- **Auth**: Required (Client JWT)
- **Body**: `{ currentPassword, newPassword }`
- **Response**: Success message

---

### **Products Management** (`/api/products`)

#### **GET** `/api/products`
- **Purpose**: Get all products for client
- **Auth**: Required (Client JWT)
- **Response**: Array of products

#### **GET** `/api/products/:id`
- **Purpose**: Get single product
- **Auth**: Required (Client JWT)
- **Response**: Product details

#### **POST** `/api/products`
- **Purpose**: Create new product
- **Auth**: Required (Client JWT)
- **Body**: `{ productName, productDetails (JSONB), aliasNames (Array) }`
- **Response**: Created product

#### **PUT** `/api/products/:id`
- **Purpose**: Update product
- **Auth**: Required (Client JWT)
- **Body**: `{ productName, productDetails, aliasNames }`
- **Response**: Updated product

#### **DELETE** `/api/products/:id`
- **Purpose**: Delete product (soft delete)
- **Auth**: Required (Client JWT)
- **Response**: Success message

#### **POST** `/api/products/bulk-upload`
- **Purpose**: Bulk upload products from file
- **Auth**: Required (Client JWT)
- **Body**: FormData with file
- **Response**: Upload status (AI processing placeholder)

---

### **Analytics & Logs** (`/api/analytics`)

#### **GET** `/api/analytics/call-logs`
- **Purpose**: Get paginated call logs
- **Auth**: Required (Client JWT)
- **Query**: `?page=1&limit=50&startDate=2024-01-01&endDate=2024-01-31`
- **Response**: Call logs with pagination

#### **GET** `/api/analytics/sms-logs`
- **Purpose**: Get paginated SMS logs
- **Auth**: Required (Client JWT)
- **Query**: `?page=1&limit=50&startDate=2024-01-01&endDate=2024-01-31`
- **Response**: SMS logs with pagination

#### **GET** `/api/analytics/usage-stats`
- **Purpose**: Get usage statistics
- **Auth**: Required (Client JWT)
- **Response**: Plan usage, current month stats, daily stats

#### **GET** `/api/analytics/dashboard-overview`
- **Purpose**: Get dashboard overview data
- **Auth**: Required (Client JWT)
- **Response**: Plan info, today's stats, week's stats

---

### **Support System** (`/api/support`)

#### **GET** `/api/support`
- **Purpose**: Get client support tickets
- **Auth**: Required (Client JWT)
- **Query**: `?page=1&limit=20&status=open`
- **Response**: Support tickets with pagination

#### **POST** `/api/support`
- **Purpose**: Create new support ticket
- **Auth**: Required (Client JWT)
- **Body**: `{ subject, message, urgencyLevel }`
- **Response**: Created ticket + email notification

#### **GET** `/api/support/:id`
- **Purpose**: Get single support ticket
- **Auth**: Required (Client JWT)
- **Response**: Ticket details

---

## 🔧 **Admin Panel APIs (Port 4000)**

### **Admin Authentication** (`/api/admin/auth`)

#### **POST** `/api/admin/auth/login`
- **Purpose**: Admin login with ID/password
- **Body**: `{ adminId, password }`
- **Response**: Admin data + JWT tokens
- **Default**: `adminId: "admin001", password: "admin123"`

#### **POST** `/api/admin/auth/logout`
- **Purpose**: Admin logout
- **Auth**: Required (Admin JWT)
- **Response**: Success message

#### **POST** `/api/admin/auth/refresh`
- **Purpose**: Refresh admin tokens
- **Body**: `{ refreshToken }`
- **Response**: New tokens

#### **GET** `/api/admin/auth/profile`
- **Purpose**: Get admin profile
- **Auth**: Required (Admin JWT)
- **Response**: Admin profile + assigned clients count

---

### **Admin Panel Management** (`/api/admin/panel`)

#### **GET** `/api/admin/panel/dashboard`
- **Purpose**: Get admin dashboard overview
- **Auth**: Required (Admin JWT)
- **Response**: Assigned clients, active numbers, today's activity

#### **GET** `/api/admin/panel/clients`
- **Purpose**: Get all assigned clients (max 250)
- **Auth**: Required (Admin JWT)
- **Query**: `?page=1&limit=50&search=client&status=active`
- **Response**: Clients with plan info and usage

#### **GET** `/api/admin/panel/clients/:clientId`
- **Purpose**: Get detailed client information
- **Auth**: Required (Admin JWT)
- **Response**: Complete client profile + recent activity

#### **PUT** `/api/admin/panel/clients/:clientId/assign-number`
- **Purpose**: Assign Plivo number to client
- **Auth**: Required (Admin JWT)
- **Body**: `{ plivoNumber }`
- **Response**: Updated client with assigned number

#### **DELETE** `/api/admin/panel/clients/:clientId/remove-number`
- **Purpose**: Remove Plivo number from client
- **Auth**: Required (Admin JWT)
- **Response**: Updated client without number

---

### **Admin Support Management** (`/api/admin/support`)

#### **GET** `/api/admin/support/tickets`
- **Purpose**: Get all support tickets for admin's clients
- **Auth**: Required (Admin JWT)
- **Query**: `?page=1&limit=50&status=open&urgencyLevel=high`
- **Response**: Support tickets with client info

#### **GET** `/api/admin/support/tickets/:ticketId`
- **Purpose**: Get detailed ticket information
- **Auth**: Required (Admin JWT)
- **Response**: Ticket details + client information

#### **PUT** `/api/admin/support/tickets/:ticketId/respond`
- **Purpose**: Respond to support ticket
- **Auth**: Required (Admin JWT)
- **Body**: `{ response, status }`
- **Response**: Updated ticket + email notification to client

#### **GET** `/api/admin/support/stats`
- **Purpose**: Get support statistics
- **Auth**: Required (Admin JWT)
- **Response**: Ticket counts by status and priority

---

## 🚀 **How to Start Both Servers**

### **1. Start Client Server (Port 5000)**
```bash
cd Telecalling/backend
npm install
npm start
```

### **2. Start Admin Panel Server (Port 4000)**
```bash
cd Telecalling/admin-panel
npm install
npm start
```

### **3. Environment Variables**
```env
# Add to both .env files
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret
RESEND_API_KEY=your_resend_api_key
ADMIN_PORT=4000
ADMIN_FRONTEND_URL=http://localhost:3001
```

---

## 🔒 **Security Features**

### **Data Isolation**
- ✅ Clients can only access their own data
- ✅ Admins can only access assigned clients (max 250)
- ✅ Row Level Security (RLS) in database
- ✅ JWT token validation on all protected routes

### **Rate Limiting**
- ✅ Authentication endpoints: 5 requests/15 minutes
- ✅ General API: 100 requests/15 minutes
- ✅ Admin panel: 100 requests/15 minutes

### **Authentication**
- ✅ Separate JWT tokens for clients and admins
- ✅ Session management with expiry
- ✅ Refresh token rotation
- ✅ Password hashing with bcrypt

---

## 📊 **Database Impact Assessment**

### **✅ SAFE - No Impact on Existing Auth**
The new columns added to `clients` table are:
- `business_summary` (TEXT, nullable)
- `business_address` (TEXT, nullable)
- `owner_name` (VARCHAR, nullable)
- `mobile_number` (VARCHAR, nullable)
- `business_email` (VARCHAR, nullable)
- `whatsapp_number` (VARCHAR, nullable)
- `assigned_plivo_number` (VARCHAR, nullable, unique)

**All existing authentication columns remain unchanged:**
- `id`, `email`, `username`, `password_hash`, `is_email_verified`, `is_active`, `created_at`, `updated_at`

---

## 🎯 **Frontend Integration**

### **Client Dashboard** (Port 3000)
- Landing page remains as landing page
- Dashboard accessible after login
- All client APIs integrated
- Real-time data from backend

### **Admin Panel Frontend** (Port 3001 - To be created)
- Separate React app for admin panel
- Admin authentication
- Client management interface
- Support ticket management

This complete API documentation covers all the features you requested! 🚀

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import emailVerificationImage from '../../assets/images/email-verification.jpeg';
import './EmailVerificationModal.css';

const EmailVerificationModal = ({ isOpen, onClose, onVerifySuccess, userEmail }) => {
  const { verifyEmail, resendVerification } = useAuth();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [canResend, setCanResend] = useState(false);
  const [countdown, setCountdown] = useState(60);

  // Countdown timer for resend button
  useEffect(() => {
    let timer;
    if (isOpen && countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0) {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [countdown, isOpen]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setOtp(['', '', '', '', '', '']);
      setError('');
      setSuccess('');
      setCanResend(false);
      setCountdown(60);
    }
  }, [isOpen]);

  const handleOtpChange = (index, value) => {
    if (value.length > 1) return; // Only allow single digit
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    
    // Clear error when user starts typing
    if (error) setError('');

    // Auto focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleKeyDown = (index, e) => {
    // Handle backspace to move to previous input
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const otpValue = otp.join('');
    
    if (otpValue.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }
    
    if (!userEmail) {
      setError('Email address is missing');
      return;
    }
    
    setIsLoading(true);
    setError('');
    
    try {
      const result = await verifyEmail(userEmail, otpValue);
      
      if (result.success) {
        setSuccess('Email verified successfully!');
        setTimeout(() => {
          onVerifySuccess && onVerifySuccess();
        }, 1500);
      } else {
        setError(result.error);
        // Clear OTP on error
        setOtp(['', '', '', '', '', '']);
        // Focus first input
        const firstInput = document.getElementById('otp-0');
        if (firstInput) firstInput.focus();
      }
    } catch (error) {
      console.error('Email verification error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (!canResend || isResending || !userEmail) return;
    
    setIsResending(true);
    setError('');
    
    try {
      const result = await resendVerification(userEmail);
      
      if (result.success) {
        setSuccess('Verification code sent successfully!');
        setCanResend(false);
        setCountdown(60);
        // Clear OTP inputs
        setOtp(['', '', '', '', '', '']);
        // Focus first input
        const firstInput = document.getElementById('otp-0');
        if (firstInput) firstInput.focus();
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.error);
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setError('Failed to resend verification code. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="email-verification-modal-overlay" onClick={onClose}>
      <div 
        className="email-verification-modal-container" 
        onClick={e => e.stopPropagation()}
        style={{'--modal-bg-image': `url(${emailVerificationImage})`}}
      >

        {/* Left side - Form */}
        <div className="modal-form-section">
          <div className="form-content">
            {/* Header */}
            <div className="modal-header">
              <div className="modal-logo">
                <span className="logo-text">VoiceBot Platform</span>
              </div>
              <p className="modal-tagline">Revolutionizing Communication.</p>
            </div>

            {/* Main content */}
            <div className="form-main">
              <h2 className="form-title">Verify Your Email</h2>
              <p className="form-subtitle">
                We've sent a 6-digit verification code to <strong>{userEmail}</strong>
              </p>

              {/* Success message */}
              {success && (
                <div className="success-message">
                  {success}
                </div>
              )}

              {/* Error message */}
              {error && (
                <div className="error-message">
                  {error}
                </div>
              )}

              {/* OTP Form */}
              <form onSubmit={handleSubmit} className="otp-form">
                <div className="otp-inputs">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      id={`otp-${index}`}
                      type="text"
                      maxLength="1"
                      value={digit}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      className="otp-input"
                      disabled={isLoading}
                      autoComplete="off"
                    />
                  ))}
                </div>

                <button 
                  type="submit" 
                  className="submit-btn"
                  disabled={isLoading || otp.join('').length !== 6}
                >
                  {isLoading ? 'Verifying...' : 'Verify Email'}
                </button>
              </form>

              {/* Resend section */}
              <div className="resend-section">
                <p>
                  Didn't receive the code? 
                  {canResend ? (
                    <button 
                      type="button" 
                      className="resend-btn"
                      onClick={handleResendOtp}
                      disabled={isResending}
                    >
                      {isResending ? 'Sending...' : 'Resend Code'}
                    </button>
                  ) : (
                    <span className="countdown">
                      Resend in {countdown}s
                    </span>
                  )}
                </p>
              </div>

              {/* Instructions */}
              <div className="instructions">
                <h4>Having trouble?</h4>
                <ul>
                  <li>Check your spam/junk folder</li>
                  <li>Make sure you entered the correct email address</li>
                  <li>The code expires in 10 minutes</li>
                  <li>Contact support if you continue having issues</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Image */}
        <div className="modal-image-section">
          <img src={emailVerificationImage} alt="Email Verification" className="modal-image" />
          <div className="image-overlay">
            {/* Decorative white shapes */}
            <div className="decorative-shape top-left-shape"></div>
            <div className="decorative-shape bottom-right-shape"></div>
            
            <div className="overlay-content">
              <div className="overlay-card">
                <h3>Almost There!</h3>
                <p>Just one more step to unlock the full power of VoiceBot Platform.</p>
              </div>
              <div className="bottom-overlay">
                <h3>Secure Your Account,<br/>Unlock Your Potential!</h3>
                <p>Verification ensures your account security.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationModal; 
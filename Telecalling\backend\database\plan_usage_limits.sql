-- Plan Usage Limits: Call Minutes & SMS Count
-- Run this in your Supabase SQL editor to enable plan-based limitations

-- 1. Update subscription plans with usage limits
UPDATE subscription_plans SET
    features = jsonb_set(
        jsonb_set(
            COALESCE(features, '{}'),
            '{call_minutes_limit}',
            '"100"'
        ),
        '{sms_limit}',
        '"50"'
    )
WHERE name = 'basic' OR price = 499;

UPDATE subscription_plans SET
    features = jsonb_set(
        jsonb_set(
            COALESCE(features, '{}'),
            '{call_minutes_limit}',
            '"250"'
        ),
        '{sms_limit}',
        '"100"'
    )
WHERE name = 'pro' OR price = 999;

UPDATE subscription_plans SET
    features = jsonb_set(
        jsonb_set(
            COALESCE(features, '{}'),
            '{call_minutes_limit}',
            '"500"'
        ),
        '{sms_limit}',
        '"200"'
    )
WHERE name = 'business' OR price = 1999;

-- Keep enterprise unlimited
UPDATE subscription_plans SET
    features = jsonb_set(
        jsonb_set(
            COALESCE(features, '{}'),
            '{call_minutes_limit}',
            '"-1"'
        ),
        '{sms_limit}',
        '"-1"'
    )
WHERE name = 'enterprise' OR price >= 4999;

-- 2. Create usage tracking table
CREATE TABLE IF NOT EXISTS client_usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES client_subscriptions(id) ON DELETE SET NULL,
    usage_month DATE NOT NULL, -- First day of the month (e.g., 2024-01-01)
    call_minutes_used INTEGER DEFAULT 0,
    sms_count_used INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(client_id, usage_month)
);

-- 3. Function to get current usage for a client
CREATE OR REPLACE FUNCTION get_client_current_usage(p_client_id UUID)
RETURNS JSONB AS $$
DECLARE
    current_month DATE;
    usage_record RECORD;
    plan_limits RECORD;
    result JSONB;
BEGIN
    -- Get current month (first day)
    current_month := DATE_TRUNC('month', NOW())::DATE;
    
    -- Get current usage
    SELECT * INTO usage_record
    FROM client_usage_tracking
    WHERE client_id = p_client_id AND usage_month = current_month;
    
    -- Get plan limits
    SELECT 
        sp.features->>'call_minutes_limit' as call_limit,
        sp.features->>'sms_limit' as sms_limit,
        sp.display_name as plan_name
    INTO plan_limits
    FROM client_subscriptions cs
    JOIN subscription_plans sp ON cs.plan_id = sp.id
    WHERE cs.client_id = p_client_id 
    AND cs.status = 'active'
    ORDER BY cs.created_at DESC
    LIMIT 1;
    
    -- Build result
    result := jsonb_build_object(
        'client_id', p_client_id,
        'current_month', current_month,
        'call_minutes_used', COALESCE(usage_record.call_minutes_used, 0),
        'sms_count_used', COALESCE(usage_record.sms_count_used, 0),
        'call_minutes_limit', CASE WHEN plan_limits.call_limit = '-1' THEN -1 ELSE plan_limits.call_limit::INTEGER END,
        'sms_limit', CASE WHEN plan_limits.sms_limit = '-1' THEN -1 ELSE plan_limits.sms_limit::INTEGER END,
        'plan_name', COALESCE(plan_limits.plan_name, 'Free'),
        'call_minutes_remaining', 
            CASE 
                WHEN plan_limits.call_limit = '-1' THEN -1
                ELSE GREATEST(0, plan_limits.call_limit::INTEGER - COALESCE(usage_record.call_minutes_used, 0))
            END,
        'sms_remaining',
            CASE 
                WHEN plan_limits.sms_limit = '-1' THEN -1
                ELSE GREATEST(0, plan_limits.sms_limit::INTEGER - COALESCE(usage_record.sms_count_used, 0))
            END,
        'call_limit_exceeded', 
            CASE 
                WHEN plan_limits.call_limit = '-1' THEN false
                ELSE COALESCE(usage_record.call_minutes_used, 0) >= plan_limits.call_limit::INTEGER
            END,
        'sms_limit_exceeded',
            CASE 
                WHEN plan_limits.sms_limit = '-1' THEN false
                ELSE COALESCE(usage_record.sms_count_used, 0) >= plan_limits.sms_limit::INTEGER
            END
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 4. Function to check if client can make a call
CREATE OR REPLACE FUNCTION can_client_make_call(
    p_client_id UUID,
    p_estimated_minutes INTEGER DEFAULT 1
)
RETURNS JSONB AS $$
DECLARE
    usage_data JSONB;
    can_call BOOLEAN;
    reason TEXT;
BEGIN
    usage_data := get_client_current_usage(p_client_id);
    
    -- Check if call limit exceeded
    IF (usage_data->>'call_limit_exceeded')::BOOLEAN THEN
        can_call := false;
        reason := 'Monthly call minutes limit exceeded. Please upgrade your plan.';
    ELSIF (usage_data->>'call_minutes_limit')::INTEGER != -1 AND 
          (usage_data->>'call_minutes_used')::INTEGER + p_estimated_minutes > (usage_data->>'call_minutes_limit')::INTEGER THEN
        can_call := false;
        reason := 'This call would exceed your monthly limit. Please upgrade your plan.';
    ELSE
        can_call := true;
        reason := 'Call allowed';
    END IF;
    
    RETURN jsonb_build_object(
        'can_make_call', can_call,
        'reason', reason,
        'usage_data', usage_data
    );
END;
$$ LANGUAGE plpgsql;

-- 5. Function to check if client can send SMS
CREATE OR REPLACE FUNCTION can_client_send_sms(p_client_id UUID)
RETURNS JSONB AS $$
DECLARE
    usage_data JSONB;
    can_send BOOLEAN;
    reason TEXT;
BEGIN
    usage_data := get_client_current_usage(p_client_id);
    
    -- Check if SMS limit exceeded
    IF (usage_data->>'sms_limit_exceeded')::BOOLEAN THEN
        can_send := false;
        reason := 'Monthly SMS limit exceeded. Please upgrade your plan.';
    ELSE
        can_send := true;
        reason := 'SMS allowed';
    END IF;
    
    RETURN jsonb_build_object(
        'can_send_sms', can_send,
        'reason', reason,
        'usage_data', usage_data
    );
END;
$$ LANGUAGE plpgsql;

-- 6. Function to record call usage
CREATE OR REPLACE FUNCTION record_call_usage(
    p_client_id UUID,
    p_call_duration_minutes INTEGER
)
RETURNS JSONB AS $$
DECLARE
    current_month DATE;
    result JSONB;
BEGIN
    current_month := DATE_TRUNC('month', NOW())::DATE;
    
    -- Insert or update usage
    INSERT INTO client_usage_tracking (client_id, usage_month, call_minutes_used, last_updated)
    VALUES (p_client_id, current_month, p_call_duration_minutes, NOW())
    ON CONFLICT (client_id, usage_month)
    DO UPDATE SET 
        call_minutes_used = client_usage_tracking.call_minutes_used + p_call_duration_minutes,
        last_updated = NOW();
    
    -- Return updated usage
    result := get_client_current_usage(p_client_id);
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Call usage recorded',
        'minutes_added', p_call_duration_minutes,
        'updated_usage', result
    );
END;
$$ LANGUAGE plpgsql;

-- 7. Function to record SMS usage
CREATE OR REPLACE FUNCTION record_sms_usage(
    p_client_id UUID,
    p_sms_count INTEGER DEFAULT 1
)
RETURNS JSONB AS $$
DECLARE
    current_month DATE;
    result JSONB;
BEGIN
    current_month := DATE_TRUNC('month', NOW())::DATE;
    
    -- Insert or update usage
    INSERT INTO client_usage_tracking (client_id, usage_month, sms_count_used, last_updated)
    VALUES (p_client_id, current_month, p_sms_count, NOW())
    ON CONFLICT (client_id, usage_month)
    DO UPDATE SET 
        sms_count_used = client_usage_tracking.sms_count_used + p_sms_count,
        last_updated = NOW();
    
    -- Return updated usage
    result := get_client_current_usage(p_client_id);
    
    RETURN jsonb_build_object(
        'success', true,
        'message', 'SMS usage recorded',
        'sms_added', p_sms_count,
        'updated_usage', result
    );
END;
$$ LANGUAGE plpgsql;

-- 8. View for easy usage monitoring
CREATE OR REPLACE VIEW client_usage_summary AS
SELECT 
    c.id as client_id,
    c.business_name,
    c.email,
    sp.display_name as current_plan,
    sp.features->>'call_minutes_limit' as call_limit,
    sp.features->>'sms_limit' as sms_limit,
    COALESCE(cut.call_minutes_used, 0) as call_minutes_used,
    COALESCE(cut.sms_count_used, 0) as sms_count_used,
    CASE 
        WHEN sp.features->>'call_minutes_limit' = '-1' THEN 'Unlimited'
        ELSE (sp.features->>'call_minutes_limit')::INTEGER - COALESCE(cut.call_minutes_used, 0) || ' remaining'
    END as call_status,
    CASE 
        WHEN sp.features->>'sms_limit' = '-1' THEN 'Unlimited'
        ELSE (sp.features->>'sms_limit')::INTEGER - COALESCE(cut.sms_count_used, 0) || ' remaining'
    END as sms_status,
    cs.status as subscription_status,
    cs.ends_at as subscription_expires,
    cut.usage_month,
    cut.last_updated
FROM clients c
LEFT JOIN client_subscriptions cs ON c.id = cs.client_id AND cs.status = 'active'
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
LEFT JOIN client_usage_tracking cut ON c.id = cut.client_id 
    AND cut.usage_month = DATE_TRUNC('month', NOW())::DATE
ORDER BY c.business_name;

-- 9. Reset monthly usage (run on 1st of each month)
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS INTEGER AS $$
DECLARE
    reset_count INTEGER;
BEGIN
    -- Archive old usage data (optional)
    -- DELETE FROM client_usage_tracking WHERE usage_month < DATE_TRUNC('month', NOW() - INTERVAL '3 months')::DATE;
    
    -- Note: New month usage will be created automatically when first call/SMS is made
    -- This function is mainly for cleanup and can be scheduled
    
    GET DIAGNOSTICS reset_count = ROW_COUNT;
    RETURN reset_count;
END;
$$ LANGUAGE plpgsql;

-- 10. Quick testing commands

-- Check a client's current usage:
-- SELECT get_client_current_usage('your-client-id-here');

-- Check if client can make a call:
-- SELECT can_client_make_call('your-client-id-here', 5);

-- Check if client can send SMS:
-- SELECT can_client_send_sms('your-client-id-here');

-- Record call usage (5 minutes):
-- SELECT record_call_usage('your-client-id-here', 5);

-- Record SMS usage:
-- SELECT record_sms_usage('your-client-id-here', 1);

-- View all client usage:
-- SELECT * FROM client_usage_summary;

-- View plan limits:
-- SELECT name, display_name, price, features->>'call_minutes_limit' as call_limit, features->>'sms_limit' as sms_limit FROM subscription_plans;

COMMENT ON TABLE client_usage_tracking IS 'Tracks monthly call minutes and SMS usage per client';
COMMENT ON FUNCTION get_client_current_usage(UUID) IS 'Get current month usage and limits for a client';
COMMENT ON FUNCTION can_client_make_call(UUID, INTEGER) IS 'Check if client can make a call based on usage limits';
COMMENT ON FUNCTION can_client_send_sms(UUID) IS 'Check if client can send SMS based on usage limits';
COMMENT ON VIEW client_usage_summary IS 'Summary view of all client usage and limits';

const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireClient } = require('../middleware/auth');

const router = express.Router();

// Get all invoices for a client
router.get('/', requireClient, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, status, startDate, endDate } = req.query;
  const offset = (page - 1) * limit;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  let query = supabaseAdmin
    .from('invoices')
    .select('*')
    .eq('client_id', client.id)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (status) {
    query = query.eq('status', status);
  }

  if (startDate) {
    query = query.gte('created_at', new Date(startDate).toISOString());
  }

  if (endDate) {
    query = query.lte('created_at', new Date(endDate).toISOString());
  }

  const { data: invoices, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('invoices')
    .select('*', { count: 'exact', head: true })
    .eq('client_id', client.id);

  if (status) {
    countQuery = countQuery.eq('status', status);
  }

  if (startDate) {
    countQuery = countQuery.gte('created_at', new Date(startDate).toISOString());
  }

  if (endDate) {
    countQuery = countQuery.lte('created_at', new Date(endDate).toISOString());
  }

  const { count } = await countQuery;

  res.json({
    invoices: invoices || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Get single invoice
router.get('/:invoiceId', requireClient, asyncHandler(async (req, res) => {
  const { invoiceId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: invoice, error } = await supabaseAdmin
    .from('invoices')
    .select('*')
    .eq('id', invoiceId)
    .eq('client_id', client.id)
    .single();

  if (error) {
    throw error;
  }

  if (!invoice) {
    throw new AppError('Invoice not found', 404);
  }

  res.json({
    invoice
  });
}));

// Create new invoice
router.post('/', requireClient, asyncHandler(async (req, res) => {
  const {
    toNumber,
    customerName,
    customerEmail,
    items,
    subtotal,
    tax = 0,
    discount = 0,
    totalAmount,
    notes,
    dueDate
  } = req.body;

  if (!toNumber || !items || !totalAmount) {
    throw new AppError('Customer number, items, and total amount are required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('*')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Generate invoice number
  const invoiceNumber = await generateInvoiceNumber(client.id);

  const invoiceData = {
    id: uuidv4(),
    client_id: client.id,
    invoice_number: invoiceNumber,
    to_number: toNumber,
    customer_name: customerName || null,
    customer_email: customerEmail || null,
    items: JSON.stringify(items),
    subtotal: parseFloat(subtotal) || 0,
    tax: parseFloat(tax) || 0,
    discount: parseFloat(discount) || 0,
    total_amount: parseFloat(totalAmount),
    notes: notes || null,
    due_date: dueDate ? new Date(dueDate).toISOString() : null,
    status: 'draft',
    created_at: new Date().toISOString()
  };

  const { data: invoice, error } = await supabaseAdmin
    .from('invoices')
    .insert(invoiceData)
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.status(201).json({
    message: 'Invoice created successfully',
    invoice
  });
}));

// Update invoice
router.put('/:invoiceId', requireClient, asyncHandler(async (req, res) => {
  const { invoiceId } = req.params;
  const {
    customerName,
    customerEmail,
    items,
    subtotal,
    tax,
    discount,
    totalAmount,
    notes,
    dueDate,
    status
  } = req.body;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Check if invoice exists and belongs to client
  const { data: existingInvoice } = await supabaseAdmin
    .from('invoices')
    .select('*')
    .eq('id', invoiceId)
    .eq('client_id', client.id)
    .single();

  if (!existingInvoice) {
    throw new AppError('Invoice not found', 404);
  }

  // Don't allow editing paid invoices
  if (existingInvoice.status === 'paid') {
    throw new AppError('Cannot edit paid invoices', 400);
  }

  const updateData = {};
  if (customerName !== undefined) updateData.customer_name = customerName;
  if (customerEmail !== undefined) updateData.customer_email = customerEmail;
  if (items !== undefined) updateData.items = JSON.stringify(items);
  if (subtotal !== undefined) updateData.subtotal = parseFloat(subtotal);
  if (tax !== undefined) updateData.tax = parseFloat(tax);
  if (discount !== undefined) updateData.discount = parseFloat(discount);
  if (totalAmount !== undefined) updateData.total_amount = parseFloat(totalAmount);
  if (notes !== undefined) updateData.notes = notes;
  if (dueDate !== undefined) updateData.due_date = dueDate ? new Date(dueDate).toISOString() : null;
  if (status !== undefined) updateData.status = status;

  const { data: updatedInvoice, error } = await supabaseAdmin
    .from('invoices')
    .update(updateData)
    .eq('id', invoiceId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  res.json({
    message: 'Invoice updated successfully',
    invoice: updatedInvoice
  });
}));

// Send invoice
router.post('/:invoiceId/send', requireClient, asyncHandler(async (req, res) => {
  const { invoiceId } = req.params;
  const { method = 'whatsapp' } = req.body;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('*')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Get invoice
  const { data: invoice, error: invoiceError } = await supabaseAdmin
    .from('invoices')
    .select('*')
    .eq('id', invoiceId)
    .eq('client_id', client.id)
    .single();

  if (invoiceError || !invoice) {
    throw new AppError('Invoice not found', 404);
  }

  try {
    let sentVia = null;

    if (method === 'whatsapp') {
      // Send via WhatsApp
      const message = formatInvoiceMessage(invoice, client);
      // Implementation would call WhatsApp API
      sentVia = 'whatsapp';
    } else if (method === 'email' && invoice.customer_email) {
      // Send via email
      // Implementation would call email service
      sentVia = 'email';
    }

    // Update invoice status
    const { data: updatedInvoice, error } = await supabaseAdmin
      .from('invoices')
      .update({
        status: 'sent',
        sent_at: new Date().toISOString(),
        sent_via: sentVia
      })
      .eq('id', invoiceId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.json({
      message: `Invoice sent successfully via ${sentVia}`,
      invoice: updatedInvoice
    });
  } catch (error) {
    throw new AppError('Failed to send invoice: ' + error.message, 500);
  }
}));

// Mark invoice as paid
router.post('/:invoiceId/mark-paid', requireClient, asyncHandler(async (req, res) => {
  const { invoiceId } = req.params;
  const { paymentMethod, transactionId, paidAmount } = req.body;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: updatedInvoice, error } = await supabaseAdmin
    .from('invoices')
    .update({
      status: 'paid',
      paid_at: new Date().toISOString(),
      payment_method: paymentMethod || null,
      transaction_id: transactionId || null,
      paid_amount: paidAmount ? parseFloat(paidAmount) : null
    })
    .eq('id', invoiceId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!updatedInvoice) {
    throw new AppError('Invoice not found', 404);
  }

  res.json({
    message: 'Invoice marked as paid',
    invoice: updatedInvoice
  });
}));

// Delete invoice
router.delete('/:invoiceId', requireClient, asyncHandler(async (req, res) => {
  const { invoiceId } = req.params;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  const { data: deletedInvoice, error } = await supabaseAdmin
    .from('invoices')
    .delete()
    .eq('id', invoiceId)
    .eq('client_id', client.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!deletedInvoice) {
    throw new AppError('Invoice not found', 404);
  }

  res.json({
    message: 'Invoice deleted successfully'
  });
}));

// Get invoice statistics
router.get('/stats/overview', requireClient, asyncHandler(async (req, res) => {
  const { period = '30d' } = req.query;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(startDate.getDate() - 90);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }

  const { data: invoices, error } = await supabaseAdmin
    .from('invoices')
    .select('*')
    .eq('client_id', client.id)
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString());

  if (error) {
    throw error;
  }

  const invoiceList = invoices || [];

  // Calculate statistics
  const totalInvoices = invoiceList.length;
  const paidInvoices = invoiceList.filter(inv => inv.status === 'paid').length;
  const pendingInvoices = invoiceList.filter(inv => inv.status === 'sent').length;
  const draftInvoices = invoiceList.filter(inv => inv.status === 'draft').length;
  const totalRevenue = invoiceList
    .filter(inv => inv.status === 'paid')
    .reduce((sum, inv) => sum + inv.total_amount, 0);
  const pendingAmount = invoiceList
    .filter(inv => inv.status === 'sent')
    .reduce((sum, inv) => sum + inv.total_amount, 0);

  res.json({
    stats: {
      totalInvoices,
      paidInvoices,
      pendingInvoices,
      draftInvoices,
      totalRevenue,
      pendingAmount,
      paymentRate: totalInvoices > 0 ? (paidInvoices / totalInvoices * 100).toFixed(1) : 0
    }
  });
}));

// Helper function to generate invoice number
async function generateInvoiceNumber(clientId) {
  const { count } = await supabaseAdmin
    .from('invoices')
    .select('*', { count: 'exact', head: true })
    .eq('client_id', clientId);

  const invoiceCount = (count || 0) + 1;
  const currentYear = new Date().getFullYear();
  return `INV-${currentYear}-${invoiceCount.toString().padStart(4, '0')}`;
}

// Helper function to format invoice message
function formatInvoiceMessage(invoice, client) {
  const items = JSON.parse(invoice.items);
  
  let message = `🧾 *Invoice from ${client.shop_name}*\n\n`;
  message += `Invoice #: ${invoice.invoice_number}\n`;
  message += `Date: ${new Date(invoice.created_at).toLocaleDateString()}\n`;
  
  if (invoice.customer_name) {
    message += `Customer: ${invoice.customer_name}\n`;
  }
  
  message += `\n📋 *Items:*\n`;
  items.forEach((item, index) => {
    message += `${index + 1}. ${item.name} - ₹${item.price}${item.quantity ? ` x ${item.quantity}` : ''}\n`;
  });
  
  message += `\n💰 *Total: ₹${invoice.total_amount}*\n`;
  
  if (invoice.due_date) {
    message += `Due Date: ${new Date(invoice.due_date).toLocaleDateString()}\n`;
  }
  
  if (invoice.notes) {
    message += `\n📝 Notes: ${invoice.notes}\n`;
  }
  
  message += `\nThank you for your business!`;
  
  return message;
}

module.exports = router; 
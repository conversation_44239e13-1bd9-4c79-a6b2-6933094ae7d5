import React, { useState, useEffect } from 'react';
import { CreditCard, TrendingUp, Calendar, DollarSign } from 'lucide-react';
import { Link } from 'react-router-dom';
import subscriptionAPI from '../../services/subscriptionAPI';

const PaymentStatsCard = () => {
  const [paymentStats, setPaymentStats] = useState({
    totalSpent: 0,
    lastPayment: null,
    nextBilling: null,
    paymentsCount: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPaymentStats();
  }, []);

  const fetchPaymentStats = async () => {
    try {
      setLoading(true);
      const [subscriptionResponse, paymentsResponse] = await Promise.all([
        subscriptionAPI.getCurrentSubscription(),
        subscriptionAPI.getPaymentHistory(1, 10)
      ]);

      let totalSpent = 0;
      let lastPayment = null;
      let paymentsCount = 0;

      if (paymentsResponse.success && paymentsResponse.payments) {
        const paidPayments = paymentsResponse.payments.filter(p => p.status === 'paid');
        totalSpent = paidPayments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
        lastPayment = paidPayments.length > 0 ? paidPayments[0] : null;
        paymentsCount = paidPayments.length;
      }

      let nextBilling = null;
      if (subscriptionResponse.success && subscriptionResponse.subscription?.ends_at) {
        nextBilling = subscriptionResponse.subscription.ends_at;
      }

      setPaymentStats({
        totalSpent,
        lastPayment,
        nextBilling,
        paymentsCount
      });
    } catch (error) {
      console.error('Error fetching payment stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="animate-pulse">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-6 w-6 bg-gray-200 rounded"></div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-5 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <DollarSign className="h-6 w-6 text-green-600" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Spent</dt>
              <dd className="text-lg font-medium text-gray-900">
                {formatCurrency(paymentStats.totalSpent)}
              </dd>
            </dl>
          </div>
          <div className="flex-shrink-0">
            <div className="text-right">
              <div className="text-sm text-gray-500">
                {paymentStats.paymentsCount} payment{paymentStats.paymentsCount !== 1 ? 's' : ''}
              </div>
              {paymentStats.lastPayment && (
                <div className="text-xs text-gray-400">
                  Last: {new Date(paymentStats.lastPayment.created_at).toLocaleDateString()}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Additional Stats */}
        <div className="mt-4">
          {paymentStats.nextBilling && (
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="w-4 h-4 mr-2" />
              <span>Next billing: {new Date(paymentStats.nextBilling).toLocaleDateString()}</span>
            </div>
          )}
          
          {paymentStats.totalSpent > 0 && (
            <div className="mt-2 flex items-center text-sm text-green-600">
              <TrendingUp className="w-4 h-4 mr-2" />
              <span>Thank you for being a valued customer!</span>
            </div>
          )}
        </div>
      </div>
      
      <div className="bg-gray-50 px-5 py-3">
        <div className="text-sm">
          <Link to="/dashboard/payments" className="text-green-600 hover:text-green-900">
            View payment history
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PaymentStatsCard;

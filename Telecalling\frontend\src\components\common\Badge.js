import React from 'react';

const Badge = ({ children, variant = 'primary', size = 'medium' }) => {
  const variantClasses = {
    primary: 'badge-primary',
    secondary: 'badge-secondary',
    success: 'badge-success',
    warning: 'badge-warning',
    danger: 'badge-danger'
  };

  const sizeClasses = {
    small: 'text-xs px-2 py-1',
    medium: 'text-sm px-3 py-1',
    large: 'text-base px-4 py-2'
  };

  const classes = [
    'badge',
    variantClasses[variant],
    sizeClasses[size]
  ].join(' ');

  return (
    <span className={classes}>
      {children}
    </span>
  );
};

export default Badge; 
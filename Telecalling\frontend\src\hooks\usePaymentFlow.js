import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import subscriptionAPI from '../services/subscriptionAPI';

export const usePaymentFlow = () => {
  const { isAuthenticated, user } = useAuth();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentResult, setPaymentResult] = useState(null);

  useEffect(() => {
    // Check if user just logged in/signed up and has a selected plan
    if (isAuthenticated && user) {
      const storedPlan = localStorage.getItem('selectedPlan');
      if (storedPlan) {
        try {
          const planData = JSON.parse(storedPlan);
          setSelectedPlan(planData);
          setShowPaymentModal(true);
          // Clear the stored plan
          localStorage.removeItem('selectedPlan');
        } catch (error) {
          console.error('Error parsing stored plan:', error);
          localStorage.removeItem('selectedPlan');
        }
      }
    }
  }, [isAuthenticated, user]);

  const openPaymentModal = (plan) => {
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  const closePaymentModal = () => {
    setShowPaymentModal(false);
    setSelectedPlan(null);
  };

  const handlePaymentSuccess = (result) => {
    setPaymentResult(result);
    setShowPaymentModal(false);
    setShowSuccessModal(true);
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    setPaymentResult(null);
    setSelectedPlan(null);
  };

  return {
    showPaymentModal,
    showSuccessModal,
    selectedPlan,
    paymentResult,
    openPaymentModal,
    closePaymentModal,
    handlePaymentSuccess,
    closeSuccessModal
  };
};

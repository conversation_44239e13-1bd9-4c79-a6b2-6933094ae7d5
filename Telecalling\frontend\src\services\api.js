import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Helper functions for token management
const getAccessToken = () => localStorage.getItem('voicebot_access_token');
const getRefreshToken = () => localStorage.getItem('voicebot_refresh_token');
const setTokens = (accessToken, refreshToken) => {
  localStorage.setItem('voicebot_access_token', accessToken);
  localStorage.setItem('voicebot_refresh_token', refreshToken);
};
const clearTokens = () => {
  localStorage.removeItem('voicebot_access_token');
  localStorage.removeItem('voicebot_refresh_token');
  localStorage.removeItem('voicebot_user');
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const refreshResponse = await axios.post(`${api.defaults.baseURL}/auth/refresh-token`, {
          refreshToken: refreshToken
        });
        
        const { accessToken, refreshToken: newRefreshToken } = refreshResponse.data.tokens;
        
        setTokens(accessToken, newRefreshToken);
        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
        
        return api(originalRequest);
      } catch (refreshError) {
        clearTokens();
        // Redirect to login if we're not already there
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  // Sign up new user
  signup: (userData) => api.post('/auth/signup', userData),
  
  // Login user
  login: (credentials) => api.post('/auth/login', credentials),
  
  // Verify email with OTP
  verifyEmail: (data) => api.post('/auth/verify-email', data),
  
  // Resend email verification OTP
  resendVerification: (email) => api.post('/auth/resend-verification', { email }),
  
  // Request password reset
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  
  // Reset password with OTP
  resetPassword: (data) => api.post('/auth/reset-password', data),
  
  // Refresh access token
  refreshToken: (refreshToken) => api.post('/auth/refresh-token', { refreshToken }),
  
  // Logout user
  logout: (refreshToken) => api.post('/auth/logout', { refreshToken }),
  
  // Get current user profile
  getProfile: () => api.get('/auth/me'),
  
  // Update user profile
  updateProfile: (data) => api.put('/auth/profile', data),
  
  // Google OAuth
  googleAuth: (data) => api.post('/auth/google', data),

  // Legacy methods for backward compatibility
  register: (userData) => api.post('/auth/signup', userData),
  changePassword: (data) => api.put('/auth/change-password', data),
};

// Client API
export const clientAPI = {
  create: (data) => api.post('/clients', data),
  getProfile: () => api.get('/clients/profile'),
  updateProfile: (data) => api.put('/clients/profile', data),
  getDashboardOverview: () => api.get('/clients/dashboard/overview'),
};

// Products API
export const productsAPI = {
  getAll: () => api.get('/products'),
  create: (data) => api.post('/products', data),
  update: (id, data) => api.put(`/products/${id}`, data),
  delete: (id) => api.delete(`/products/${id}`),
  bulkImport: (data) => api.post('/products/bulk-import', data),
  getCategories: () => api.get('/products/categories'),
};

// Appointments API
export const appointmentsAPI = {
  getAll: (params) => api.get('/appointments', { params }),
  createSlot: (data) => api.post('/appointments/slots', data),
  book: (slotId, data) => api.post(`/appointments/${slotId}/book`, data),
  cancel: (slotId) => api.put(`/appointments/${slotId}/cancel`),
  delete: (slotId) => api.delete(`/appointments/${slotId}`),
  getAvailable: (clientId, params) => api.get(`/appointments/available/${clientId}`, { params }),
  bulkCreate: (data) => api.post('/appointments/bulk-create', data),
};

// Call Logs API
export const callLogsAPI = {
  getAll: (params) => api.get('/call-logs', { params }),
  getById: (id) => api.get(`/call-logs/${id}`),
  update: (id, data) => api.put(`/call-logs/${id}`, data),
  delete: (id) => api.delete(`/call-logs/${id}`),
  getStats: (params) => api.get('/call-logs/stats/overview', { params }),
};

// WhatsApp API
export const whatsappAPI = {
  send: (data) => api.post('/whatsapp/send', data),
  getLogs: (params) => api.get('/whatsapp/logs', { params }),
  sendEstimate: (data) => api.post('/whatsapp/send-estimate', data),
  sendPaymentLink: (data) => api.post('/whatsapp/send-payment-link', data),
};

// Invoices API
export const invoicesAPI = {
  getAll: (params) => api.get('/invoices', { params }),
  getById: (id) => api.get(`/invoices/${id}`),
  create: (data) => api.post('/invoices', data),
  update: (id, data) => api.put(`/invoices/${id}`, data),
  send: (id, data) => api.post(`/invoices/${id}/send`, data),
  markPaid: (id, data) => api.post(`/invoices/${id}/mark-paid`, data),
  delete: (id) => api.delete(`/invoices/${id}`),
  getStats: (params) => api.get('/invoices/stats/overview', { params }),
};

// Payments API
export const paymentsAPI = {
  getAll: (params) => api.get('/payments', { params }),
  getById: (id) => api.get(`/payments/${id}`),
  createLink: (data) => api.post('/payments/create-link', data),
  updateStatus: (id, data) => api.put(`/payments/${id}/status`, data),
  resend: (id, data) => api.post(`/payments/${id}/resend`, data),
  cancel: (id) => api.post(`/payments/${id}/cancel`),
  getStats: (params) => api.get('/payments/stats/overview', { params }),
};

// Support API
export const supportAPI = {
  getTickets: (params) => api.get('/support', { params }),
  getTicket: (id) => api.get(`/support/${id}`),
  createTicket: (data) => api.post('/support', data),
  updateTicket: (id, data) => api.put(`/support/${id}`, data),
  closeTicket: (id, data) => api.post(`/support/${id}/close`, data),
  reopenTicket: (id, data) => api.post(`/support/${id}/reopen`, data),
  addComment: (id, data) => api.post(`/support/${id}/comments`, data),
  getFAQ: (params) => api.get('/support/faq/items', { params }),
};

// Admin API
export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getClients: (params) => api.get('/admin/clients', { params }),
  getClient: (id) => api.get(`/admin/clients/${id}`),
  updateClientStatus: (id, data) => api.put(`/admin/clients/${id}/status`, data),
  assignNumber: (id, data) => api.post(`/admin/clients/${id}/assign-number`, data),
  getCallLogs: (params) => api.get('/admin/call-logs', { params }),
  getSupportTickets: (params) => api.get('/admin/support-tickets', { params }),
  updateSupportTicket: (id, data) => api.put(`/admin/support-tickets/${id}`, data),
  getStats: (params) => api.get('/admin/stats', { params }),
  getActions: (params) => api.get('/admin/actions', { params }),
};

// Bot Features API
export const botFeaturesAPI = {
  get: () => api.get('/bot-features'),
  update: (data) => api.put('/bot-features', data),
};

// Utility functions
export const uploadFile = async (file, endpoint) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return api.post(endpoint, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const downloadFile = async (url, filename) => {
  const response = await api.get(url, {
    responseType: 'blob',
  });
  
  const blob = new Blob([response.data]);
  const downloadUrl = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(downloadUrl);
};

// Authentication helper functions
export const authHelpers = {
  // Check if user is logged in
  isAuthenticated: () => {
    return !!getAccessToken();
  },
  
  // Get current user from localStorage
  getCurrentUser: () => {
    const userStr = localStorage.getItem('voicebot_user');
    return userStr ? JSON.parse(userStr) : null;
  },
  
  // Set current user in localStorage
  setCurrentUser: (user) => {
    localStorage.setItem('voicebot_user', JSON.stringify(user));
  },
  
  // Clear all auth data
  clearAuth: () => {
    clearTokens();
  },
  
  // Set tokens and user data
  setAuthData: (tokens, user) => {
    setTokens(tokens.accessToken, tokens.refreshToken);
    authHelpers.setCurrentUser(user);
  },
  
  // Check if user email is verified
  isEmailVerified: () => {
    const user = authHelpers.getCurrentUser();
    return user?.isEmailVerified || false;
  },
  
  // Get user's business name
  getBusinessName: () => {
    const user = authHelpers.getCurrentUser();
    return user?.businessName || null;
  },
  
  // Get user's username
  getUsername: () => {
    const user = authHelpers.getCurrentUser();
    return user?.username || null;
  }
};

export default api; 
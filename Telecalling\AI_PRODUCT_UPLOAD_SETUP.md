# 🤖 AI-Powered Product Upload System

## 🎯 **Overview**

This system uses OpenAI's advanced AI models to automatically extract product data from various file types including images, documents, and spreadsheets. The AI generates smart aliases and organizes product information automatically.

## ✨ **Features**

### **🔍 AI Data Extraction**
- **Images**: Uses GPT-4 Vision to analyze product images and extract details
- **Documents**: Processes PDFs, Word docs with text extraction
- **Spreadsheets**: Handles Excel files and CSV data
- **Text Files**: Processes plain text product lists

### **🧠 Smart Alias Generation**
- **Automatic Aliases**: AI generates 2 intelligent alternative names for each product
- **Search Optimization**: Creates searchable terms, slang, and common names
- **Context Aware**: Uses product category and description for better aliases

### **📁 Supported File Types**
- **Images**: JPG, JPEG, PNG, GIF, WebP
- **Documents**: PDF, DOC, DOCX
- **Spreadsheets**: XLSX, XLS, CSV
- **Text**: TXT files

## 🚀 **Setup Instructions**

### **Step 1: Install Dependencies**

```bash
cd Telecalling/backend
chmod +x install_ai_dependencies.sh
./install_ai_dependencies.sh
```

Or install manually:
```bash
npm install openai@^4.0.0 multer@^1.4.5-lts.1 xlsx@^0.18.5 csv-parser@^3.0.0 mammoth@^1.6.0 pdf-parse@^1.1.1
```

### **Step 2: Get OpenAI API Key**

1. **Visit OpenAI**: Go to [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
2. **Create Account**: Sign up or log in to your OpenAI account
3. **Generate Key**: Click "Create new secret key"
4. **Copy Key**: Save the key (starts with `sk-...`)

### **Step 3: Add API Key to Environment**

Add to your `Telecalling/backend/.env` file:
```env
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```

**⚠️ Important**: Replace `sk-your-actual-openai-api-key-here` with your real OpenAI API key!

### **Step 4: Create Upload Directory**

```bash
mkdir -p Telecalling/backend/uploads/products
```

### **Step 5: Restart Backend**

```bash
cd Telecalling/backend
npm start
```

## 🎨 **How to Use**

### **Frontend Interface**

1. **Go to Products Page**: Navigate to Dashboard → Products
2. **Click AI Upload**: Look for the purple "AI Upload" button
3. **Upload Files**: Drag & drop or click to select files
4. **AI Processing**: Watch as AI extracts product data
5. **Review Results**: See extracted products with smart aliases

### **Upload Process**

1. **File Selection**: Choose images, documents, or spreadsheets
2. **AI Analysis**: OpenAI processes each file
3. **Data Extraction**: AI identifies products, prices, categories
4. **Alias Generation**: AI creates 2 smart alternative names
5. **Database Storage**: Products saved with JSONB format

## 🔧 **Technical Details**

### **AI Models Used**
- **GPT-4 Vision**: For image analysis and product extraction
- **GPT-4**: For text processing and alias generation
- **Smart Prompting**: Optimized prompts for accurate extraction

### **Data Flow**
```
File Upload → AI Processing → Data Extraction → Alias Generation → Database Storage
```

### **Database Schema**
```sql
products {
  id: UUID
  client_id: UUID
  product_name: VARCHAR
  product_details: JSONB {
    name: string,
    price: number,
    category: string,
    description: string,
    availability: string
  }
  alias_names: TEXT[] -- [alias1, alias2]
  is_active: BOOLEAN
  created_at: TIMESTAMP
  updated_at: TIMESTAMP
}
```

## 📊 **Example Extractions**

### **From Image**
**Input**: Product photo
**AI Extracts**:
```json
{
  "name": "iPhone 15 Pro",
  "price": 999,
  "category": "Electronics",
  "description": "Latest Apple smartphone with titanium design",
  "availability": "available",
  "alias1": "Apple Phone",
  "alias2": "Latest iPhone"
}
```

### **From Excel/CSV**
**Input**: Spreadsheet with product data
**AI Extracts**: Multiple products with smart categorization and aliases

### **From PDF/Word**
**Input**: Product catalog document
**AI Extracts**: Structured product information with intelligent parsing

## 🎯 **Smart Alias Examples**

| Product Name | AI-Generated Aliases |
|--------------|---------------------|
| "MacBook Pro" | "Apple Laptop", "Mac Computer" |
| "Nike Air Max" | "Nike Sneakers", "Running Shoes" |
| "Samsung Galaxy" | "Android Phone", "Galaxy Device" |
| "Coca Cola" | "Coke", "Cola Drink" |

## 🔍 **API Endpoints**

### **AI Bulk Upload**
```
POST /api/products/ai-bulk-upload
Content-Type: multipart/form-data
Authorization: Bearer <token>

Body: files[] (multiple files)
```

### **Supported Types**
```
GET /api/products/supported-types
```

## 🚨 **Troubleshooting**

### **Common Issues**

1. **"OpenAI not configured"**
   - Check if `OPENAI_API_KEY` is set in `.env`
   - Restart backend after adding key

2. **"Unsupported file type"**
   - Check supported formats list
   - Ensure file extension is correct

3. **"Upload failed"**
   - Check file size (10MB limit)
   - Verify OpenAI API key is valid
   - Check backend logs for errors

### **File Size Limits**
- **Maximum**: 10MB per file
- **Recommended**: Under 5MB for faster processing

### **API Rate Limits**
- OpenAI has rate limits based on your plan
- Consider upgrading for high-volume usage

## 💰 **OpenAI Costs**

### **Estimated Costs** (as of 2024)
- **GPT-4 Vision**: ~$0.01-0.03 per image
- **GPT-4 Text**: ~$0.03 per 1K tokens
- **Typical Upload**: $0.05-0.15 per file

### **Cost Optimization**
- Process files in batches
- Use smaller images when possible
- Monitor usage in OpenAI dashboard

## 🎉 **Success Indicators**

When everything is working correctly:

1. ✅ **AI Upload button appears** in Products page
2. ✅ **Files upload successfully** with progress indicators
3. ✅ **AI extracts product data** from various file types
4. ✅ **Smart aliases generated** for each product
5. ✅ **Products appear** in your product catalog
6. ✅ **Search works** with original names and aliases

## 🔮 **Advanced Features**

- **Batch Processing**: Handle multiple files simultaneously
- **Progress Tracking**: Real-time upload and processing status
- **Error Handling**: Detailed error messages and recovery
- **Smart Categorization**: AI-powered product categorization
- **Duplicate Detection**: Avoid duplicate products (future feature)

The AI-powered product upload system transforms any file into structured product data automatically! 🚀

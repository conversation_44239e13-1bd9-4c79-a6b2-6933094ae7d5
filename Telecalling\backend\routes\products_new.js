const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { dbHelpers } = require('../config/supabase');
const { auth } = require('../middleware/auth');
const fileProcessorService = require('../services/fileProcessorService');
const openaiService = require('../services/openaiService');
const router = express.Router();

// Configure multer for file uploads (memory storage for regular uploads)
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/jpg',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only images, PDF, Word, and Excel files are allowed.'));
    }
  }
});

// Configure multer for AI processing (disk storage)
const aiStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/products');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const aiUpload = multer({
  storage: aiStorage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    if (fileProcessorService.isSupported(file.originalname)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type for AI processing'), false);
    }
  }
});

// Get all products for client
router.get('/', auth, async (req, res) => {
  try {
    const products = await dbHelpers.query(
      'SELECT * FROM products WHERE client_id = $1 AND is_active = true ORDER BY created_at DESC',
      [req.user.id]
    );

    res.json({
      message: 'Products retrieved successfully',
      products: products.map(product => ({
        id: product.id,
        productName: product.product_name,
        productDetails: product.product_details,
        aliasNames: product.alias_names || [],
        isActive: product.is_active,
        createdAt: product.created_at,
        updatedAt: product.updated_at
      }))
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve products',
      message: 'An error occurred while retrieving products'
    });
  }
});

// Create new product
router.post('/', auth, async (req, res) => {
  try {
    const { productName, productDetails, aliasNames } = req.body;

    if (!productName) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Product name is required'
      });
    }

    // Validate productDetails is valid JSON
    let parsedDetails = {};
    if (productDetails) {
      try {
        parsedDetails = typeof productDetails === 'string' 
          ? JSON.parse(productDetails) 
          : productDetails;
      } catch (parseError) {
        return res.status(400).json({ 
          error: 'Validation error',
          message: 'Product details must be valid JSON'
        });
      }
    }

    // Validate aliasNames is array
    let parsedAliasNames = [];
    if (aliasNames) {
      parsedAliasNames = Array.isArray(aliasNames) ? aliasNames : [aliasNames];
    }

    const product = await dbHelpers.insert('products', {
      id: uuidv4(),
      client_id: req.user.id,
      product_name: productName,
      product_details: parsedDetails,
      alias_names: parsedAliasNames,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    });

    res.status(201).json({
      message: 'Product created successfully',
      product: {
        id: product.id,
        productName: product.product_name,
        productDetails: product.product_details,
        aliasNames: product.alias_names || [],
        isActive: product.is_active,
        createdAt: product.created_at,
        updatedAt: product.updated_at
      }
    });

  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({ 
      error: 'Failed to create product',
      message: 'An error occurred while creating product'
    });
  }
});

// Update product
router.put('/:id', auth, async (req, res) => {
  try {
    const { productName, productDetails, aliasNames } = req.body;

    // Check if product exists and belongs to client
    const existingProduct = await dbHelpers.findOne('products', { 
      id: req.params.id,
      client_id: req.user.id
    });

    if (!existingProduct) {
      return res.status(404).json({ 
        error: 'Product not found',
        message: 'Product not found or access denied'
      });
    }

    if (!productName) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Product name is required'
      });
    }

    // Validate productDetails is valid JSON
    let parsedDetails = {};
    if (productDetails) {
      try {
        parsedDetails = typeof productDetails === 'string' 
          ? JSON.parse(productDetails) 
          : productDetails;
      } catch (parseError) {
        return res.status(400).json({ 
          error: 'Validation error',
          message: 'Product details must be valid JSON'
        });
      }
    }

    // Validate aliasNames is array
    let parsedAliasNames = [];
    if (aliasNames) {
      parsedAliasNames = Array.isArray(aliasNames) ? aliasNames : [aliasNames];
    }

    const updatedProduct = await dbHelpers.update('products', {
      product_name: productName,
      product_details: parsedDetails,
      alias_names: parsedAliasNames,
      updated_at: new Date()
    }, { id: req.params.id, client_id: req.user.id });

    res.json({
      message: 'Product updated successfully',
      product: {
        id: updatedProduct.id,
        productName: updatedProduct.product_name,
        productDetails: updatedProduct.product_details,
        aliasNames: updatedProduct.alias_names || [],
        isActive: updatedProduct.is_active,
        createdAt: updatedProduct.created_at,
        updatedAt: updatedProduct.updated_at
      }
    });

  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({ 
      error: 'Failed to update product',
      message: 'An error occurred while updating product'
    });
  }
});

// Bulk delete products (must come before /:id route)
router.delete('/bulk', auth, async (req, res) => {
  try {
    const { productIds } = req.body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'Product IDs array is required'
      });
    }

    // Verify all products exist and belong to client
    const existingProducts = await dbHelpers.query(`
      SELECT id FROM products
      WHERE id = ANY($1) AND client_id = $2 AND is_active = true
    `, [productIds, req.user.id]);

    const existingProductIds = existingProducts.map(p => p.id);
    const notFoundIds = productIds.filter(id => !existingProductIds.includes(id));

    if (notFoundIds.length > 0) {
      return res.status(404).json({
        error: 'Products not found',
        message: `Some products not found or access denied: ${notFoundIds.join(', ')}`
      });
    }

    // Bulk soft delete
    await dbHelpers.query(`
      UPDATE products
      SET is_active = false, updated_at = NOW()
      WHERE id = ANY($1) AND client_id = $2
    `, [productIds, req.user.id]);

    res.json({
      message: `Successfully deleted ${productIds.length} products`,
      deletedCount: productIds.length
    });

  } catch (error) {
    console.error('Bulk delete products error:', error);
    res.status(500).json({
      error: 'Failed to delete products',
      message: 'An error occurred while deleting products'
    });
  }
});

// Delete product (soft delete)
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if product exists and belongs to client
    const existingProduct = await dbHelpers.findOne('products', {
      id: req.params.id,
      client_id: req.user.id
    });

    if (!existingProduct) {
      return res.status(404).json({
        error: 'Product not found',
        message: 'Product not found or access denied'
      });
    }

    // Soft delete
    await dbHelpers.update('products', {
      is_active: false,
      updated_at: new Date()
    }, { id: req.params.id, client_id: req.user.id });

    res.json({
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({
      error: 'Failed to delete product',
      message: 'An error occurred while deleting product'
    });
  }
});

// Bulk upload products from file (placeholder for OpenAI integration)
router.post('/bulk-upload', auth, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        error: 'No file uploaded',
        message: 'Please upload a file'
      });
    }

    // TODO: Integrate with OpenAI to process the file
    // For now, return a placeholder response
    res.json({
      message: 'File uploaded successfully. Processing with AI...',
      fileName: req.file.originalname,
      fileSize: req.file.size,
      fileType: req.file.mimetype,
      note: 'AI processing feature will be implemented soon'
    });

  } catch (error) {
    console.error('Bulk upload error:', error);
    res.status(500).json({ 
      error: 'Failed to upload file',
      message: 'An error occurred while uploading file'
    });
  }
});

// AI-Powered Smart Bulk Upload Route
router.post('/ai-bulk-upload', auth, aiUpload.array('files', 10), async (req, res) => {
  try {
    console.log('🚀 Starting AI-powered bulk upload...');
    console.log('📁 Files received:', req.files?.length || 0);
    console.log('👤 User:', req.user?.email);

    if (!req.files || req.files.length === 0) {
      console.log('❌ No files uploaded');
      return res.status(400).json({
        error: 'No files uploaded',
        message: 'Please select files to upload'
      });
    }

    // Check if OpenAI is configured
    console.log('🔑 OpenAI configured:', openaiService.isConfigured());
    if (!openaiService.isConfigured()) {
      console.log('❌ OpenAI API key not found');
      return res.status(500).json({
        error: 'OpenAI not configured',
        message: 'OpenAI API key is required for AI-powered uploads'
      });
    }

    const results = {
      totalFiles: req.files.length,
      processedFiles: 0,
      totalProducts: 0,
      successfulProducts: 0,
      failedProducts: 0,
      products: [],
      errors: []
    };

    // Process each file
    for (const file of req.files) {
      try {
        console.log(`📁 Processing file: ${file.originalname}`);

        // Extract product data using AI
        const extractedProducts = await fileProcessorService.processFile(file.path, file.originalname);

        results.processedFiles++;
        results.totalProducts += extractedProducts.length;

        // Save each product to database
        for (const productData of extractedProducts) {
          try {
            // Generate additional aliases if not provided
            if (!productData.alias1 || !productData.alias2) {
              const aliases = await openaiService.generateSmartAliases(
                productData.name,
                productData.category,
                productData.description
              );
              productData.alias1 = productData.alias1 || aliases.alias1;
              productData.alias2 = productData.alias2 || aliases.alias2;
            }

            // Prepare product details as JSONB with key-value pairs (excluding name since it's in product_name column)
            const productDetails = {
              price: parseFloat(productData.price) || 0,
              category: productData.category || 'General',
              availability: productData.availability || 'available',
              // Add all features as key-value pairs
              ...(productData.features || {})
            };

            console.log(`📋 Product details for ${productData.name}:`, productDetails);

            // Insert product into database
            const newProduct = await dbHelpers.insert('products', {
              id: uuidv4(),
              client_id: req.user.id,
              product_name: productData.name,
              product_details: productDetails,
              alias_names: [productData.alias1, productData.alias2],
              is_active: true,
              created_at: new Date(),
              updated_at: new Date()
            });

            results.successfulProducts++;
            results.products.push({
              id: newProduct.id,
              name: productData.name,
              category: productData.category,
              price: productData.price,
              aliases: [productData.alias1, productData.alias2],
              source: file.originalname
            });

            console.log(`✅ Product saved: ${productData.name}`);

          } catch (productError) {
            console.error(`❌ Error saving product: ${productData.name}`, productError);
            results.failedProducts++;
            results.errors.push({
              product: productData.name,
              file: file.originalname,
              error: productError.message
            });
          }
        }

        // Clean up uploaded file
        fs.unlinkSync(file.path);

      } catch (fileError) {
        console.error(`❌ Error processing file: ${file.originalname}`, fileError);
        results.errors.push({
          file: file.originalname,
          error: fileError.message
        });

        // Clean up uploaded file
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      }
    }

    console.log('🎉 AI bulk upload completed:', results);

    res.json({
      message: 'AI-powered bulk upload completed',
      results: results
    });

  } catch (error) {
    console.error('❌ AI bulk upload error:', error);

    // Clean up any remaining files
    if (req.files) {
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }

    res.status(500).json({
      error: 'AI bulk upload failed',
      message: 'An error occurred during AI-powered processing'
    });
  }
});

// Get supported file types for frontend
router.get('/supported-types', (req, res) => {
  res.json({
    message: 'Supported file types retrieved',
    types: fileProcessorService.getSupportedTypes()
  });
});

module.exports = router;

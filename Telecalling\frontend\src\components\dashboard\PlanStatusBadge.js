import React, { useState, useEffect } from 'react';
import { Crown, Clock, CheckCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import subscriptionAPI from '../../services/subscriptionAPI';

const PlanStatusBadge = () => {
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSubscription();
  }, []);

  const fetchSubscription = async () => {
    try {
      const response = await subscriptionAPI.getCurrentSubscription();
      if (response.success) {
        setSubscription(response.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-6 w-16 bg-gray-200 rounded"></div>
      </div>
    );
  }

  const currentPlan = subscription?.subscription_plans;
  const isFreePlan = !subscription || currentPlan?.name === 'free';

  const getPlanConfig = () => {
    if (isFreePlan) {
      return {
        icon: Clock,
        text: 'Free',
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        hoverColor: 'hover:bg-gray-200'
      };
    }

    return {
      icon: subscription?.status === 'active' ? Crown : CheckCircle,
      text: currentPlan?.display_name || 'Pro',
      color: subscription?.status === 'active' 
        ? 'bg-blue-100 text-blue-800 border-blue-200' 
        : 'bg-green-100 text-green-800 border-green-200',
      hoverColor: subscription?.status === 'active' 
        ? 'hover:bg-blue-200' 
        : 'hover:bg-green-200'
    };
  };

  const config = getPlanConfig();
  const Icon = config.icon;

  return (
    <Link
      to="/dashboard/payments"
      className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border transition-colors ${config.color} ${config.hoverColor}`}
      title={`Current plan: ${currentPlan?.display_name || 'Free Plan'}`}
    >
      <Icon className="w-3 h-3 mr-1" />
      {config.text}
    </Link>
  );
};

export default PlanStatusBadge;

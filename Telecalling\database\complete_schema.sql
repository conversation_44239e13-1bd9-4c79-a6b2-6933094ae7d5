-- Complete Database Schema for Telecalling Project
-- Run this in Supabase SQL Editor

-- 1. Update clients table with additional profile fields (SAFE - Only adds new columns)
-- These columns are optional and won't affect existing authentication
ALTER TABLE clients
ADD COLUMN IF NOT EXISTS business_summary TEXT,
ADD COLUMN IF NOT EXISTS business_address TEXT,
ADD COLUMN IF NOT EXISTS owner_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS mobile_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS business_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS whatsapp_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS assigned_plivo_number VARCHAR(20) UNIQUE;

-- Ensure existing authentication columns remain unchanged
-- email, username, password_hash, supabase_auth_id, is_email_verified, etc. are NOT modified

-- 2. Create admins table
CREATE TABLE IF NOT EXISTS admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id VARCHAR(50) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    name VA<PERSON>HAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create admin_client_assignments table (each admin manages 250 clients)
CREATE TABLE IF NOT EXISTS admin_client_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID REFERENCES admins(id) ON DELETE CASCADE,
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(client_id) -- Each client can only be assigned to one admin
);

-- 4. Create products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    product_name VARCHAR(255) NOT NULL,
    product_details JSONB DEFAULT '{}',
    alias_names TEXT[], -- Array of alias names
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create call_logs table
CREATE TABLE IF NOT EXISTS call_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    caller_number VARCHAR(20) NOT NULL,
    call_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    duration_minutes INTEGER DEFAULT 0,
    bot_conversation_summary TEXT,
    call_status VARCHAR(50) DEFAULT 'completed', -- completed, failed, missed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create sms_logs table
CREATE TABLE IF NOT EXISTS sms_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    recipient_number VARCHAR(20) NOT NULL,
    message_content TEXT,
    message_summary TEXT,
    file_attachments JSONB DEFAULT '[]', -- Array of file info
    sms_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'sent', -- sent, failed, pending
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create plan_subscriptions table
CREATE TABLE IF NOT EXISTS plan_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    plan_name VARCHAR(100) NOT NULL DEFAULT '₹499 Plan',
    plan_price DECIMAL(10,2) DEFAULT 499.00,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    call_minutes_limit INTEGER DEFAULT 1000,
    sms_limit INTEGER DEFAULT 500,
    calls_limit INTEGER DEFAULT 200,
    call_minutes_used INTEGER DEFAULT 0,
    sms_sent INTEGER DEFAULT 0,
    calls_made INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create support_tickets table
CREATE TABLE IF NOT EXISTS support_tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    admin_id UUID REFERENCES admins(id) ON DELETE SET NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    urgency_level VARCHAR(20) DEFAULT 'medium', -- low, medium, high, urgent
    status VARCHAR(20) DEFAULT 'open', -- open, in-progress, resolved, closed
    client_email VARCHAR(255),
    admin_response TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. Create support_ticket_messages table for chat functionality
CREATE TABLE IF NOT EXISTS support_ticket_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL, -- 'client' or 'admin'
    sender_id UUID NOT NULL, -- client_id or admin_id
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text', -- text, image, file
    attachment_url TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_assigned_plivo_number ON clients(assigned_plivo_number);
CREATE INDEX IF NOT EXISTS idx_admin_client_assignments_admin_id ON admin_client_assignments(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_client_assignments_client_id ON admin_client_assignments(client_id);
CREATE INDEX IF NOT EXISTS idx_products_client_id ON products(client_id);
CREATE INDEX IF NOT EXISTS idx_call_logs_client_id ON call_logs(client_id);
CREATE INDEX IF NOT EXISTS idx_call_logs_timestamp ON call_logs(call_timestamp);
CREATE INDEX IF NOT EXISTS idx_sms_logs_client_id ON sms_logs(client_id);
CREATE INDEX IF NOT EXISTS idx_sms_logs_timestamp ON sms_logs(sms_timestamp);
CREATE INDEX IF NOT EXISTS idx_plan_subscriptions_client_id ON plan_subscriptions(client_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_client_id ON support_tickets(client_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_ticket_id ON support_ticket_messages(ticket_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_sender ON support_ticket_messages(sender_type, sender_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_messages_created_at ON support_ticket_messages(created_at);

-- 10. Insert default admin (password: admin123)
INSERT INTO admins (admin_id, password_hash, name, email, role)
VALUES (
    'admin001',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtGtrOrBiW', -- admin123
    'System Administrator',
    '<EMAIL>',
    'admin'
) ON CONFLICT (admin_id) DO NOTHING;

-- 10.1. Insert default super admin (password: superadmin123)
INSERT INTO admins (admin_id, password_hash, name, email, role)
VALUES (
    'superadmin001',
    'superadmin123', -- Simple password, no bcrypt hashing
    'Super Administrator',
    '<EMAIL>',
    'super_admin'
) ON CONFLICT (admin_id) DO NOTHING;

-- 11. Create default plan subscription for existing clients
INSERT INTO plan_subscriptions (client_id, plan_name, plan_price)
SELECT id, '₹499 Plan', 499.00 
FROM clients 
WHERE NOT EXISTS (
    SELECT 1 FROM plan_subscriptions WHERE client_id = clients.id
);

-- 12. Enable Row Level Security (RLS)
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE sms_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE plan_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

-- 13. Create RLS policies for clients (they can only see their own data)
CREATE POLICY "Clients can view own data" ON clients
    FOR ALL USING (id = auth.uid()::uuid OR id = current_setting('app.current_client_id')::uuid);

CREATE POLICY "Clients can view own products" ON products
    FOR ALL USING (client_id = current_setting('app.current_client_id')::uuid);

CREATE POLICY "Clients can view own call logs" ON call_logs
    FOR ALL USING (client_id = current_setting('app.current_client_id')::uuid);

CREATE POLICY "Clients can view own SMS logs" ON sms_logs
    FOR ALL USING (client_id = current_setting('app.current_client_id')::uuid);

CREATE POLICY "Clients can view own plan subscriptions" ON plan_subscriptions
    FOR ALL USING (client_id = current_setting('app.current_client_id')::uuid);

CREATE POLICY "Clients can view own support tickets" ON support_tickets
    FOR ALL USING (client_id = current_setting('app.current_client_id')::uuid);

-- 14. Create admin_sessions table
CREATE TABLE IF NOT EXISTS admin_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID REFERENCES admins(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin_id ON admin_sessions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_token ON admin_sessions(session_token);

-- Success message
SELECT 'Complete database schema created successfully!' as status;

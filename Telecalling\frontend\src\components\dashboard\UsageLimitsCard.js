import React, { useState, useEffect } from 'react';
import { Phone, MessageSquare, Clock, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react';
import { Link } from 'react-router-dom';
import api from '../../services/api';

const UsageLimitsCard = () => {
  const [usage, setUsage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchUsageData();
  }, []);

  const fetchUsageData = async () => {
    try {
      setLoading(true);
      const response = await api.get('/usage/current');
      
      if (response.data.success) {
        setUsage(response.data.usage);
      } else {
        setError('Failed to load usage data');
      }
    } catch (error) {
      console.error('Error fetching usage:', error);
      setError('Failed to load usage data');
    } finally {
      setLoading(false);
    }
  };

  const getUsagePercentage = (used, limit) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 90) return 'text-red-600 bg-red-100';
    if (percentage >= 75) return 'text-orange-600 bg-orange-100';
    if (percentage >= 50) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getProgressBarColor = (percentage) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (loading) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="animate-pulse">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-6 w-6 bg-gray-200 rounded"></div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-5 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-5">
          <div className="flex items-center text-red-600">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  if (!usage) return null;

  const callPercentage = getUsagePercentage(usage.call_minutes_used, usage.call_minutes_limit);
  const smsPercentage = getUsagePercentage(usage.sms_count_used, usage.sms_limit);

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-gray-900">Usage This Month</h3>
              <p className="text-sm text-gray-500">{usage.plan_name || 'Free Plan'}</p>
            </div>
          </div>
          {(usage.call_limit_exceeded || usage.sms_limit_exceeded) && (
            <div className="flex-shrink-0">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <AlertTriangle className="w-3 h-3 mr-1" />
                Limit Exceeded
              </span>
            </div>
          )}
        </div>

        {/* Call Minutes Usage */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <Phone className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-sm font-medium text-gray-700">Call Minutes</span>
            </div>
            <div className="text-right">
              <span className="text-sm font-semibold text-gray-900">
                {usage.call_minutes_used}
                {usage.call_minutes_limit === -1 ? ' / Unlimited' : ` / ${usage.call_minutes_limit}`}
              </span>
            </div>
          </div>
          
          {usage.call_minutes_limit !== -1 && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(callPercentage)}`}
                style={{ width: `${callPercentage}%` }}
              ></div>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUsageColor(callPercentage)}`}>
              {usage.call_minutes_limit === -1 ? (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Unlimited
                </>
              ) : usage.call_limit_exceeded ? (
                <>
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Limit Exceeded
                </>
              ) : (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  {usage.call_minutes_remaining} remaining
                </>
              )}
            </span>
            {usage.call_minutes_limit !== -1 && (
              <span className="text-xs text-gray-500">
                {callPercentage.toFixed(0)}% used
              </span>
            )}
          </div>
        </div>

        {/* SMS Usage */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <MessageSquare className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-sm font-medium text-gray-700">SMS Messages</span>
            </div>
            <div className="text-right">
              <span className="text-sm font-semibold text-gray-900">
                {usage.sms_count_used}
                {usage.sms_limit === -1 ? ' / Unlimited' : ` / ${usage.sms_limit}`}
              </span>
            </div>
          </div>
          
          {usage.sms_limit !== -1 && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(smsPercentage)}`}
                style={{ width: `${smsPercentage}%` }}
              ></div>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUsageColor(smsPercentage)}`}>
              {usage.sms_limit === -1 ? (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Unlimited
                </>
              ) : usage.sms_limit_exceeded ? (
                <>
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Limit Exceeded
                </>
              ) : (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  {usage.sms_remaining} remaining
                </>
              )}
            </span>
            {usage.sms_limit !== -1 && (
              <span className="text-xs text-gray-500">
                {smsPercentage.toFixed(0)}% used
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Action Footer */}
      <div className="bg-gray-50 px-5 py-3">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">
            Resets on {new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toLocaleDateString()}
          </span>
          {(usage.call_limit_exceeded || usage.sms_limit_exceeded || callPercentage > 80 || smsPercentage > 80) && (
            <Link
              to="/dashboard/payments"
              className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-900"
            >
              <TrendingUp className="w-3 h-3 mr-1" />
              Upgrade Plan
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default UsageLimitsCard;

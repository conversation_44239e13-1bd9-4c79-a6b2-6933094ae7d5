// MNC-Style Session Management System
const { supabaseAdmin } = require('../config/supabase');
const jwt = require('jsonwebtoken');

/**
 * Session management for enterprise-level applications
 * Features:
 * - Persistent sessions (90 days)
 * - Multiple device support
 * - Session tracking and management
 * - Automatic cleanup of expired sessions
 */

class SessionManager {
  
  /**
   * Create a new persistent session
   */
  static async createSession(userId, deviceInfo, sessionType = 'client') {
    try {
      console.log('🔐 Creating persistent session for user:', userId);
      
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const sessionId = `${sessionType}_${timestamp}_${randomId}`;
      
      // Generate long-lived tokens
      const accessToken = jwt.sign(
        {
          userId,
          type: 'access',
          sessionId: sessionId,
          sessionType,
          iat: Math.floor(timestamp / 1000)
        },
        process.env.JWT_SECRET,
        { expiresIn: '30d', noTimestamp: false }
      );

      const refreshToken = jwt.sign(
        {
          userId,
          type: 'refresh',
          sessionId: sessionId,
          sessionType,
          iat: Math.floor(timestamp / 1000)
        },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: '90d', noTimestamp: false }
      );

      // Store session in database
      const sessionData = {
        id: require('uuid').v4(),
        client_id: userId,
        session_id: sessionId,
        session_token: accessToken,
        refresh_token: refreshToken,
        expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        is_active: true,
        session_type: sessionType,
        device_info: deviceInfo,
        ip_address: deviceInfo.ip,
        user_agent: deviceInfo.userAgent,
        created_at: new Date(),
        last_accessed_at: new Date()
      };

      const { data, error } = await supabaseAdmin
        .from('login_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        console.error('❌ Session creation failed:', error);
        return null;
      }

      console.log('✅ Persistent session created successfully');
      return {
        sessionId,
        accessToken,
        refreshToken,
        expiresAt: sessionData.expires_at
      };

    } catch (error) {
      console.error('❌ Session creation error:', error);
      return null;
    }
  }

  /**
   * Get all active sessions for a user
   */
  static async getUserSessions(userId) {
    try {
      const { data: sessions, error } = await supabaseAdmin
        .from('login_sessions')
        .select('*')
        .eq('client_id', userId)
        .eq('is_active', true)
        .order('last_accessed_at', { ascending: false });

      if (error) {
        console.error('❌ Failed to get user sessions:', error);
        return [];
      }

      return sessions.map(session => ({
        sessionId: session.session_id,
        deviceInfo: session.device_info,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        createdAt: session.created_at,
        lastAccessedAt: session.last_accessed_at,
        isCurrent: false // Will be set by caller if needed
      }));

    } catch (error) {
      console.error('❌ Get user sessions error:', error);
      return [];
    }
  }

  /**
   * Update session last accessed time
   */
  static async updateSessionAccess(sessionId) {
    try {
      await supabaseAdmin
        .from('login_sessions')
        .update({
          last_accessed_at: new Date().toISOString()
        })
        .eq('session_id', sessionId)
        .eq('is_active', true);

    } catch (error) {
      console.error('❌ Session access update error:', error);
    }
  }

  /**
   * Invalidate a specific session
   */
  static async invalidateSession(sessionId) {
    try {
      console.log('🔒 Invalidating session:', sessionId);
      
      const { error } = await supabaseAdmin
        .from('login_sessions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('session_id', sessionId);

      if (error) {
        console.error('❌ Session invalidation failed:', error);
        return false;
      }

      console.log('✅ Session invalidated successfully');
      return true;

    } catch (error) {
      console.error('❌ Session invalidation error:', error);
      return false;
    }
  }

  /**
   * Invalidate all sessions for a user (logout from all devices)
   */
  static async invalidateAllUserSessions(userId, exceptSessionId = null) {
    try {
      console.log('🔒 Invalidating all sessions for user:', userId);
      
      let query = supabaseAdmin
        .from('login_sessions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('client_id', userId);

      if (exceptSessionId) {
        query = query.neq('session_id', exceptSessionId);
      }

      const { error } = await query;

      if (error) {
        console.error('❌ All sessions invalidation failed:', error);
        return false;
      }

      console.log('✅ All user sessions invalidated successfully');
      return true;

    } catch (error) {
      console.error('❌ All sessions invalidation error:', error);
      return false;
    }
  }

  /**
   * Clean up expired sessions (run periodically)
   */
  static async cleanupExpiredSessions() {
    try {
      console.log('🧹 Cleaning up expired sessions');
      
      const { data, error } = await supabaseAdmin
        .from('login_sessions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .lt('expires_at', new Date().toISOString())
        .eq('is_active', true)
        .select('id');

      if (error) {
        console.error('❌ Session cleanup failed:', error);
        return 0;
      }

      const cleanedCount = data?.length || 0;
      console.log(`✅ Cleaned up ${cleanedCount} expired sessions`);
      return cleanedCount;

    } catch (error) {
      console.error('❌ Session cleanup error:', error);
      return 0;
    }
  }

  /**
   * Validate session and check if it's still active
   */
  static async validateSession(sessionId) {
    try {
      const { data: session, error } = await supabaseAdmin
        .from('login_sessions')
        .select('*')
        .eq('session_id', sessionId)
        .eq('is_active', true)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !session) {
        return null;
      }

      // Update last accessed time
      await this.updateSessionAccess(sessionId);

      return session;

    } catch (error) {
      console.error('❌ Session validation error:', error);
      return null;
    }
  }

  /**
   * Get session statistics for monitoring
   */
  static async getSessionStats() {
    try {
      const { data: stats, error } = await supabaseAdmin
        .from('login_sessions')
        .select('is_active, session_type, created_at')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days

      if (error) {
        console.error('❌ Session stats error:', error);
        return null;
      }

      const activeSessions = stats.filter(s => s.is_active).length;
      const totalSessions = stats.length;
      const clientSessions = stats.filter(s => s.session_type === 'client').length;
      const adminSessions = stats.filter(s => s.session_type === 'admin').length;

      return {
        activeSessions,
        totalSessions,
        clientSessions,
        adminSessions,
        inactiveSessions: totalSessions - activeSessions
      };

    } catch (error) {
      console.error('❌ Session stats error:', error);
      return null;
    }
  }
}

// Auto-cleanup expired sessions every hour
setInterval(() => {
  SessionManager.cleanupExpiredSessions();
}, 60 * 60 * 1000); // 1 hour

module.exports = SessionManager;

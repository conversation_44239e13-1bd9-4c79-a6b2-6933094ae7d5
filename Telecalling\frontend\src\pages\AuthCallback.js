import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../config/supabase';
import { useAuth } from '../contexts/AuthContext';

const AuthCallback = () => {
  const navigate = useNavigate();
  const { handleGoogleAuthCallback } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const hasProcessed = useRef(false); // Prevent multiple processing

  useEffect(() => {
    // Prevent multiple executions
    if (hasProcessed.current) {
      console.log('🔄 Auth callback already processed, skipping...');
      return;
    }

    const handleAuthCallback = async () => {
      try {
        hasProcessed.current = true; // Mark as processed immediately
        console.log('🔄 Starting Google auth callback processing...');

        // Get the session from URL hash
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth callback error:', error);
          setError('Authentication failed. Please try again.');
          setTimeout(() => navigate('/'), 3000);
          return;
        }

        if (data.session) {
          console.log('Google auth session received:', data.session.user.email);

          // Handle the Google auth callback in AuthContext
          const result = await handleGoogleAuthCallback(data.session);

          if (result.success) {
            console.log('✅ Google authentication completed successfully');

            // Double-check localStorage sync - ensure auth data is stored
            if (result.tokens && result.user) {
              console.log('🔄 Ensuring auth data is properly stored...');
              localStorage.setItem('voicebot_access_token', result.tokens.accessToken);
              localStorage.setItem('voicebot_refresh_token', result.tokens.refreshToken);
              localStorage.setItem('voicebot_user', JSON.stringify(result.user));
              console.log('✅ Auth data manually synced to localStorage');
            }

            // Add a small delay to ensure tokens are properly stored
            setTimeout(() => {
              // Verify auth data is stored before redirecting
              const accessToken = localStorage.getItem('voicebot_access_token');
              const userData = localStorage.getItem('voicebot_user');

              if (accessToken && userData) {
                console.log('✅ Auth data verified, redirecting to dashboard');
                console.log('✅ Access token found:', !!accessToken);
                console.log('✅ User data found:', !!userData);
                navigate('/dashboard/plan499');
              } else {
                console.error('❌ Auth data not found after successful login');
                console.error('❌ Access token:', !!accessToken);
                console.error('❌ User data:', !!userData);
                setError('Authentication data not found. Please try again.');
                setTimeout(() => navigate('/'), 2000);
              }
            }, 1500); // Increased delay to 1.5 seconds
          } else {
            console.error('❌ Google auth callback failed:', result.error);

            // Handle rate limiting specifically
            if (result.error && result.error.includes('wait')) {
              setError('Please wait a moment and try again.');
              setTimeout(() => navigate('/'), 2000);
            } else {
              setError(result.error || 'Failed to complete authentication');
              setTimeout(() => navigate('/'), 3000);
            }
          }
        } else {
          // No session found, redirect to home
          console.log('No session found, redirecting to home');
          navigate('/');
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        setError('An unexpected error occurred');
        setTimeout(() => navigate('/'), 3000);
      } finally {
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [navigate]); // Remove handleGoogleAuthCallback from dependencies

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '3px solid #f3f3f3',
          borderTop: '3px solid #007bbf',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          marginBottom: '20px'
        }}></div>
        <h2>Completing your sign-in...</h2>
        <p>Please wait while we set up your account.</p>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'center'
      }}>
        <div style={{
          backgroundColor: '#fee',
          border: '1px solid #fcc',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '400px'
        }}>
          <h2 style={{ color: '#c33', marginBottom: '10px' }}>Authentication Error</h2>
          <p style={{ color: '#666', marginBottom: '20px' }}>{error}</p>
          <p style={{ color: '#999', fontSize: '14px' }}>Redirecting to home page...</p>
        </div>
      </div>
    );
  }

  return null;
};

export default AuthCallback;

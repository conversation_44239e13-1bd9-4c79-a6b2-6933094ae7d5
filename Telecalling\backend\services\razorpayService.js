const Razorpay = require('razorpay');
const crypto = require('crypto');
const { supabaseAdmin } = require('../config/supabase');
const { v4: uuidv4 } = require('uuid');

class RazorpayService {
  constructor() {
    this.razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    });
  }

  /**
   * Create a Razorpay order for subscription payment
   */
  async createOrder({ amount, currency = 'INR', receipt, notes = {} }) {
    try {
      const options = {
        amount: Math.round(amount * 100), // Convert to paise
        currency,
        receipt: receipt || `order_${Date.now()}`,
        notes,
      };

      const order = await this.razorpay.orders.create(options);
      return {
        success: true,
        order,
      };
    } catch (error) {
      console.error('Razorpay order creation failed:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Verify payment signature
   */
  verifyPaymentSignature({ razorpay_order_id, razorpay_payment_id, razorpay_signature }) {
    try {
      const body = razorpay_order_id + '|' + razorpay_payment_id;
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(body.toString())
        .digest('hex');

      return expectedSignature === razorpay_signature;
    } catch (error) {
      console.error('Payment signature verification failed:', error);
      return false;
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(body, signature) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET)
        .update(body)
        .digest('hex');

      return expectedSignature === signature;
    } catch (error) {
      console.error('Webhook signature verification failed:', error);
      return false;
    }
  }

  /**
   * Create payment transaction record in database
   */
  async createPaymentTransaction({
    clientId,
    subscriptionId,
    razorpayOrderId,
    amount,
    currency = 'INR',
    description,
    metadata = {}
  }) {
    try {
      const { data, error } = await supabaseAdmin
        .from('payment_transactions')
        .insert({
          id: uuidv4(),
          client_id: clientId,
          subscription_id: subscriptionId,
          razorpay_order_id: razorpayOrderId,
          amount,
          currency,
          description,
          metadata,
          status: 'created',
        })
        .select()
        .single();

      if (error) throw error;

      return {
        success: true,
        transaction: data,
      };
    } catch (error) {
      console.error('Failed to create payment transaction:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update payment transaction status
   */
  async updatePaymentTransaction({
    razorpayOrderId,
    razorpayPaymentId,
    razorpaySignature,
    status,
    paymentMethod,
    metadata = {}
  }) {
    try {
      const updateData = {
        status,
        updated_at: new Date().toISOString(),
        metadata: { ...metadata }
      };

      if (razorpayPaymentId) {
        updateData.razorpay_payment_id = razorpayPaymentId;
      }

      if (razorpaySignature) {
        updateData.razorpay_signature = razorpaySignature;
      }

      if (paymentMethod) {
        updateData.payment_method = paymentMethod;
      }

      const { data, error } = await supabaseAdmin
        .from('payment_transactions')
        .update(updateData)
        .eq('razorpay_order_id', razorpayOrderId)
        .select()
        .single();

      if (error) throw error;

      return {
        success: true,
        transaction: data,
      };
    } catch (error) {
      console.error('Failed to update payment transaction:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Create or update client subscription
   */
  async createClientSubscription({
    clientId,
    planId,
    transactionId,
    billingPeriod = 'monthly'
  }) {
    try {
      // Calculate subscription end date
      const startDate = new Date();
      const endDate = new Date();
      
      if (billingPeriod === 'yearly') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        endDate.setMonth(endDate.getMonth() + 1);
      }

      // Create subscription record
      const { data: subscription, error: subError } = await supabaseAdmin
        .from('client_subscriptions')
        .insert({
          id: uuidv4(),
          client_id: clientId,
          plan_id: planId,
          status: 'active',
          starts_at: startDate.toISOString(),
          ends_at: endDate.toISOString(),
        })
        .select()
        .single();

      if (subError) throw subError;

      // Update client's current plan
      const { error: clientError } = await supabaseAdmin
        .from('clients')
        .update({
          current_plan_id: planId,
          subscription_status: 'active',
          subscription_ends_at: endDate.toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', clientId);

      if (clientError) throw clientError;

      // Update payment transaction with subscription ID
      if (transactionId) {
        await supabaseAdmin
          .from('payment_transactions')
          .update({ subscription_id: subscription.id })
          .eq('id', transactionId);
      }

      return {
        success: true,
        subscription,
      };
    } catch (error) {
      console.error('Failed to create client subscription:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get subscription plans
   */
  async getSubscriptionPlans() {
    try {
      const { data, error } = await supabaseAdmin
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;

      return {
        success: true,
        plans: data,
      };
    } catch (error) {
      console.error('Failed to get subscription plans:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get client's current subscription
   */
  async getClientSubscription(clientId) {
    try {
      const { data, error } = await supabaseAdmin
        .from('client_subscriptions')
        .select(`
          *,
          subscription_plans (*)
        `)
        .eq('client_id', clientId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      return {
        success: true,
        subscription: data,
      };
    } catch (error) {
      console.error('Failed to get client subscription:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Cancel subscription (for dev mode undo)
   */
  async cancelSubscription(clientId, subscriptionId) {
    try {
      // Update subscription status
      const { error: subError } = await supabaseAdmin
        .from('client_subscriptions')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString(),
        })
        .eq('id', subscriptionId)
        .eq('client_id', clientId);

      if (subError) throw subError;

      // Get free plan
      const { data: freePlan } = await supabaseAdmin
        .from('subscription_plans')
        .select('id')
        .eq('name', 'free')
        .single();

      // Reset client to free plan
      const { error: clientError } = await supabaseAdmin
        .from('clients')
        .update({
          current_plan_id: freePlan?.id || null,
          subscription_status: 'free',
          subscription_ends_at: null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', clientId);

      if (clientError) throw clientError;

      return {
        success: true,
        message: 'Subscription cancelled successfully',
      };
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

module.exports = new RazorpayService();

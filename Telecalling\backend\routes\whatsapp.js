const express = require('express');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireClient } = require('../middleware/auth');

const router = express.Router();

// Send WhatsApp message
router.post('/send', requireClient, asyncHandler(async (req, res) => {
  const { toNumber, messageType, content, templateName, templateParams } = req.body;

  if (!toNumber || !content) {
    throw new AppError('Recipient number and content are required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  try {
    // Send message via WhatsApp Business API
    const whatsappResponse = await sendWhatsAppMessage({
      to: toNumber,
      type: messageType || 'text',
      content,
      templateName,
      templateParams
    });

    // Log the message
    const { data: logEntry, error } = await supabaseAdmin
      .from('whatsapp_logs')
      .insert({
        id: uuidv4(),
        client_id: client.id,
        to_number: toNumber,
        message_type: messageType || 'text',
        content,
        status: 'sent',
        whatsapp_message_id: whatsappResponse.messageId,
        timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      message: 'WhatsApp message sent successfully',
      messageId: whatsappResponse.messageId,
      logEntry
    });
  } catch (error) {
    // Log failed message
    await supabaseAdmin
      .from('whatsapp_logs')
      .insert({
        id: uuidv4(),
        client_id: client.id,
        to_number: toNumber,
        message_type: messageType || 'text',
        content,
        status: 'failed',
        error_message: error.message,
        timestamp: new Date().toISOString()
      });

    throw new AppError('Failed to send WhatsApp message: ' + error.message, 500);
  }
}));

// Get WhatsApp message logs
router.get('/logs', requireClient, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, status, messageType, startDate, endDate } = req.query;
  const offset = (page - 1) * limit;

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('id')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  let query = supabaseAdmin
    .from('whatsapp_logs')
    .select('*')
    .eq('client_id', client.id)
    .order('timestamp', { ascending: false })
    .range(offset, offset + limit - 1);

  if (status) {
    query = query.eq('status', status);
  }

  if (messageType) {
    query = query.eq('message_type', messageType);
  }

  if (startDate) {
    query = query.gte('timestamp', new Date(startDate).toISOString());
  }

  if (endDate) {
    query = query.lte('timestamp', new Date(endDate).toISOString());
  }

  const { data: logs, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('whatsapp_logs')
    .select('*', { count: 'exact', head: true })
    .eq('client_id', client.id);

  if (status) {
    countQuery = countQuery.eq('status', status);
  }

  if (messageType) {
    countQuery = countQuery.eq('message_type', messageType);
  }

  if (startDate) {
    countQuery = countQuery.gte('timestamp', new Date(startDate).toISOString());
  }

  if (endDate) {
    countQuery = countQuery.lte('timestamp', new Date(endDate).toISOString());
  }

  const { count } = await countQuery;

  res.json({
    logs: logs || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Send estimate via WhatsApp
router.post('/send-estimate', requireClient, asyncHandler(async (req, res) => {
  const { toNumber, items, totalAmount, notes } = req.body;

  if (!toNumber || !items || !totalAmount) {
    throw new AppError('Recipient number, items, and total amount are required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('*')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Format estimate message
  const estimateMessage = formatEstimateMessage({
    shopName: client.shop_name,
    items,
    totalAmount,
    notes
  });

  try {
    // Send estimate via WhatsApp
    const whatsappResponse = await sendWhatsAppMessage({
      to: toNumber,
      type: 'text',
      content: estimateMessage
    });

    // Log the message
    const { data: logEntry, error } = await supabaseAdmin
      .from('whatsapp_logs')
      .insert({
        id: uuidv4(),
        client_id: client.id,
        to_number: toNumber,
        message_type: 'estimate',
        content: estimateMessage,
        status: 'sent',
        whatsapp_message_id: whatsappResponse.messageId,
        timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      message: 'Estimate sent successfully via WhatsApp',
      messageId: whatsappResponse.messageId,
      logEntry
    });
  } catch (error) {
    throw new AppError('Failed to send estimate: ' + error.message, 500);
  }
}));

// Send payment link via WhatsApp
router.post('/send-payment-link', requireClient, asyncHandler(async (req, res) => {
  const { toNumber, amount, description, paymentLink } = req.body;

  if (!toNumber || !amount || !paymentLink) {
    throw new AppError('Recipient number, amount, and payment link are required', 400);
  }

  // Get client ID
  const { data: client } = await supabaseAdmin
    .from('clients')
    .select('*')
    .eq('user_id', req.user.id)
    .single();

  if (!client) {
    throw new AppError('Client profile not found', 404);
  }

  // Format payment message
  const paymentMessage = formatPaymentMessage({
    shopName: client.shop_name,
    amount,
    description,
    paymentLink
  });

  try {
    // Send payment link via WhatsApp
    const whatsappResponse = await sendWhatsAppMessage({
      to: toNumber,
      type: 'text',
      content: paymentMessage
    });

    // Log the message
    const { data: logEntry, error } = await supabaseAdmin
      .from('whatsapp_logs')
      .insert({
        id: uuidv4(),
        client_id: client.id,
        to_number: toNumber,
        message_type: 'payment',
        content: paymentMessage,
        status: 'sent',
        whatsapp_message_id: whatsappResponse.messageId,
        timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.status(201).json({
      message: 'Payment link sent successfully via WhatsApp',
      messageId: whatsappResponse.messageId,
      logEntry
    });
  } catch (error) {
    throw new AppError('Failed to send payment link: ' + error.message, 500);
  }
}));

// WhatsApp webhook for message status updates
router.post('/webhook', asyncHandler(async (req, res) => {
  const { entry } = req.body;

  if (!entry || !entry[0]) {
    return res.status(200).json({ success: true });
  }

  const changes = entry[0].changes;
  if (!changes || !changes[0]) {
    return res.status(200).json({ success: true });
  }

  const change = changes[0];
  if (change.field === 'messages') {
    const statuses = change.value.statuses;
    
    if (statuses && statuses.length > 0) {
      // Update message status in database
      for (const status of statuses) {
        await supabaseAdmin
          .from('whatsapp_logs')
          .update({
            status: status.status,
            delivered_at: status.status === 'delivered' ? new Date().toISOString() : null,
            read_at: status.status === 'read' ? new Date().toISOString() : null
          })
          .eq('whatsapp_message_id', status.id);
      }
    }
  }

  res.status(200).json({ success: true });
}));

// WhatsApp webhook verification
router.get('/webhook', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  if (mode === 'subscribe' && token === process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN) {
    res.status(200).send(challenge);
  } else {
    res.status(403).send('Forbidden');
  }
});

// Helper function to send WhatsApp message
async function sendWhatsAppMessage({ to, type, content, templateName, templateParams }) {
  const accessToken = process.env.WHATSAPP_ACCESS_TOKEN;
  const phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;

  if (!accessToken || !phoneNumberId) {
    throw new Error('WhatsApp configuration missing');
  }

  const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;

  let messageData = {
    messaging_product: 'whatsapp',
    to: to.replace(/\D/g, ''), // Remove non-digits
    type: type
  };

  if (type === 'text') {
    messageData.text = { body: content };
  } else if (type === 'template') {
    messageData.template = {
      name: templateName,
      language: { code: 'en' },
      components: templateParams ? [{
        type: 'body',
        parameters: templateParams
      }] : []
    };
  }

  const response = await axios.post(url, messageData, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return {
    messageId: response.data.messages[0].id,
    status: 'sent'
  };
}

// Helper function to format estimate message
function formatEstimateMessage({ shopName, items, totalAmount, notes }) {
  let message = `📋 *Estimate from ${shopName}*\n\n`;
  
  items.forEach((item, index) => {
    message += `${index + 1}. ${item.name} - ₹${item.price}${item.quantity ? ` x ${item.quantity}` : ''}\n`;
  });
  
  message += `\n💰 *Total: ₹${totalAmount}*\n`;
  
  if (notes) {
    message += `\n📝 Notes: ${notes}\n`;
  }
  
  message += `\nThank you for your interest! Please let us know if you have any questions.`;
  
  return message;
}

// Helper function to format payment message
function formatPaymentMessage({ shopName, amount, description, paymentLink }) {
  let message = `💳 *Payment Request from ${shopName}*\n\n`;
  message += `Amount: ₹${amount}\n`;
  
  if (description) {
    message += `Description: ${description}\n`;
  }
  
  message += `\n🔗 Pay now: ${paymentLink}\n\n`;
  message += `Thank you for your business!`;
  
  return message;
}

module.exports = router; 
/* Landing Page Styles */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #10b981;
  --accent-color: #8b5cf6;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-accent: #f0f9ff;
  --border-color: #e5e7eb;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  overflow-x: hidden;
}

/* Header Styles */
.landing-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.landing-header.scrolled {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
}

.logo-icon {
  width: 32px;
  height: 32px;
  margin-right: 0.5rem;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Mobile Menu */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu.active {
  opacity: 1;
  visibility: visible;
}

.mobile-menu-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: white;
  padding: 2rem;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.mobile-menu.active .mobile-menu-content {
  transform: translateX(0);
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.mobile-menu-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
}

.mobile-nav {
  list-style: none;
  margin-bottom: 2rem;
}

.mobile-nav li {
  margin-bottom: 1rem;
}

.mobile-nav a {
  display: block;
  padding: 1rem 0;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
  transition: color 0.3s ease;
}

.mobile-nav a:hover {
  color: var(--primary-color);
}

.mobile-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Hero Section */
.hero-section {
  background: #ffffff;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 70px;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  animation: fadeInUp 1s ease-out;
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05),
    0 8px 16px rgba(0, 0, 0, 0.03);
  position: relative;
  z-index: 3;
}

.hero-title span {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 
    0 2px 4px rgba(59, 130, 246, 0.3),
    0 4px 8px rgba(139, 92, 246, 0.2),
    0 8px 16px rgba(6, 182, 212, 0.1);
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2vw, 1.25rem);
  color: #1e293b;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeInUp 1s ease-out 0.2s both;
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.15),
    0 4px 8px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 3;
  font-weight: 600;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-btn {
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
}

.hero-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  color: white;
  box-shadow: 
    0 4px 14px rgba(59, 130, 246, 0.4),
    0 8px 25px rgba(139, 92, 246, 0.3),
    0 12px 35px rgba(6, 182, 212, 0.2);
  border: none;
  position: relative;
  z-index: 3;
}

.hero-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.5),
    0 12px 35px rgba(139, 92, 246, 0.4),
    0 16px 45px rgba(6, 182, 212, 0.3);
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 50%, #0891b2 100%);
  color: white;
}

.hero-btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #1e293b;
  border: 2px solid rgba(59, 130, 246, 0.2);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow: 
    0 4px 14px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 3;
  font-weight: 600;
}

.hero-btn-secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-3px);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08);
  color: #1e293b;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 3rem;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 3;
  background: rgba(255, 255, 255, 0.7);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.hero-feature-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
  filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3));
}

/* Floating Elements Animation */
.floating-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 50%, rgba(6, 182, 212, 0.1) 100%);
  animation: float 6s ease-in-out infinite;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.floating-element:nth-child(1) {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element:nth-child(2) {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.floating-element:nth-child(3) {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}



/* Features Section */
.features-section {
  padding: 4rem 0 6rem 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  margin-top: -2rem;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 2;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.feature-icon {
  width: 48px;
  height: 48px;
  padding: 12px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works-section {
  padding: 4rem 0 6rem 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  margin-top: -2rem;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-top: 4rem;
}

.step-card {
  text-align: center;
  position: relative;
}

.step-number {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0 auto 1.5rem;
  position: relative;
  z-index: 2;
}

.step-number.bg-primary { background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); }
.step-number.bg-secondary { background: linear-gradient(135deg, var(--secondary-color), #059669); }
.step-number.bg-accent { background: linear-gradient(135deg, var(--accent-color), #7c3aed); }

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.step-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Pricing Section */
.pricing-section {
  padding: 4rem 0 6rem 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  margin-top: -2rem;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.pricing-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: var(--shadow-md);
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pricing-card.popular {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.pricing-card.popular::before {
  content: 'Most Popular';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: var(--primary-color);
  color: white;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.pricing-card.popular:hover {
  transform: scale(1.05) translateY(-8px);
}

.pricing-header {
  text-align: center;
  margin-bottom: 2rem;
}

.pricing-card.popular .pricing-header {
  padding-top: 1.5rem;
}

.pricing-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.pricing-price {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.pricing-period {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.pricing-description {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.pricing-features {
  list-style: none;
  margin-bottom: 2rem;
}

.pricing-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
}

.pricing-feature-icon {
  width: 20px;
  height: 20px;
  color: var(--secondary-color);
  flex-shrink: 0;
}

.pricing-cta {
  width: 100%;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  display: block;
  transition: all 0.3s ease;
}

.pricing-cta.primary {
  background: var(--primary-color);
  color: white;
}

.pricing-cta.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

.pricing-cta.outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.pricing-cta.outline:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Testimonials Section */
.testimonials-section {
  padding: 4rem 0 6rem 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  margin-top: -2rem;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.testimonial-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 4rem;
  color: var(--primary-color);
  opacity: 0.2;
  font-family: serif;
}

.testimonial-text {
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.testimonial-author {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.testimonial-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.testimonial-business {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.testimonial-location {
  color: var(--primary-color);
  font-size: 0.875rem;
  font-weight: 500;
}

/* CTA Section */
.cta-section {
  padding: 4rem 0 6rem 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  margin-top: -2rem;
}

.cta-container {
  position: relative;
  z-index: 2;
  text-align: center;
}

.cta-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
}

.cta-subtitle {
  font-size: 1.25rem;
  color: #475569;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
}

.cta-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  color: white;
  box-shadow: 
    0 4px 14px rgba(59, 130, 246, 0.4),
    0 8px 25px rgba(139, 92, 246, 0.3);
}

.cta-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 8px 25px rgba(59, 130, 246, 0.5),
    0 12px 35px rgba(139, 92, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 50%, #0891b2 100%);
  color: white;
}

.cta-btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #1e293b;
  border: 2px solid rgba(59, 130, 246, 0.2);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
}

.cta-btn-secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  color: #1e293b;
}

/* Footer */
.footer {
  background: var(--text-primary);
  color: white;
  padding: 4rem 0 2rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-brand {
  margin-bottom: 1rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.footer-description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

.footer-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.footer-links {
  list-style: none;
}

.footer-link {
  margin-bottom: 0.5rem;
}

.footer-link a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-features {
    flex-direction: column;
    align-items: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .steps-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-card.popular {
    transform: none;
  }
  
  .pricing-card.popular:hover {
    transform: translateY(-8px);
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .floating-element {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .pricing-card {
    padding: 1.5rem;
  }
  
  .hero-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }
  
  .cta-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
} 

.hero-graph-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.graph-line {
  fill: none;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.25;
  stroke-dasharray: 2000;
  stroke-dashoffset: 2000;
  animation: graphLineGrow 2.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.graph-line-1 {
  stroke: #3b82f6; /* blue */
  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3)) drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
  animation-delay: 0.1s;
}
.graph-line-2 {
  stroke: #8b5cf6; /* purple */
  filter: drop-shadow(0 2px 4px rgba(139, 92, 246, 0.3)) drop-shadow(0 4px 8px rgba(139, 92, 246, 0.2));
  animation-delay: 0.4s;
}
.graph-line-3 {
  stroke: #06b6d4; /* cyan */
  filter: drop-shadow(0 2px 4px rgba(6, 182, 212, 0.3)) drop-shadow(0 4px 8px rgba(6, 182, 212, 0.2));
  animation-delay: 0.7s;
}

@keyframes graphLineGrow {
  to {
    stroke-dashoffset: 0;
  }
}

@media (max-width: 768px) {
  .hero-graph-bg {
    height: 100%;
    min-height: 320px;
  }
  .graph-line {
    stroke-width: 4;
    opacity: 0.3;
  }
} 
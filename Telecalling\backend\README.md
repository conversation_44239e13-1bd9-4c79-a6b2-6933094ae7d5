# VoiceBot Platform - Backend API

Express.js backend API for the VoiceBot Telecalling platform with Supabase integration.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Setup environment
cp env.example .env
# Edit .env with your configuration

# Start development server
npm run dev

# Or start production server
npm start
```

## 📋 Environment Configuration

### Required Environment Variables

Create a `.env` file from `env.example`:

```bash
cp env.example .env
```

**Essential Variables (Required):**
```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# JWT Configuration
JWT_SECRET=your_secure_jwt_secret_min_32_chars
```

**Server Configuration:**
```env
PORT=5000
NODE_ENV=development
```

### Optional Integrations

**Voice Calling (Plivo):**
```env
PLIVO_AUTH_ID=your_plivo_auth_id
PLIVO_AUTH_TOKEN=your_plivo_auth_token
PLIVO_WEBHOOK_URL=https://your-domain.com/api/plivo-webhook
```

**WhatsApp Business:**
```env
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token
```

**Payment Gateway (Razorpay):**
```env
RAZORPAY_KEY_ID=your_key_id
RAZORPAY_KEY_SECRET=your_key_secret
```

## 🗃️ Database Setup

### Supabase Configuration

1. **Create Supabase Project:**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Copy project URL and keys

2. **Run Database Schema:**
   ```sql
   -- Execute the SQL in database/schema.sql in Supabase SQL editor
   ```

3. **Configure RLS (Row Level Security):**
   - Enable RLS on all tables
   - Set appropriate policies for user access

## 📡 API Endpoints

### Authentication Routes (`/api/auth`)
- `POST /api/auth/login` - User login
- `POST /api/auth/signup` - User registration
- `POST /api/auth/verify-email` - Email verification
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/reset-password` - Password reset
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Protected Routes (Require Authentication)
- `/api/clients` - Client management
- `/api/products` - Product catalog
- `/api/appointments` - Appointment scheduling
- `/api/call-logs` - Call history
- `/api/whatsapp` - WhatsApp messaging
- `/api/invoices` - Invoice management
- `/api/payments` - Payment processing
- `/api/admin` - Admin operations
- `/api/support` - Support tickets

### Public Routes
- `/api/plivo` - Plivo webhooks
- `/health` - Health check

## 🛠️ Development

### Available Scripts

```bash
# Development with auto-reload
npm run dev

# Production start
npm start

# Run tests
npm test
```

### Project Structure

```
backend/
├── config/
│   └── supabase.js         # Database configuration
├── database/
│   └── schema.sql          # Database schema
├── middleware/
│   ├── auth.js            # Authentication middleware
│   └── errorHandler.js    # Error handling
├── routes/
│   ├── auth.js            # Authentication routes
│   ├── clients.js         # Client management
│   ├── products.js        # Product catalog
│   ├── appointments.js    # Appointments
│   ├── callLogs.js        # Call logs
│   ├── whatsapp.js        # WhatsApp integration
│   ├── invoices.js        # Invoice management
│   ├── payments.js        # Payment processing
│   ├── admin.js           # Admin operations
│   ├── plivo.js           # Voice calling
│   └── support.js         # Support system
├── .env                   # Environment variables
├── .env.example           # Environment template
├── package.json           # Dependencies and scripts
└── server.js              # Main server file
```

## 🔒 Security Features

- **Helmet.js** - Security headers
- **Rate Limiting** - Prevent abuse
- **CORS** - Cross-origin configuration
- **JWT Authentication** - Secure token-based auth
- **Input Validation** - Request validation with Joi
- **Error Handling** - Centralized error management

## 🚨 Troubleshooting

### Server Won't Start

**1. Check Dependencies:**
```bash
rm -rf node_modules package-lock.json
npm install
```

**2. Verify Environment Variables:**
```bash
# Check if .env exists
ls -la .env

# Verify required variables are set
node -e "require('dotenv').config(); console.log('URL:', !!process.env.SUPABASE_URL); console.log('ANON:', !!process.env.SUPABASE_ANON_KEY); console.log('SERVICE:', !!process.env.SUPABASE_SERVICE_ROLE_KEY); console.log('JWT:', !!process.env.JWT_SECRET);"
```

**3. Test Supabase Connection:**
```bash
# Test connection
node -e "
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const client = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);
console.log('Supabase client created successfully');
"
```

**4. Check Port Availability:**
```bash
# Check if port 5000 is in use
netstat -an | grep 5000
# Or on Windows
netstat -an | findstr 5000
```

### Common Errors

**"Missing Supabase environment variables"**
- Ensure `.env` file exists with correct Supabase credentials

**"EADDRINUSE: address already in use"**
- Port 5000 is occupied, change PORT in `.env` or kill the process

**"Cannot find module"**
- Run `npm install` to install dependencies

**CORS errors**
- Check frontend URL in CORS configuration (server.js)

### Database Issues

**"relation does not exist"**
- Run the SQL schema in `database/schema.sql`

**"RLS policy violation"**
- Check Row Level Security policies in Supabase

## 📊 Health Check

Visit `http://localhost:5000/health` to verify server status:

```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456
}
```

## 🔧 Configuration

### Rate Limiting
```env
RATE_LIMIT_WINDOW=15      # Minutes
RATE_LIMIT_MAX_REQUESTS=100
```

### File Upload
```env
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=pdf,doc,docx,csv,xlsx
```

## 📝 Logging

- **Development:** Console logging with colors
- **Production:** Combined format logging
- **Levels:** Error, Warn, Info, Debug

## 🤝 API Testing

Use tools like Postman or curl:

```bash
# Health check
curl http://localhost:5000/health

# Login example
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
``` 
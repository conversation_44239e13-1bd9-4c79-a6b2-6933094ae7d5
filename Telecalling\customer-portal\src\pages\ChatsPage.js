import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { MessageSquare, Plus, Search, User } from 'lucide-react';

const ChatsPage = () => {
  const [chats] = useState([
    {
      id: '1',
      title: 'Product Support',
      lastMessage: 'Thank you for your help with the laptop specifications!',
      timestamp: '2 hours ago',
      unreadCount: 0,
      clientName: 'TechStore Solutions',
      status: 'active'
    },
    {
      id: '2',
      title: 'Order Inquiry',
      lastMessage: 'When will my order be delivered?',
      timestamp: '1 day ago',
      unreadCount: 1,
      clientName: 'TechStore Solutions',
      status: 'active'
    },
    {
      id: '3',
      title: 'Service Request',
      lastMessage: 'The repair has been completed successfully.',
      timestamp: '3 days ago',
      unreadCount: 0,
      clientName: 'TechStore Solutions',
      status: 'closed'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');

  const filteredChats = chats.filter(chat =>
    chat.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chat.lastMessage.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="chats-page" style={{ minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <header style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
            My Chats
          </h1>
          <Link 
            to="/dashboard" 
            style={{ 
              padding: '0.5rem 1rem', 
              backgroundColor: '#3b82f6', 
              color: 'white', 
              textDecoration: 'none', 
              borderRadius: '4px'
            }}
          >
            Back to Dashboard
          </Link>
        </div>
      </header>

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem 1rem' }}>
        {/* Stats */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginBottom: '2rem' }}>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6' }}>{chats.length}</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Total Chats</div>
          </div>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981' }}>{chats.filter(c => c.status === 'active').length}</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Active Chats</div>
          </div>
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', textAlign: 'center' }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ef4444' }}>{chats.reduce((sum, c) => sum + c.unreadCount, 0)}</div>
            <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Unread Messages</div>
          </div>
        </div>

        {/* Search and Actions */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1.5rem', marginBottom: '2rem' }}>
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <div style={{ position: 'relative', flex: 1 }}>
              <Search size={20} style={{ position: 'absolute', left: '0.75rem', top: '50%', transform: 'translateY(-50%)', color: '#9ca3af' }} />
              <input
                type="text"
                placeholder="Search chats..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem 0.75rem 0.75rem 2.5rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  fontSize: '1rem',
                  boxSizing: 'border-box'
                }}
              />
            </div>
            <button
              style={{
                padding: '0.75rem 1rem',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              <Plus size={16} />
              New Chat
            </button>
          </div>
        </div>

        {/* Chats List */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
          <div style={{ padding: '1.5rem', borderBottom: '1px solid #e5e7eb' }}>
            <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>Recent Conversations</h2>
          </div>

          <div style={{ padding: '0' }}>
            {filteredChats.length > 0 ? (
              filteredChats.map((chat) => (
                <Link
                  key={chat.id}
                  to={`/chats/${chat.id}`}
                  style={{ 
                    display: 'block',
                    padding: '1.5rem', 
                    borderBottom: '1px solid #e5e7eb',
                    textDecoration: 'none',
                    color: 'inherit',
                    transition: 'background-color 0.2s'
                  }}
                  onMouseOver={(e) => e.target.style.backgroundColor = '#f9fafb'}
                  onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                        <MessageSquare size={20} style={{ color: '#3b82f6' }} />
                        <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1f2937', margin: 0 }}>
                          {chat.title}
                        </h3>
                        {chat.status === 'active' && (
                          <span style={{ 
                            padding: '0.125rem 0.5rem', 
                            backgroundColor: '#dcfce7', 
                            color: '#166534', 
                            borderRadius: '12px', 
                            fontSize: '0.75rem',
                            fontWeight: '500'
                          }}>
                            Active
                          </span>
                        )}
                      </div>
                      <p style={{ color: '#6b7280', marginBottom: '0.5rem', fontSize: '0.875rem' }}>
                        {chat.lastMessage}
                      </p>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.75rem', color: '#9ca3af' }}>
                        <User size={12} />
                        <span>{chat.clientName}</span>
                        <span>•</span>
                        <span>{chat.timestamp}</span>
                      </div>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      {chat.unreadCount > 0 && (
                        <span style={{ 
                          backgroundColor: '#ef4444', 
                          color: 'white', 
                          borderRadius: '50%', 
                          width: '24px', 
                          height: '24px', 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center', 
                          fontSize: '0.75rem',
                          fontWeight: '600'
                        }}>
                          {chat.unreadCount}
                        </span>
                      )}
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div style={{ padding: '3rem', textAlign: 'center' }}>
                <MessageSquare size={48} style={{ color: '#9ca3af', margin: '0 auto 1rem' }} />
                <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1f2937', marginBottom: '0.5rem' }}>
                  {searchTerm ? 'No chats found' : 'No chats yet'}
                </h3>
                <p style={{ color: '#6b7280', marginBottom: '1.5rem' }}>
                  {searchTerm 
                    ? 'Try adjusting your search terms'
                    : 'Start a conversation with our team to get help with your orders and inquiries.'
                  }
                </p>
                {!searchTerm && (
                  <button
                    style={{
                      padding: '0.75rem 1.5rem',
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '1rem',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      margin: '0 auto'
                    }}
                  >
                    <Plus size={16} />
                    Start Your First Chat
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatsPage;

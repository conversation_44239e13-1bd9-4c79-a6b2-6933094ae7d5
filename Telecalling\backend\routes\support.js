const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { dbH<PERSON><PERSON> } = require('../config/supabase');
const { auth } = require('../middleware/auth');
const router = express.Router();

// Email sending function using Supabase edge function
const sendSupportEmail = async (ticketData, clientData) => {
  try {
    if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.log('⚠️ Email service not configured - skipping email notification');
      return;
    }

    const supabaseUrl = process.env.SUPABASE_URL.replace(/\/$/, '');
    const endpoint = `${supabaseUrl}/functions/v1/send-support-ticket-email`;

    console.log('📧 Sending support ticket <NAME_EMAIL>');

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        ticketId: ticketData.id,
        subject: ticketData.subject,
        message: ticketData.message,
        urgencyLevel: ticketData.urgency_level,
        clientName: clientData.username,
        clientEmail: clientData.email,
        businessName: clientData.businessName || 'Not provided',
        createdAt: ticketData.created_at
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Failed to send support email:', errorText);
    } else {
      console.log('✅ Support ticket email sent successfully');
    }
  } catch (error) {
    console.error('❌ Error sending support email:', error);
  }
};

// Get support tickets for client
router.get('/', auth, async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT st.*, a.name as admin_name
      FROM support_tickets st
      LEFT JOIN admins a ON st.admin_id = a.id
      WHERE st.client_id = $1
    `;
    let params = [req.user.id];
    let paramIndex = 2;

    if (status) {
      query += ` AND st.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    query += ` ORDER BY st.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const tickets = await dbHelpers.query(query, params);

    let countQuery = `SELECT COUNT(*) as total FROM support_tickets WHERE client_id = $1`;
    let countParams = [req.user.id];

    if (status) {
      countQuery += ` AND status = $2`;
      countParams.push(status);
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Support tickets retrieved successfully',
      tickets: tickets.map(ticket => ({
        id: ticket.id,
        subject: ticket.subject,
        message: ticket.message,
        urgencyLevel: ticket.urgency_level,
        status: ticket.status,
        adminName: ticket.admin_name,
        adminResponse: ticket.admin_response,
        resolvedAt: ticket.resolved_at,
        createdAt: ticket.created_at,
        updatedAt: ticket.updated_at
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get support tickets error:', error);
    res.status(500).json({
      error: 'Failed to retrieve support tickets',
      message: 'An error occurred while retrieving support tickets'
    });
  }
});

// Create new support ticket
router.post('/', auth, async (req, res) => {
  try {
    const { subject, message, urgencyLevel = 'medium' } = req.body;

    if (!subject || !message) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Subject and message are required'
      });
    }

    const validUrgencyLevels = ['low', 'medium', 'high', 'urgent'];
    if (!validUrgencyLevels.includes(urgencyLevel)) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid urgency level'
      });
    }

    const client = await dbHelpers.findOne('clients', { id: req.user.id });

    const ticket = await dbHelpers.insert('support_tickets', {
      id: uuidv4(),
      client_id: req.user.id,
      subject,
      message,
      urgency_level: urgencyLevel,
      status: 'open',
      client_email: client.email,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Send <NAME_EMAIL>
    await sendSupportEmail(ticket, client);

    res.status(201).json({
      message: 'Support ticket created successfully',
      ticket: {
        id: ticket.id,
        subject: ticket.subject,
        message: ticket.message,
        urgencyLevel: ticket.urgency_level,
        status: ticket.status,
        createdAt: ticket.created_at
      }
    });

  } catch (error) {
    console.error('Create support ticket error:', error);
    res.status(500).json({
      error: 'Failed to create support ticket',
      message: 'An error occurred while creating support ticket'
    });
  }
});

// Get single support ticket
router.get('/:id', auth, async (req, res) => {
  try {
    const ticket = await dbHelpers.query(`
      SELECT st.*, a.name as admin_name
      FROM support_tickets st
      LEFT JOIN admins a ON st.admin_id = a.id
      WHERE st.id = $1 AND st.client_id = $2
    `, [req.params.id, req.user.id]);

    if (!ticket.length) {
      return res.status(404).json({
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    const ticketData = ticket[0];

    res.json({
      message: 'Support ticket retrieved successfully',
      ticket: {
        id: ticketData.id,
        subject: ticketData.subject,
        message: ticketData.message,
        urgencyLevel: ticketData.urgency_level,
        status: ticketData.status,
        adminName: ticketData.admin_name,
        adminResponse: ticketData.admin_response,
        resolvedAt: ticketData.resolved_at,
        createdAt: ticketData.created_at,
        updatedAt: ticketData.updated_at
      }
    });

  } catch (error) {
    console.error('Get support ticket error:', error);
    res.status(500).json({
      error: 'Failed to retrieve support ticket',
      message: 'An error occurred while retrieving support ticket'
    });
  }
});

// Get messages for a support ticket
router.get('/:ticketId/messages', auth, async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    // Verify ticket belongs to the client
    const ticket = await dbHelpers.findOne('support_tickets', {
      id: ticketId,
      client_id: req.user.id
    });

    if (!ticket) {
      return res.status(404).json({
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    // Get messages for the ticket
    const messages = await dbHelpers.query(`
      SELECT stm.*,
             CASE
               WHEN stm.sender_type = 'client' THEN c.username
               WHEN stm.sender_type = 'admin' THEN a.name
             END as sender_name
      FROM support_ticket_messages stm
      LEFT JOIN clients c ON stm.sender_type = 'client' AND stm.sender_id = c.id
      LEFT JOIN admins a ON stm.sender_type = 'admin' AND stm.sender_id = a.id
      WHERE stm.ticket_id = $1
      ORDER BY stm.created_at ASC
      LIMIT $2 OFFSET $3
    `, [ticketId, limit, offset]);

    // Mark messages as read by client
    await dbHelpers.query(`
      UPDATE support_ticket_messages
      SET is_read = true
      WHERE ticket_id = $1 AND sender_type = 'admin' AND is_read = false
    `, [ticketId]);

    res.json({
      message: 'Messages retrieved successfully',
      messages: messages.map(msg => ({
        id: msg.id,
        message: msg.message,
        messageType: msg.message_type,
        senderType: msg.sender_type,
        senderName: msg.sender_name,
        attachmentUrl: msg.attachment_url,
        isRead: msg.is_read,
        createdAt: msg.created_at
      }))
    });

  } catch (error) {
    console.error('Get ticket messages error:', error);
    res.status(500).json({
      error: 'Failed to retrieve messages',
      message: 'An error occurred while retrieving ticket messages'
    });
  }
});

// Send message to support ticket
router.post('/:ticketId/messages', auth, async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { message, messageType = 'text', attachmentUrl } = req.body;

    if (!message || message.trim() === '') {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Message content is required'
      });
    }

    // Verify ticket belongs to the client
    const ticket = await dbHelpers.findOne('support_tickets', {
      id: ticketId,
      client_id: req.user.id
    });

    if (!ticket) {
      return res.status(404).json({
        error: 'Ticket not found',
        message: 'Support ticket not found or access denied'
      });
    }

    // Create message
    const newMessage = await dbHelpers.insert('support_ticket_messages', {
      id: uuidv4(),
      ticket_id: ticketId,
      sender_type: 'client',
      sender_id: req.user.id,
      message: message.trim(),
      message_type: messageType,
      attachment_url: attachmentUrl || null,
      is_read: false,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Update ticket status to in-progress if it was open
    if (ticket.status === 'open') {
      await dbHelpers.update('support_tickets', {
        status: 'in-progress',
        updated_at: new Date()
      }, { id: ticketId });
    }

    // Get client info for response
    const client = await dbHelpers.findOne('clients', { id: req.user.id });

    res.status(201).json({
      message: 'Message sent successfully',
      messageData: {
        id: newMessage.id,
        message: newMessage.message,
        messageType: newMessage.message_type,
        senderType: 'client',
        senderName: client.username,
        attachmentUrl: newMessage.attachment_url,
        isRead: false,
        createdAt: newMessage.created_at
      }
    });

  } catch (error) {
    console.error('Send ticket message error:', error);
    res.status(500).json({
      error: 'Failed to send message',
      message: 'An error occurred while sending the message'
    });
  }
});

module.exports = router;
# Authentication System Fix Guide

## Issues Identified

1. **JWT Secret Issue**: Environment variable loading problem
2. **Dual Client Systems**: Two different client table structures
3. **OTP Verification Bypass**: Users getting registered without verification
4. **Missing Database Tables**: Supabase tables need to be recreated

## Step-by-Step Fix

### 1. Database Setup

First, run the complete table creation script in your Supabase SQL Editor:

```sql
-- Run this file: database/create_all_tables.sql
```

This will create all necessary tables:
- `clients` (for authentication)
- `pending_registrations` (for signup flow)
- `otp_verifications` (for OTP management)
- `login_sessions` (for session management)
- `password_reset_tokens` (for password resets)

### 2. Environment Variables Fix

The issue with JWT_SECRET is likely due to a space in the SUPABASE_URL. This has been fixed in your .env file.

Verify these environment variables are set:
```
JWT_SECRET=93e3541d0dc7ac8688055d7edc4621db99d029611b190f4d8def70da1ec350c4a82bc7623a2a2b3f56497b16d3734553641a121068309ed58ed37e721e1b6784
JWT_REFRESH_SECRET=b8f2e9a3c7d6f1e4a8b3c9d5e7f2a6b4c8d3e9f1a5b7c3d8e4f2a9b6c5d1e7f3a4b8c2d6e9f5a1b4c7d3e8f6a2b5c9d4e1f7a3b6c8d5e2f9a7b3c4d1e8f6a5b2c9
SUPABASE_URL=https://giskyueandklposlokkq.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdpc2t5dWVhbmRrbHBvc2xva2txIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTY5NjEwNSwiZXhwIjoyMDY3MjcyMTA1fQ.zTHXXptfIbbQufOIXlViOx14zVg8gqAsEP4Nn71v27M
DATABASE_URL=postgresql://postgres.giskyueandklposlokkq:<EMAIL>:5432/postgres
```

### 3. Authentication Flow Verification

The current authentication flow is correct:

1. **Signup** → Creates `pending_registrations` record
2. **OTP Email** → Sent via Supabase Edge Function
3. **OTP Verification** → Creates `clients` record and Supabase Auth user
4. **Login** → Uses JWT or Supabase Auth

### 4. Dual Client System Resolution

You have two client systems:

**Authentication Clients** (`auth_schema.sql`):
- Used for user authentication
- Fields: email, password_hash, business_name, username, etc.

**Business Clients** (`schema.sql`):
- Used for business profiles
- Fields: shop_name, business_type, whatsapp_number, etc.
- References `profiles` table

**Recommendation**: Keep both systems but ensure they work together:
1. Authentication clients for login/auth
2. Business clients for business data

### 5. Testing the Fix

After running the SQL script, test the authentication flow:

1. **Test Signup**:
```bash
curl -X POST http://localhost:5000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "businessName": "Test Business",
    "username": "testuser"
  }'
```

2. **Check Pending Registration**:
```sql
SELECT * FROM pending_registrations WHERE email = '<EMAIL>';
```

3. **Check OTP**:
```sql
SELECT * FROM otp_verifications WHERE email = '<EMAIL>';
```

4. **Test OTP Verification**:
```bash
curl -X POST http://localhost:5000/api/auth/verify-email \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "123456"
  }'
```

5. **Check Client Creation**:
```sql
SELECT * FROM clients WHERE email = '<EMAIL>';
```

### 6. Common Issues and Solutions

**Issue**: "JWT_SECRET environment variable is not set"
**Solution**: Restart your server after fixing the .env file

**Issue**: Users getting registered without OTP
**Solution**: Check if there's a direct registration endpoint bypassing the auth flow

**Issue**: OTP emails not sending
**Solution**: Verify Supabase Edge Function is deployed and RESEND_API_KEY is set

**Issue**: Login fails with JWT error
**Solution**: Check JWT_SECRET and JWT_REFRESH_SECRET are properly set

### 7. Security Recommendations

1. **Rate Limiting**: Already implemented for auth endpoints
2. **Password Hashing**: Using bcrypt with 12 rounds
3. **JWT Expiration**: Access tokens expire in 15 minutes
4. **Account Locking**: After 5 failed attempts
5. **OTP Expiration**: OTPs expire in 10 minutes
6. **Session Management**: Proper session tracking

### 8. Monitoring

Monitor these tables for issues:
- `pending_registrations` - Should be cleaned up regularly
- `otp_verifications` - Check for high failure rates
- `login_sessions` - Monitor active sessions
- `clients` - Check for duplicate emails

### 9. Edge Function Verification

Your Supabase Edge Function for sending OTP emails is working correctly. Make sure it's deployed:

```bash
supabase functions deploy send-otp-email
```

### 10. Next Steps

1. Run the database creation script
2. Restart your backend server
3. Test the complete authentication flow
4. Monitor logs for any remaining issues
5. Consider implementing additional security measures if needed

## Troubleshooting Commands

```sql
-- Check all authentication-related data
SELECT 'pending_registrations' as table_name, count(*) as count FROM pending_registrations
UNION ALL
SELECT 'otp_verifications', count(*) FROM otp_verifications
UNION ALL
SELECT 'clients', count(*) FROM clients
UNION ALL
SELECT 'login_sessions', count(*) FROM login_sessions;

-- Clean up expired records
SELECT cleanup_expired_records();

-- Check user auth status
SELECT * FROM user_auth_status WHERE email = '<EMAIL>';
```

#!/usr/bin/env node

/**
 * Check Super Admin Database Setup
 */

require('dotenv').config({ path: './backend/.env' });
const { dbHelpers } = require('./backend/config/supabase');

async function checkSuperAdmin() {
  console.log('🔍 Checking Super Admin Database Setup\n');

  try {
    // Check if super admin exists
    console.log('1️⃣ Checking if super admin exists...');
    const superAdmin = await dbHelpers.findOne('admins', { admin_id: 'superadmin001' });
    
    if (superAdmin) {
      console.log('✅ Super admin found:');
      console.log(`   - ID: ${superAdmin.admin_id}`);
      console.log(`   - Name: ${superAdmin.name}`);
      console.log(`   - Email: ${superAdmin.email}`);
      console.log(`   - Role: ${superAdmin.role}`);
      console.log(`   - Password: ${superAdmin.password_hash}`);
      console.log(`   - Active: ${superAdmin.is_active}`);
      
      if (superAdmin.role !== 'super_admin') {
        console.log('❌ ISSUE: Role is not "super_admin"');
        console.log('🔧 Fixing role...');
        await dbHelpers.update('admins', { role: 'super_admin' }, { admin_id: 'superadmin001' });
        console.log('✅ Role updated to super_admin');
      }
      
      if (superAdmin.password_hash !== 'superadmin123') {
        console.log('❌ ISSUE: Password is not "superadmin123"');
        console.log('🔧 Fixing password...');
        await dbHelpers.update('admins', { password_hash: 'superadmin123' }, { admin_id: 'superadmin001' });
        console.log('✅ Password updated to superadmin123');
      }
    } else {
      console.log('❌ Super admin not found');
      console.log('🔧 Creating super admin...');
      
      const newSuperAdmin = await dbHelpers.insert('admins', {
        admin_id: 'superadmin001',
        password_hash: 'superadmin123',
        name: 'Super Administrator',
        email: '<EMAIL>',
        role: 'super_admin',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      });
      
      console.log('✅ Super admin created:', newSuperAdmin.admin_id);
    }

    // Check regular admin too
    console.log('\n2️⃣ Checking regular admin...');
    const regularAdmin = await dbHelpers.findOne('admins', { admin_id: 'admin001' });
    
    if (regularAdmin) {
      console.log('✅ Regular admin found:');
      console.log(`   - ID: ${regularAdmin.admin_id}`);
      console.log(`   - Role: ${regularAdmin.role}`);
      console.log(`   - Password: ${regularAdmin.password_hash}`);
      
      if (regularAdmin.password_hash !== 'admin123') {
        console.log('🔧 Fixing regular admin password...');
        await dbHelpers.update('admins', { password_hash: 'admin123' }, { admin_id: 'admin001' });
        console.log('✅ Regular admin password updated');
      }
    } else {
      console.log('❌ Regular admin not found');
      console.log('🔧 Creating regular admin...');
      
      await dbHelpers.insert('admins', {
        admin_id: 'admin001',
        password_hash: 'admin123',
        name: 'System Administrator',
        email: '<EMAIL>',
        role: 'admin',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      });
      
      console.log('✅ Regular admin created');
    }

    // Final verification
    console.log('\n3️⃣ Final verification...');
    const allAdmins = await dbHelpers.query('SELECT admin_id, name, role, password_hash, is_active FROM admins ORDER BY role DESC');
    
    console.log('📋 All admins in database:');
    allAdmins.forEach(admin => {
      console.log(`   - ${admin.admin_id} | ${admin.name} | Role: ${admin.role} | Password: ${admin.password_hash} | Active: ${admin.is_active}`);
    });

    // Test authentication
    console.log('\n4️⃣ Testing authentication...');
    const testSuperAdmin = await dbHelpers.findOne('admins', { admin_id: 'superadmin001' });
    const testPassword = 'superadmin123';
    const authTest = testPassword === testSuperAdmin.password_hash;
    
    console.log(`🔐 Super admin auth test: ${authTest ? 'PASS' : 'FAIL'}`);
    console.log(`   Expected: ${testPassword}`);
    console.log(`   Stored: ${testSuperAdmin.password_hash}`);
    console.log(`   Role: ${testSuperAdmin.role}`);

    console.log('\n🎉 Database check completed!');
    console.log('\n📋 Credentials to use:');
    console.log('   Super Admin: superadmin001 / superadmin123');
    console.log('   Regular Admin: admin001 / admin123');

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
}

// Run the check
checkSuperAdmin();

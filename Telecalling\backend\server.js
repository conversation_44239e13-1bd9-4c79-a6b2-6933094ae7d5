const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const clientRoutes = require('./routes/clients');
const productRoutes = require('./routes/products_new');
const appointmentRoutes = require('./routes/appointments');
const callLogRoutes = require('./routes/callLogs');
const whatsappRoutes = require('./routes/whatsapp');
const invoiceRoutes = require('./routes/invoices');
const paymentRoutes = require('./routes/payments');
const adminRoutes = require('./routes/admin');
const plivoRoutes = require('./routes/plivo');
const supportRoutes = require('./routes/support');
const profileRoutes = require('./routes/profile');
const analyticsRoutes = require('./routes/analytics');
const subscriptionRoutes = require('./routes/subscriptions');
const customerRoutes = require('./routes/customers');
const usageLimitsRoutes = require('./routes/usage-limits');
const chatbotRoutes = require('./routes/chatbot');

// Import middleware
const { errorHandler } = require('./middleware/errorHandler');
const { authenticate } = require('./middleware/auth');

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: (process.env.RATE_LIMIT_WINDOW || 15) * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_MAX_REQUESTS || 100,
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com', 'https://www.your-domain.com']
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV !== 'production') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Root endpoint - Show available routes
app.get('/', (req, res) => {
  res.json({
    service: 'Telecalling Client API',
    version: '1.0.0',
    status: 'Running',
    port: PORT,
    timestamp: new Date().toISOString(),
    availableRoutes: {
      authentication: {
        signup: 'POST /api/auth/signup',
        login: 'POST /api/auth/login',
        googleAuth: 'POST /api/auth/google',
        forgotPassword: 'POST /api/auth/forgot-password',
        resetPassword: 'POST /api/auth/reset-password'
      },
      profile: {
        getProfile: 'GET /api/profile',
        updateProfile: 'PUT /api/profile',
        resetPassword: 'POST /api/profile/reset-password'
      },
      products: {
        getAllProducts: 'GET /api/products',
        getProduct: 'GET /api/products/:id',
        createProduct: 'POST /api/products',
        updateProduct: 'PUT /api/products/:id',
        deleteProduct: 'DELETE /api/products/:id',
        bulkUpload: 'POST /api/products/bulk-upload'
      },
      analytics: {
        callLogs: 'GET /api/analytics/call-logs',
        smsLogs: 'GET /api/analytics/sms-logs',
        usageStats: 'GET /api/analytics/usage-stats',
        dashboardOverview: 'GET /api/analytics/dashboard-overview'
      },
      support: {
        getTickets: 'GET /api/support',
        createTicket: 'POST /api/support',
        getTicket: 'GET /api/support/:id'
      },
      subscriptions: {
        getPlans: 'GET /api/subscriptions/plans',
        getCurrentSubscription: 'GET /api/subscriptions/current',
        createOrder: 'POST /api/subscriptions/create-order',
        verifyPayment: 'POST /api/subscriptions/verify-payment',
        getPayments: 'GET /api/subscriptions/payments',
        cancelSubscription: 'POST /api/subscriptions/cancel/:id'
      },
      utilities: {
        health: 'GET /health'
      }
    },
    adminPanel: {
      url: 'http://localhost:4000',
      note: 'Admin panel runs on separate port'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/profile', profileRoutes);
app.use('/api/products', productRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/support', supportRoutes);
app.use('/api/clients', authenticate, clientRoutes);
app.use('/api/appointments', authenticate, appointmentRoutes);
app.use('/api/call-logs', authenticate, callLogRoutes);
app.use('/api/whatsapp', authenticate, whatsappRoutes);
app.use('/api/invoices', authenticate, invoiceRoutes);
app.use('/api/payments', authenticate, paymentRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/admin', authenticate, adminRoutes);
app.use('/api/usage', authenticate, usageLimitsRoutes);
app.use('/api/chatbot', authenticate, chatbotRoutes);

// Customer portal routes (separate authentication)
app.use('/api/customers', customerRoutes);

// Public routes (no authentication required)
app.use('/api/plivo', plivoRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

// Global error handler
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
});

module.exports = app; 
const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get admin dashboard overview
router.get('/dashboard', requireAdmin, asyncHandler(async (req, res) => {
  // Get total clients
  const { count: totalClients } = await supabaseAdmin
    .from('clients')
    .select('*', { count: 'exact', head: true });

  // Get active clients
  const { count: activeClients } = await supabaseAdmin
    .from('clients')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'active');

  // Get pending clients
  const { count: pendingClients } = await supabaseAdmin
    .from('clients')
    .select('*', { count: 'exact', head: true })
    .eq('status', 'pending');

  // Get total calls in last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const { count: totalCalls } = await supabaseAdmin
    .from('call_logs')
    .select('*', { count: 'exact', head: true })
    .gte('call_time', thirtyDaysAgo.toISOString());

  // Get total WhatsApp messages in last 30 days
  const { count: totalMessages } = await supabaseAdmin
    .from('whatsapp_logs')
    .select('*', { count: 'exact', head: true })
    .gte('timestamp', thirtyDaysAgo.toISOString());

  // Get recent signups
  const { data: recentSignups } = await supabaseAdmin
    .from('clients')
    .select(`
      *,
      profiles!clients_user_id_fkey (
        full_name,
        email
      )
    `)
    .order('created_at', { ascending: false })
    .limit(5);

  res.json({
    overview: {
      totalClients: totalClients || 0,
      activeClients: activeClients || 0,
      pendingClients: pendingClients || 0,
      totalCalls: totalCalls || 0,
      totalMessages: totalMessages || 0,
      recentSignups: recentSignups || []
    }
  });
}));

// Get all clients with pagination
router.get('/clients', requireAdmin, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, status, search, sortBy = 'created_at', sortOrder = 'desc' } = req.query;
  const offset = (page - 1) * limit;

  let query = supabaseAdmin
    .from('clients')
    .select(`
      *,
      profiles!clients_user_id_fkey (
        full_name,
        email,
        created_at
      )
    `)
    .order(sortBy, { ascending: sortOrder === 'asc' })
    .range(offset, offset + limit - 1);

  if (status) {
    query = query.eq('status', status);
  }

  if (search) {
    query = query.or(`shop_name.ilike.%${search}%,business_type.ilike.%${search}%`);
  }

  const { data: clients, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('clients')
    .select('*', { count: 'exact', head: true });

  if (status) {
    countQuery = countQuery.eq('status', status);
  }

  if (search) {
    countQuery = countQuery.or(`shop_name.ilike.%${search}%,business_type.ilike.%${search}%`);
  }

  const { count } = await countQuery;

  res.json({
    clients: clients || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Get single client details
router.get('/clients/:clientId', requireAdmin, asyncHandler(async (req, res) => {
  const { clientId } = req.params;

  const { data: client, error } = await supabaseAdmin
    .from('clients')
    .select(`
      *,
      profiles!clients_user_id_fkey (
        full_name,
        email,
        created_at
      ),
      bot_features (*),
      products (count),
      call_logs (count),
      whatsapp_logs (count),
      appointments:appointment_slots (count),
      invoices (count),
      payments (count)
    `)
    .eq('id', clientId)
    .single();

  if (error) {
    throw error;
  }

  if (!client) {
    throw new AppError('Client not found', 404);
  }

  res.json({
    client
  });
}));

// Update client status
router.put('/clients/:clientId/status', requireAdmin, asyncHandler(async (req, res) => {
  const { clientId } = req.params;
  const { status, plivoNumber, notes } = req.body;

  if (!['pending', 'active', 'inactive', 'suspended'].includes(status)) {
    throw new AppError('Invalid status', 400);
  }

  const updateData = { status };
  if (plivoNumber) updateData.plivo_number = plivoNumber;

  const { data: updatedClient, error } = await supabaseAdmin
    .from('clients')
    .update(updateData)
    .eq('id', clientId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!updatedClient) {
    throw new AppError('Client not found', 404);
  }

  // Log admin action
  await supabaseAdmin
    .from('admin_actions')
    .insert({
      id: uuidv4(),
      admin_id: req.user.id,
      client_id: clientId,
      action_type: 'status_change',
      details: {
        old_status: updatedClient.status,
        new_status: status,
        plivo_number: plivoNumber,
        notes
      },
      created_at: new Date().toISOString()
    });

  res.json({
    message: 'Client status updated successfully',
    client: updatedClient
  });
}));

// Assign Plivo number to client
router.post('/clients/:clientId/assign-number', requireAdmin, asyncHandler(async (req, res) => {
  const { clientId } = req.params;
  const { plivoNumber } = req.body;

  if (!plivoNumber) {
    throw new AppError('Plivo number is required', 400);
  }

  // Check if number is already assigned
  const { data: existingClient } = await supabaseAdmin
    .from('clients')
    .select('id, shop_name')
    .eq('plivo_number', plivoNumber)
    .single();

  if (existingClient) {
    throw new AppError(`Number ${plivoNumber} is already assigned to ${existingClient.shop_name}`, 409);
  }

  const { data: updatedClient, error } = await supabaseAdmin
    .from('clients')
    .update({
      plivo_number: plivoNumber,
      status: 'active'
    })
    .eq('id', clientId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!updatedClient) {
    throw new AppError('Client not found', 404);
  }

  // Log admin action
  await supabaseAdmin
    .from('admin_actions')
    .insert({
      id: uuidv4(),
      admin_id: req.user.id,
      client_id: clientId,
      action_type: 'assign_number',
      details: {
        plivo_number: plivoNumber
      },
      created_at: new Date().toISOString()
    });

  res.json({
    message: 'Plivo number assigned successfully',
    client: updatedClient
  });
}));

// Get all call logs (admin view)
router.get('/call-logs', requireAdmin, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, clientId, startDate, endDate } = req.query;
  const offset = (page - 1) * limit;

  let query = supabaseAdmin
    .from('call_logs')
    .select(`
      *,
      clients!call_logs_client_id_fkey (
        shop_name,
        business_type
      )
    `)
    .order('call_time', { ascending: false })
    .range(offset, offset + limit - 1);

  if (clientId) {
    query = query.eq('client_id', clientId);
  }

  if (startDate) {
    query = query.gte('call_time', new Date(startDate).toISOString());
  }

  if (endDate) {
    query = query.lte('call_time', new Date(endDate).toISOString());
  }

  const { data: callLogs, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('call_logs')
    .select('*', { count: 'exact', head: true });

  if (clientId) {
    countQuery = countQuery.eq('client_id', clientId);
  }

  if (startDate) {
    countQuery = countQuery.gte('call_time', new Date(startDate).toISOString());
  }

  if (endDate) {
    countQuery = countQuery.lte('call_time', new Date(endDate).toISOString());
  }

  const { count } = await countQuery;

  res.json({
    callLogs: callLogs || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Get support tickets
router.get('/support-tickets', requireAdmin, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, status, priority } = req.query;
  const offset = (page - 1) * limit;

  let query = supabaseAdmin
    .from('support_tickets')
    .select(`
      *,
      clients!support_tickets_client_id_fkey (
        shop_name,
        profiles!clients_user_id_fkey (
          full_name,
          email
        )
      )
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (status) {
    query = query.eq('status', status);
  }

  if (priority) {
    query = query.eq('priority', priority);
  }

  const { data: tickets, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('support_tickets')
    .select('*', { count: 'exact', head: true });

  if (status) {
    countQuery = countQuery.eq('status', status);
  }

  if (priority) {
    countQuery = countQuery.eq('priority', priority);
  }

  const { count } = await countQuery;

  res.json({
    tickets: tickets || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

// Update support ticket
router.put('/support-tickets/:ticketId', requireAdmin, asyncHandler(async (req, res) => {
  const { ticketId } = req.params;
  const { status, priority, response } = req.body;

  const updateData = {};
  if (status) updateData.status = status;
  if (priority) updateData.priority = priority;
  if (response) updateData.admin_response = response;

  const { data: updatedTicket, error } = await supabaseAdmin
    .from('support_tickets')
    .update(updateData)
    .eq('id', ticketId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  if (!updatedTicket) {
    throw new AppError('Support ticket not found', 404);
  }

  res.json({
    message: 'Support ticket updated successfully',
    ticket: updatedTicket
  });
}));

// Get system statistics
router.get('/stats', requireAdmin, asyncHandler(async (req, res) => {
  const { period = '30d' } = req.query;

  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(startDate.getDate() - 90);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }

  // Get various statistics
  const [
    { count: totalCalls },
    { count: totalMessages },
    { count: totalInvoices },
    { count: totalPayments },
    { data: callsByDate },
    { data: messagesByDate }
  ] = await Promise.all([
    supabaseAdmin.from('call_logs').select('*', { count: 'exact', head: true })
      .gte('call_time', startDate.toISOString()),
    supabaseAdmin.from('whatsapp_logs').select('*', { count: 'exact', head: true })
      .gte('timestamp', startDate.toISOString()),
    supabaseAdmin.from('invoices').select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString()),
    supabaseAdmin.from('payments').select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString()),
    supabaseAdmin.from('call_logs').select('call_time')
      .gte('call_time', startDate.toISOString()),
    supabaseAdmin.from('whatsapp_logs').select('timestamp')
      .gte('timestamp', startDate.toISOString())
  ]);

  // Group data by date
  const callsByDateGrouped = {};
  const messagesByDateGrouped = {};

  (callsByDate || []).forEach(log => {
    const date = new Date(log.call_time).toISOString().split('T')[0];
    callsByDateGrouped[date] = (callsByDateGrouped[date] || 0) + 1;
  });

  (messagesByDate || []).forEach(log => {
    const date = new Date(log.timestamp).toISOString().split('T')[0];
    messagesByDateGrouped[date] = (messagesByDateGrouped[date] || 0) + 1;
  });

  res.json({
    stats: {
      totalCalls: totalCalls || 0,
      totalMessages: totalMessages || 0,
      totalInvoices: totalInvoices || 0,
      totalPayments: totalPayments || 0,
      callsByDate: callsByDateGrouped,
      messagesByDate: messagesByDateGrouped
    }
  });
}));

// Get admin actions log
router.get('/actions', requireAdmin, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, adminId, actionType } = req.query;
  const offset = (page - 1) * limit;

  let query = supabaseAdmin
    .from('admin_actions')
    .select(`
      *,
      profiles!admin_actions_admin_id_fkey (
        full_name,
        email
      ),
      clients!admin_actions_client_id_fkey (
        shop_name
      )
    `)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (adminId) {
    query = query.eq('admin_id', adminId);
  }

  if (actionType) {
    query = query.eq('action_type', actionType);
  }

  const { data: actions, error } = await query;

  if (error) {
    throw error;
  }

  // Get total count
  let countQuery = supabaseAdmin
    .from('admin_actions')
    .select('*', { count: 'exact', head: true });

  if (adminId) {
    countQuery = countQuery.eq('admin_id', adminId);
  }

  if (actionType) {
    countQuery = countQuery.eq('action_type', actionType);
  }

  const { count } = await countQuery;

  res.json({
    actions: actions || [],
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: count || 0,
      pages: Math.ceil((count || 0) / limit)
    }
  });
}));

module.exports = router; 
import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import forgotPasswordImage from '../../assets/images/forget-password.jpeg';
import './ForgotPasswordModal.css';

const ForgotPasswordModal = ({ isOpen, onClose, onBackToLogin, onResetEmailSent, onResetPassword }) => {
  const { forgotPassword } = useAuth();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleInputChange = (e) => {
    setEmail(e.target.value);
    // Clear error when user starts typing
    if (error) setError('');
  };

  const validateEmail = (email) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setError('Please enter your email address');
      return;
    }
    
    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setError('');
    
    try {
      const result = await forgotPassword(email);
      
      if (result.success) {
        setIsEmailSent(true);
        setSuccess(result.message);
        onResetEmailSent && onResetEmailSent(email);
      } else {
        setError(result.error);
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setEmail('');
    setIsEmailSent(false);
    setError('');
    setSuccess('');
    onBackToLogin && onBackToLogin();
  };

  const handleResetPassword = () => {
    // Navigate to reset password modal with the email
    onResetPassword && onResetPassword();
  };

  if (!isOpen) return null;

  return (
    <div className="forgot-password-modal-overlay" onClick={onClose}>
      <div 
        className="forgot-password-modal-container" 
        onClick={e => e.stopPropagation()}
        style={{'--modal-bg-image': `url(${forgotPasswordImage})`}}
      >

        {/* Left side - Form */}
        <div className="modal-form-section">
          <div className="form-content">
            {/* Header */}
            <div className="modal-header">
              <div className="modal-logo">
                <span className="logo-text">VoiceBot Platform</span>
              </div>
              <p className="modal-tagline">Revolutionizing Communication.</p>
            </div>

            {/* Main content */}
            <div className="form-main">
              {!isEmailSent ? (
                <>
                  <h2 className="form-title">Forgot Your Password?</h2>
                  <p className="form-subtitle">
                    No worries! Enter your email address and we'll send you a reset code.
                  </p>

                  {/* Error message */}
                  {error && (
                    <div className="error-message">
                      {error}
                    </div>
                  )}

                  {/* Form */}
                  <form onSubmit={handleSubmit} className="forgot-password-form">
                    <div className="modal-form-group">
                      <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={handleInputChange}
                        disabled={isLoading}
                        required
                        autoFocus
                      />
                      <label htmlFor="email">Email Address</label>
                    </div>

                    <button 
                      type="submit" 
                      className="submit-btn"
                      disabled={isLoading || !email}
                    >
                      {isLoading ? 'Sending...' : 'Send Reset Code'}
                    </button>
                  </form>

                  <div className="form-footer">
                    <p>
                      Remember your password? 
                      <button 
                        type="button" 
                        className="link-btn"
                        onClick={handleBackToLogin}
                      >
                        Back to Login
                      </button>
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <h2 className="form-title">Check Your Email</h2>
                  <p className="form-subtitle">
                    We've sent a password reset code to <strong>{email}</strong>
                  </p>

                  {/* Success message */}
                  {success && (
                    <div className="success-message">
                      {success}
                    </div>
                  )}

                  <div className="success-content">
                    <div className="success-icon">
                      <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="m9 12 2 2 4-4"></path>
                      </svg>
                    </div>
                    
                    <div className="instructions">
                      <h4>What's next?</h4>
                      <ul>
                        <li>Check your email inbox for the reset code</li>
                        <li>The code will expire in 15 minutes</li>
                        <li>Don't forget to check your spam folder</li>
                        <li>Enter the code in the password reset form</li>
                      </ul>
                    </div>

                    <button 
                      type="button" 
                      className="submit-btn"
                      onClick={handleResetPassword}
                    >
                      I Have the Code
                    </button>

                    <div className="form-footer">
                      <p>
                        Didn't receive the email? 
                        <button 
                          type="button" 
                          className="link-btn"
                          onClick={() => {
                            setIsEmailSent(false);
                            setSuccess('');
                          }}
                        >
                          Try Again
                        </button>
                      </p>
                      <p>
                        <button 
                          type="button" 
                          className="link-btn"
                          onClick={handleBackToLogin}
                        >
                          Back to Login
                        </button>
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Right side - Image */}
        <div className="modal-image-section">
          <img src={forgotPasswordImage} alt="Forgot Password" className="modal-image" />
          <div className="image-overlay">
            {/* Decorative white shapes */}
            <div className="decorative-shape top-left-shape"></div>
            <div className="decorative-shape bottom-right-shape"></div>
            
            <div className="overlay-content">
              <div className="overlay-card">
                <h3>Reset Made Easy!</h3>
                <p>We'll help you get back into your account quickly and securely.</p>
              </div>
              <div className="bottom-overlay">
                <h3>Secure Password Recovery,<br/>Your Way!</h3>
                <p>Account security is our priority.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordModal; 
const { db<PERSON><PERSON><PERSON> } = require('./backend/config/supabase');

async function testSupportTickets() {
  try {
    console.log('🔍 Testing Support Ticket System...\n');

    // Check if there are any support tickets
    console.log('1. Checking support tickets...');
    const tickets = await dbHelpers.query('SELECT * FROM support_tickets ORDER BY created_at DESC LIMIT 5');
    console.log(`Found ${tickets.length} support tickets:`);
    tickets.forEach(ticket => {
      console.log(`  - ID: ${ticket.id.slice(-8)}, Subject: ${ticket.subject}, Status: ${ticket.status}, Client: ${ticket.client_id.slice(-8)}`);
    });

    // Check admin-client assignments
    console.log('\n2. Checking admin-client assignments...');
    const assignments = await dbHelpers.query(`
      SELECT aca.*, a.name as admin_name, c.username as client_name 
      FROM admin_client_assignments aca
      JOIN admins a ON aca.admin_id = a.id
      JOIN clients c ON aca.client_id = c.id
      LIMIT 10
    `);
    console.log(`Found ${assignments.length} admin-client assignments:`);
    assignments.forEach(assignment => {
      console.log(`  - Admin: ${assignment.admin_name}, Client: ${assignment.client_name}`);
    });

    // Check if there are any admins
    console.log('\n3. Checking admins...');
    const admins = await dbHelpers.query('SELECT id, admin_id, name, role FROM admins');
    console.log(`Found ${admins.length} admins:`);
    admins.forEach(admin => {
      console.log(`  - ID: ${admin.id.slice(-8)}, Admin ID: ${admin.admin_id}, Name: ${admin.name}, Role: ${admin.role}`);
    });

    // Check if there are any clients
    console.log('\n4. Checking clients...');
    const clients = await dbHelpers.query('SELECT id, username, email FROM clients LIMIT 5');
    console.log(`Found ${clients.length} clients:`);
    clients.forEach(client => {
      console.log(`  - ID: ${client.id.slice(-8)}, Username: ${client.username}, Email: ${client.email}`);
    });

    // Test the admin support query
    if (admins.length > 0 && tickets.length > 0) {
      console.log('\n5. Testing admin support ticket query...');
      const adminId = admins[0].id;
      const adminTickets = await dbHelpers.query(`
        SELECT st.*, c.username as client_name, c.email as client_email, c.business_name,
               a.name as admin_name
        FROM support_tickets st
        JOIN admin_client_assignments aca ON st.client_id = aca.client_id
        JOIN clients c ON st.client_id = c.id
        LEFT JOIN admins a ON st.admin_id = a.id
        WHERE aca.admin_id = $1
        ORDER BY st.created_at DESC
      `, [adminId]);
      
      console.log(`Admin ${admins[0].name} can see ${adminTickets.length} tickets:`);
      adminTickets.forEach(ticket => {
        console.log(`  - Subject: ${ticket.subject}, Client: ${ticket.client_name}, Status: ${ticket.status}`);
      });
    }

    console.log('\n✅ Support ticket system test completed!');

  } catch (error) {
    console.error('❌ Error testing support tickets:', error);
  }
}

testSupportTickets();

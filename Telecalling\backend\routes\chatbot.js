const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { supabaseAdmin } = require('../config/supabase');
const { authenticate: auth } = require('../middleware/auth');
const openaiService = require('../services/openaiService');
const OpenAI = require('openai');

const router = express.Router();

// Initialize OpenAI with GPT-3.5-turbo for cost efficiency
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Fetch all client data for chatbot context
const fetchClientData = async (clientId) => {
  try {
    console.log('🤖 Fetching client data for chatbot context:', clientId);

    // Fetch client profile
    const { data: client, error: clientError } = await supabaseAdmin
      .from('clients')
      .select('*')
      .eq('id', clientId)
      .single();

    if (clientError) throw clientError;

    // Fetch products
    const { data: products, error: productsError } = await supabaseAdmin
      .from('products')
      .select('*')
      .eq('client_id', clientId)
      .order('created_at', { ascending: false });

    if (productsError) throw productsError;

    // Fetch recent call logs (last 50)
    const { data: callLogs, error: callLogsError } = await supabaseAdmin
      .from('call_logs')
      .select('*')
      .eq('client_id', clientId)
      .order('call_time', { ascending: false })
      .limit(50);

    if (callLogsError) throw callLogsError;

    // Fetch recent SMS logs (last 30)
    const { data: smsLogs, error: smsLogsError } = await supabaseAdmin
      .from('sms_logs')
      .select('*')
      .eq('client_id', clientId)
      .order('sms_timestamp', { ascending: false })
      .limit(30);

    if (smsLogsError) throw smsLogsError;

    // Fetch customers associated with this client
    const { data: customers, error: customersError } = await supabaseAdmin
      .from('customer_interactions')
      .select(`
        customers (
          id,
          name,
          email,
          phone,
          address,
          city,
          state
        ),
        interaction_count,
        last_interaction_at,
        status,
        conversation_summary,
        product_interests,
        estimated_value
      `)
      .eq('client_id', clientId)
      .order('last_interaction_at', { ascending: false })
      .limit(100);

    if (customersError) throw customersError;

    // Fetch appointments
    const { data: appointments, error: appointmentsError } = await supabaseAdmin
      .from('appointment_slots')
      .select('*')
      .eq('client_id', clientId)
      .order('slot_time', { ascending: false })
      .limit(50);

    if (appointmentsError) throw appointmentsError;

    // Fetch subscription and billing info
    const { data: subscription, error: subscriptionError } = await supabaseAdmin
      .from('client_subscriptions')
      .select(`
        *,
        subscription_plans (
          name,
          display_name,
          price,
          features,
          max_calls,
          max_products
        )
      `)
      .eq('client_id', clientId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    // Don't throw error if no subscription found
    if (subscriptionError && subscriptionError.code !== 'PGRST116') {
      console.warn('Subscription fetch warning:', subscriptionError);
    }

    // Fetch usage tracking
    const { data: usage, error: usageError } = await supabaseAdmin
      .from('client_usage_tracking')
      .select('*')
      .eq('client_id', clientId)
      .order('month', { ascending: false })
      .limit(3);

    if (usageError) throw usageError;

    console.log('✅ Client data fetched successfully');
    
    return {
      client,
      products: products || [],
      callLogs: callLogs || [],
      smsLogs: smsLogs || [],
      customers: customers || [],
      appointments: appointments || [],
      subscription: subscription || null,
      usage: usage || []
    };

  } catch (error) {
    console.error('❌ Error fetching client data:', error);
    throw error;
  }
};

// Generate system prompt based on client data
const generateSystemPrompt = (clientData) => {
  const { client, products, callLogs, smsLogs, customers, appointments, subscription, usage } = clientData;
  
  return `You are an intelligent assistant for ${client.business_name || client.shop_name}, a ${client.business_type} business. 
You have access to comprehensive business data and should help the client with queries about their business operations.

BUSINESS INFORMATION:
- Business Name: ${client.business_name || client.shop_name}
- Business Type: ${client.business_type}
- Business Summary: ${client.business_summary || 'Not provided'}
- WhatsApp Number: ${client.whatsapp_number}
- Assigned Phone Number: ${client.assigned_plivo_number || 'Not assigned'}
- Business Address: ${client.business_address || 'Not provided'}
- Owner: ${client.owner_name || 'Not provided'}

PRODUCTS CATALOG (${products.length} products):
${products.map(p => `- ${p.product_name}: ${JSON.stringify(p.product_details)} (Aliases: ${p.alias_names?.join(', ') || 'None'})`).join('\n')}

RECENT CALL ACTIVITY (${callLogs.length} recent calls):
${callLogs.slice(0, 10).map(call => `- ${call.caller_number} on ${new Date(call.call_time).toLocaleDateString()}: ${call.summary || 'No summary'} (Duration: ${call.duration}s)`).join('\n')}

CUSTOMER BASE (${customers.length} customers):
${customers.slice(0, 10).map(c => `- ${c.customers?.name || 'Unknown'} (${c.customers?.phone || 'No phone'}): ${c.interaction_count} interactions, Status: ${c.status}`).join('\n')}

APPOINTMENTS (${appointments.length} slots):
${appointments.slice(0, 5).map(apt => `- ${new Date(apt.slot_time).toLocaleString()}: ${apt.is_booked ? `Booked by ${apt.booked_by_name}` : 'Available'}`).join('\n')}

SUBSCRIPTION STATUS:
${subscription ? `Plan: ${subscription.subscription_plans?.display_name} ($${subscription.subscription_plans?.price}) - Status: ${subscription.status}` : 'No active subscription'}

USAGE STATISTICS:
${usage.length > 0 ? `Current month - Calls: ${usage[0]?.calls_made || 0}, SMS: ${usage[0]?.sms_sent || 0}` : 'No usage data available'}

INSTRUCTIONS:
1. Be helpful, professional, and knowledgeable about the business
2. Answer questions about products, customers, calls, appointments, and business performance
3. Provide insights and suggestions based on the data
4. Keep responses concise but informative
5. If asked about specific data not available, politely explain what information you have access to
6. Help with business analysis, customer insights, and operational questions
7. Be conversational and friendly while maintaining professionalism

Remember: You are representing ${client.business_name || client.shop_name} and should always provide accurate information based on the available data.`;
};

// Chat endpoint
router.post('/chat', auth, async (req, res) => {
  try {
    const { message, conversationHistory = [] } = req.body;
    
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Message is required and must be a non-empty string'
      });
    }

    // Get client ID from authenticated user
    const clientId = req.user.id;
    
    // Fetch all client data for context
    const clientData = await fetchClientData(clientId);
    
    // Generate system prompt
    const systemPrompt = generateSystemPrompt(clientData);
    
    // Prepare conversation messages
    const messages = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory.slice(-10), // Keep last 10 messages for context
      { role: 'user', content: message.trim() }
    ];

    console.log('🤖 Sending request to OpenAI GPT-3.5-turbo');
    
    // Call OpenAI API with GPT-3.5-turbo for cost efficiency
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages,
      max_tokens: 500, // Limit response length for cost efficiency
      temperature: 0.7, // Balanced creativity and consistency
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    });

    const aiResponse = completion.choices[0].message.content;
    
    console.log('✅ OpenAI response received');
    
    res.json({
      success: true,
      response: aiResponse,
      usage: {
        promptTokens: completion.usage.prompt_tokens,
        completionTokens: completion.usage.completion_tokens,
        totalTokens: completion.usage.total_tokens
      }
    });

  } catch (error) {
    console.error('❌ Chatbot error:', error);
    
    if (error.code === 'insufficient_quota') {
      return res.status(402).json({
        success: false,
        error: 'OpenAI API quota exceeded. Please check your billing.',
        code: 'QUOTA_EXCEEDED'
      });
    }
    
    if (error.code === 'invalid_api_key') {
      return res.status(401).json({
        success: false,
        error: 'OpenAI API key is invalid or missing.',
        code: 'INVALID_API_KEY'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Failed to process chat request',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get client data summary (for chatbot initialization)
router.get('/context', auth, async (req, res) => {
  try {
    const clientId = req.user.id;
    const clientData = await fetchClientData(clientId);
    
    // Return summarized data for frontend
    res.json({
      success: true,
      context: {
        businessName: clientData.client.business_name || clientData.client.shop_name,
        businessType: clientData.client.business_type,
        productsCount: clientData.products.length,
        customersCount: clientData.customers.length,
        recentCallsCount: clientData.callLogs.length,
        appointmentsCount: clientData.appointments.length,
        subscriptionStatus: clientData.subscription?.status || 'inactive'
      }
    });
    
  } catch (error) {
    console.error('❌ Error fetching chatbot context:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch chatbot context'
    });
  }
});

module.exports = router;

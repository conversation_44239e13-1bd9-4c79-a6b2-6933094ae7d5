import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Phone, 
  MessageSquare, 
  Calendar, 
  CreditCard, 
  TrendingUp, 
  TrendingDown,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  ArrowRight,
  Activity,
  Package,
  FileText,
  Settings
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { callLogsAPI, appointmentsAPI, paymentsAPI, whatsappAPI } from '../../services/api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Badge from '../../components/common/Badge';

import PaymentCard from '../../components/dashboard/PaymentCard';
import PaymentStatsCard from '../../components/dashboard/PaymentStatsCard';

const DashboardPage = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCalls: 0,
    answeredCalls: 0,
    missedCalls: 0,
    totalAppointments: 0,
    totalRevenue: 0,
    whatsappSent: 0
  });
  const [recentCalls, setRecentCalls] = useState([]);
  const [recentAppointments, setRecentAppointments] = useState([]);
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch stats and recent data
      const [callsResponse, appointmentsResponse, paymentsResponse, whatsappResponse] = await Promise.all([
        callLogsAPI.getAll(),
        appointmentsAPI.getAll(),
        paymentsAPI.getAll(),
        whatsappAPI.getAll()
      ]);

      // Calculate stats
      const calls = callsResponse.data || [];
      const appointments = appointmentsResponse.data || [];
      const payments = paymentsResponse.data || [];
      const whatsappLogs = whatsappResponse.data || [];

      const totalCalls = calls.length;
      const answeredCalls = calls.filter(call => call.status === 'completed').length;
      const missedCalls = calls.filter(call => call.status === 'missed').length;
      const totalRevenue = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);

      setStats({
        totalCalls,
        answeredCalls,
        missedCalls,
        totalAppointments: appointments.length,
        totalRevenue,
        whatsappSent: whatsappLogs.length
      });

      // Set recent data
      setRecentCalls(calls.slice(0, 5));
      setRecentAppointments(appointments.slice(0, 5));

      // Generate chart data (last 7 days)
      const last7Days = Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        return date.toISOString().split('T')[0];
      }).reverse();

      const chartData = last7Days.map(date => {
        const daysCalls = calls.filter(call => call.created_at?.startsWith(date));
        const daysAppointments = appointments.filter(apt => apt.created_at?.startsWith(date));
        
        return {
          date: new Date(date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
          calls: daysCalls.length,
          appointments: daysAppointments.length
        };
      });

      setChartData(chartData);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success">Completed</Badge>;
      case 'missed':
        return <Badge variant="danger">Missed</Badge>;
      case 'in_progress':
        return <Badge variant="warning">In Progress</Badge>;
      case 'confirmed':
        return <Badge variant="success">Confirmed</Badge>;
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      case 'cancelled':
        return <Badge variant="danger">Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: 'short'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="large" message="Loading dashboard..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">


      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard Overview
          </h2>
          <p className="text-gray-600 mt-1">
            Welcome back! Here's what's happening with your business today.
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button 
            onClick={fetchDashboardData}
            className="btn btn-outline"
          >
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Phone className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Calls</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.totalCalls}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="text-green-600 font-medium">{stats.answeredCalls} answered</span>
              <span className="text-gray-500"> • </span>
              <span className="text-red-600 font-medium">{stats.missedCalls} missed</span>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Appointments</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.totalAppointments}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <Link to="/dashboard/appointments" className="text-purple-600 hover:text-purple-900">
                View all appointments
              </Link>
            </div>
          </div>
        </div>

        <PaymentStatsCard />

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MessageSquare className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">WhatsApp Messages</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.whatsappSent}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <Link to="/dashboard/whatsapp" className="text-green-600 hover:text-green-900">
                View messages
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <PaymentCard />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Call Activity (Last 7 Days)</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="calls" stroke="#3B82F6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Appointments (Last 7 Days)</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="appointments" fill="#8B5CF6" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Calls */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Calls</h3>
              <Link to="/dashboard/calls" className="text-sm text-blue-600 hover:text-blue-900">
                View all <ArrowRight className="w-4 h-4 inline ml-1" />
              </Link>
            </div>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentCalls.length > 0 ? (
                  recentCalls.map((call) => (
                    <li key={call.id} className="py-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <Phone className="h-4 w-4 text-blue-600" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {call.customer_phone || 'Unknown'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatTime(call.created_at)}
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          {getStatusBadge(call.status)}
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  <li className="py-4 text-center text-gray-500">
                    No recent calls
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* Recent Appointments */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Appointments</h3>
              <Link to="/dashboard/appointments" className="text-sm text-blue-600 hover:text-blue-900">
                View all <ArrowRight className="w-4 h-4 inline ml-1" />
              </Link>
            </div>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentAppointments.length > 0 ? (
                  recentAppointments.map((appointment) => (
                    <li key={appointment.id} className="py-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                            <Calendar className="h-4 w-4 text-purple-600" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {appointment.customer_name || 'Unknown Customer'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatTime(appointment.appointment_time)}
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          {getStatusBadge(appointment.status)}
                        </div>
                      </div>
                    </li>
                  ))
                ) : (
                  <li className="py-4 text-center text-gray-500">
                    No recent appointments
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              to="/dashboard/products"
              className="relative group bg-blue-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg hover:bg-blue-100"
            >
              <div className="flex items-center">
                <span className="rounded-lg inline-flex p-3 bg-blue-600 text-white">
                  <Package className="h-6 w-6" />
                </span>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">Manage Products</h3>
                  <p className="text-sm text-gray-500">Add or edit your product catalog</p>
                </div>
              </div>
            </Link>

            <Link
              to="/dashboard/appointments"
              className="relative group bg-purple-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-purple-500 rounded-lg hover:bg-purple-100"
            >
              <div className="flex items-center">
                <span className="rounded-lg inline-flex p-3 bg-purple-600 text-white">
                  <Calendar className="h-6 w-6" />
                </span>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">View Appointments</h3>
                  <p className="text-sm text-gray-500">Check your upcoming bookings</p>
                </div>
              </div>
            </Link>

            <Link
              to="/dashboard/invoices"
              className="relative group bg-green-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-green-500 rounded-lg hover:bg-green-100"
            >
              <div className="flex items-center">
                <span className="rounded-lg inline-flex p-3 bg-green-600 text-white">
                  <FileText className="h-6 w-6" />
                </span>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">Create Invoice</h3>
                  <p className="text-sm text-gray-500">Generate new invoices</p>
                </div>
              </div>
            </Link>

            <Link
              to="/dashboard/settings"
              className="relative group bg-gray-50 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-gray-500 rounded-lg hover:bg-gray-100"
            >
              <div className="flex items-center">
                <span className="rounded-lg inline-flex p-3 bg-gray-600 text-white">
                  <Settings className="h-6 w-6" />
                </span>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-900">Settings</h3>
                  <p className="text-sm text-gray-500">Configure your account</p>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage; 
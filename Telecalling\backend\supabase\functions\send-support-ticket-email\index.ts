import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
}

interface SupportTicketEmailRequest {
  ticketId: string
  subject: string
  message: string
  urgencyLevel: string
  clientName: string
  clientEmail: string
  businessName: string
  createdAt: string
}

const getEmailTemplate = (data: SupportTicketEmailRequest): string => {
  const urgencyColor = {
    'low': '#10b981',
    'medium': '#f59e0b', 
    'high': '#ef4444',
    'urgent': '#dc2626'
  }[data.urgencyLevel] || '#6b7280'

  const urgencyBg = {
    'low': '#dcfce7',
    'medium': '#fef3c7', 
    'high': '#fee2e2',
    'urgent': '#fecaca'
  }[data.urgencyLevel] || '#f3f4f6'

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Support Ticket - ${data.subject}</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
      
      <!-- Header -->
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700;">🎫 New Support Ticket</h1>
        <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Telecalling Support System</p>
      </div>

      <!-- Priority Badge -->
      <div style="text-align: center; margin-bottom: 25px;">
        <span style="background: ${urgencyColor}; color: white; padding: 8px 20px; border-radius: 25px; font-size: 14px; font-weight: 600; text-transform: uppercase; display: inline-block; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          ${data.urgencyLevel} Priority
        </span>
      </div>

      <!-- Ticket Info -->
      <div style="background: white; padding: 25px; border-radius: 12px; border-left: 4px solid ${urgencyColor}; margin-bottom: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
        <h2 style="margin: 0 0 15px 0; color: #1e293b; font-size: 22px; font-weight: 600;">${data.subject}</h2>
        <div style="background: ${urgencyBg}; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
          <p style="margin: 0; font-size: 14px; color: #374151;"><strong>Ticket ID:</strong> <code style="background: rgba(0,0,0,0.1); padding: 2px 6px; border-radius: 4px; font-family: monospace;">${data.ticketId}</code></p>
          <p style="margin: 8px 0 0 0; font-size: 14px; color: #374151;"><strong>Created:</strong> ${new Date(data.createdAt).toLocaleString()}</p>
        </div>
      </div>

      <!-- Client Information -->
      <div style="background: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
        <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 18px; font-weight: 600; border-bottom: 2px solid #f1f5f9; padding-bottom: 10px;">👤 Client Information</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
          <div style="background: #f8fafc; padding: 15px; border-radius: 8px;">
            <strong style="color: #64748b; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px;">Client Name</strong><br>
            <span style="color: #1e293b; font-size: 16px; font-weight: 500;">${data.clientName}</span>
          </div>
          <div style="background: #f8fafc; padding: 15px; border-radius: 8px;">
            <strong style="color: #64748b; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px;">Email</strong><br>
            <a href="mailto:${data.clientEmail}" style="color: #667eea; text-decoration: none; font-size: 16px; font-weight: 500;">${data.clientEmail}</a>
          </div>
          <div style="grid-column: span 2; background: #f8fafc; padding: 15px; border-radius: 8px;">
            <strong style="color: #64748b; font-size: 12px; text-transform: uppercase; letter-spacing: 0.5px;">Business</strong><br>
            <span style="color: #1e293b; font-size: 16px; font-weight: 500;">${data.businessName}</span>
          </div>
        </div>
      </div>

      <!-- Message -->
      <div style="background: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
        <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 18px; font-weight: 600; border-bottom: 2px solid #f1f5f9; padding-bottom: 10px;">💬 Message</h3>
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; white-space: pre-wrap; font-size: 15px; line-height: 1.6; color: #374151;">${data.message}</div>
      </div>

      <!-- Action Buttons -->
      <div style="text-align: center; margin-bottom: 30px;">
        <a href="mailto:${data.clientEmail}?subject=Re: ${data.subject} (Ticket #${data.ticketId})" style="background: #667eea; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; margin-right: 10px; display: inline-block;">Reply to Client</a>
        <a href="http://localhost:4000" style="background: #10b981; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; display: inline-block;">Admin Panel</a>
      </div>

      <!-- Footer -->
      <div style="background: #1e293b; color: white; padding: 20px; border-radius: 12px; text-align: center;">
        <p style="margin: 0; font-size: 14px; opacity: 0.8;">
          🤖 This email was automatically generated from the Telecalling Support System<br>
          <strong>Tecnvi AI</strong> - Telecalling Support Team
        </p>
      </div>

    </body>
    </html>
  `
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY not found')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const requestData: SupportTicketEmailRequest = await req.json()
    
    console.log('📧 Processing support ticket email request:', {
      ticketId: requestData.ticketId,
      subject: requestData.subject,
      urgencyLevel: requestData.urgencyLevel,
      clientEmail: requestData.clientEmail
    })

    const htmlContent = getEmailTemplate(requestData)
    const subject = `[SUPPORT] ${requestData.urgencyLevel.toUpperCase()} - ${requestData.subject}`

    // Send email using Resend
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Telecalling Support <<EMAIL>>',
        to: ['<EMAIL>'],
        subject: subject,
        html: htmlContent,
        text: `New Support Ticket: ${requestData.subject}\n\nFrom: ${requestData.clientName} (${requestData.clientEmail})\nBusiness: ${requestData.businessName}\nPriority: ${requestData.urgencyLevel}\n\nMessage:\n${requestData.message}\n\nTicket ID: ${requestData.ticketId}\nCreated: ${new Date(requestData.createdAt).toLocaleString()}`
      }),
    })

    if (!emailResponse.ok) {
      const errorData = await emailResponse.text()
      console.error('Resend API error:', errorData)
      return new Response(
        JSON.stringify({ error: 'Failed to send email' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const emailData = await emailResponse.json()
    console.log('✅ Support ticket email sent successfully:', emailData)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Support ticket email sent successfully',
        emailId: emailData.id
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('❌ Error in send-support-ticket-email function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

# VoiceBot Platform - Frontend

React-based frontend application for the VoiceBot Telecalling platform.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build
```

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Backend server running on port 5000

## 🛠️ Development

### Available Scripts

```bash
# Start development server (http://localhost:3000)
npm start

# Run tests
npm test

# Build for production
npm run build

# Eject from Create React App (⚠️ irreversible)
npm run eject
```

### Development Server

The development server includes:
- Hot reloading
- Error overlay
- Automatic browser opening
- Proxy to backend API (port 5000)

## 🏗️ Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── assets/            # Images and static files
│   │   └── images/        # Authentication images
│   ├── components/        # Reusable React components
│   │   ├── common/        # Shared components
│   │   ├── layout/        # Layout components
│   │   └── modals/        # Modal components
│   ├── contexts/          # React contexts
│   │   └── AuthContext.js # Authentication context
│   ├── pages/             # Page components
│   │   ├── admin/         # Admin pages
│   │   └── dashboard/     # Dashboard pages
│   ├── services/          # API services
│   │   └── api.js         # API configuration
│   ├── styles/            # CSS styles
│   ├── App.js             # Main application component
│   └── index.js           # Application entry point
├── package.json           # Dependencies and scripts
└── README.md             # This file
```

## 🎨 Components

### Common Components
- `Badge.js` - Status badges
- `Button.js` - Styled buttons
- `LoadingSpinner.js` - Loading indicators
- `Modal.js` - Modal dialogs

### Layout Components
- `DashboardLayout.js` - Main dashboard layout

### Modal Components
- `LoginModal.js` - User login
- `SignupModal.js` - User registration
- `EmailVerificationModal.js` - Email verification
- `ForgotPasswordModal.js` - Password reset
- `ResetPasswordModal.js` - Password reset form
- `ModalManager.js` - Modal state management

### Page Components
- `LandingPage.js` - Landing page
- `LoginPage.js` - Login page
- `SignupPage.js` - Registration page
- `OnboardingPage.js` - User onboarding
- `DashboardPage.js` - Main dashboard
- `Plan499Dashboard.js` - Specific plan dashboard

### Dashboard Pages
- `AppointmentsPage.js` - Appointment management
- `CallLogsPage.js` - Call history
- `InvoicesPage.js` - Invoice management
- `PaymentsPage.js` - Payment history
- `ProductsPage.js` - Product catalog
- `SettingsPage.js` - User settings
- `WhatsAppPage.js` - WhatsApp messaging

## 🌐 API Integration

### Backend Connection

The frontend connects to the backend via:
- Proxy configuration in `package.json`
- API service in `src/services/api.js`
- Base URL: `http://localhost:5000`

### API Service

```javascript
// Example API usage
import api from '../services/api';

// GET request
const clients = await api.get('/api/clients');

// POST request
const newClient = await api.post('/api/clients', clientData);

// With authentication
api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
```

## 🔐 Authentication

### AuthContext

The app uses React Context for authentication:

```javascript
import { useAuth } from '../contexts/AuthContext';

function Component() {
  const { user, login, logout, isLoading } = useAuth();
  
  // Use authentication state
}
```

### Protected Routes

Routes are protected based on authentication status and user roles.

## 🎯 Features

### Authentication Flow
- Landing page with login/signup options
- Modal-based authentication
- Email verification
- Password reset
- Persistent sessions

### Dashboard Features
- Client management
- Appointment scheduling
- Call logs and history
- Product catalog
- Invoice management
- Payment tracking
- WhatsApp integration
- Settings and profile

### Admin Features
- User management
- System administration
- Analytics and reporting

## 🎨 Styling

### CSS Architecture
- `index.css` - Global styles
- `App.css` - Application-specific styles
- `styles/common.css` - Shared component styles
- `styles/landing.css` - Landing page styles
- `styles/plan499Dashboard.css` - Dashboard styles
- Component-specific CSS files in component directories

### Design System
- Consistent color palette
- Typography scale
- Spacing units
- Component variants

## 📱 Responsive Design

The application is responsive and works on:
- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🚨 Troubleshooting

### Development Issues

**1. Server won't start:**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm start
```

**2. Proxy errors:**
- Ensure backend is running on port 5000
- Check proxy configuration in `package.json`

**3. API connection issues:**
```bash
# Test backend connectivity
curl http://localhost:5000/health
```

### Common Errors

**"npm ERR! code EADDRINUSE"**
- Port 3000 is in use
- Use different port: `PORT=3001 npm start`

**"Module not found"**
- Missing dependencies: `npm install`
- Clear cache: `npm start -- --reset-cache`

**"Failed to fetch"**
- Backend not running
- CORS issues
- Network connectivity

### Build Issues

**"npm run build fails"**
```bash
# Increase memory limit
NODE_OPTIONS=--max_old_space_size=4096 npm run build

# Check for warnings
npm run build 2>&1 | grep -i warn
```

## 🔧 Configuration

### Environment Variables

Create `.env` file for custom configuration:

```env
# Custom port
PORT=3001

# API URL (if not using proxy)
REACT_APP_API_URL=http://localhost:5000

# Other configuration
REACT_APP_APP_NAME=VoiceBot Platform
```

### Proxy Configuration

Configured in `package.json`:
```json
{
  "proxy": "http://localhost:5000"
}
```

## 📦 Dependencies

### Main Dependencies
- **React** (^18.2.0) - UI library
- **React Router** (^6.14.1) - Routing
- **Axios** (^1.4.0) - HTTP client
- **React Hook Form** (^7.45.1) - Form handling
- **React Hot Toast** (^2.4.1) - Notifications
- **Lucide React** (^0.263.1) - Icons
- **Recharts** (^2.7.2) - Charts and graphs

### Development Dependencies
- **React Scripts** (5.0.1) - Build tools
- **Testing Library** - Testing utilities

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

## 🚀 Deployment

### Production Build

```bash
# Create optimized build
npm run build

# Serve locally (for testing)
npx serve -s build
```

### Environment Setup

For production deployment:
1. Build the application: `npm run build`
2. Serve the `build` folder with a web server
3. Configure environment variables
4. Set up proper routing (SPA)

## 🔍 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 🤝 Development Guidelines

1. **Component Structure:** Keep components small and focused
2. **State Management:** Use local state and Context API
3. **Error Handling:** Implement proper error boundaries
4. **Performance:** Use React.memo and useMemo when needed
5. **Accessibility:** Follow WCAG guidelines

## 📝 Code Style

- Use functional components with hooks
- Follow naming conventions (PascalCase for components)
- Keep files under 300 lines when possible
- Use TypeScript-style JSDoc comments

## 🎯 Performance

### Optimization Tips
- Lazy load components with React.lazy()
- Use React.memo for expensive components
- Optimize images and assets
- Code splitting for large features

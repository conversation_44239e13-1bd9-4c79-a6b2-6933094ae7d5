const jwt = require('jsonwebtoken');
const { db<PERSON><PERSON><PERSON>, supabaseAuthHelpers } = require('../config/supabase');

// Authentication middleware (supports both JWT and Supabase Auth)
const auth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'No token provided' 
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    let user = null;
    
    // Try JWT first (our custom tokens for Google auth users)
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('🔍 JWT decoded:', { type: decoded.type, userId: decoded.userId });

      // Ensure it's an access token
      if (decoded.type === 'access') {
        // Get user from database using our internal ID
        user = await dbHelpers.findOne('clients', { id: decoded.userId });
        console.log('🔍 User lookup result:', user ? `Found: ${user.email}` : 'Not found');

        if (user && user.is_active) {
          // Mark as JWT user
          user._authMethod = 'jwt';
          console.log('✅ JWT verification successful for user:', user.email);
        } else if (user && !user.is_active) {
          console.log('❌ User found but inactive:', user.email);
        }
      } else {
        console.log('❌ Invalid token type:', decoded.type);
      }
    } catch (jwtError) {
      console.log('❌ JWT verification failed, trying Supabase Auth:', jwtError.message);
    }

    // If JWT didn't work, try Supabase Auth (for legacy users)
    if (!user) {
      try {
        const supabaseUser = await supabaseAuthHelpers.verifyToken(token);
        if (supabaseUser) {
          // Get user from our clients table using supabase_auth_id
          user = await dbHelpers.findOne('clients', { supabase_auth_id: supabaseUser.id });

          if (user) {
            // Mark as Supabase Auth user
            user._authMethod = 'supabase';
            console.log('✅ Supabase verification successful for user:', user.email);
          }
        }
      } catch (supabaseError) {
        console.log('❌ Supabase verifyToken error:', supabaseError.message);
      }
    }
    
    // We already tried JWT first, so no need to try again
    
    if (!user) {
      console.log('❌ Authentication failed: User not found in database');
      console.log('- Token preview:', token.substring(0, 20) + '...');
      return res.status(401).json({
        error: 'Access denied',
        message: 'User not found - Please log in again'
      });
    }
    
    // Check if user is active
    if (!user.is_active) {
      return res.status(403).json({ 
        error: 'Access denied', 
        message: 'Account is deactivated' 
      });
    }
    
    // Check if account is locked (only for JWT users, Supabase handles its own locking)
    if (user._authMethod === 'jwt' && user.locked_until && new Date() < new Date(user.locked_until)) {
      return res.status(423).json({ 
        error: 'Access denied', 
        message: 'Account is temporarily locked' 
      });
    }
    
    // Attach user to request
    req.user = {
      id: user.id,
      authId: user.supabase_auth_id || user.id, // Use supabase_auth_id if available, fallback to internal id
      email: user.email,
      businessName: user.business_name,
      username: user.username,
      phone: user.phone,
      isEmailVerified: user.is_email_verified,
      isActive: user.is_active,
      createdAt: user.created_at,
      lastLoginAt: user.last_login_at,
      _authMethod: user._authMethod
    };
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Invalid token' 
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Token expired' 
      });
    }
    
    return res.status(500).json({ 
      error: 'Internal server error', 
      message: 'Authentication failed' 
    });
  }
};

// Optional authentication middleware - doesn't fail if no token
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without authentication
      return next();
    }

    const token = authHeader.substring(7);
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      if (decoded.type === 'access') {
        const user = await dbHelpers.findOne('clients', { id: decoded.userId });
        
        if (user && user.is_active) {
          req.user = {
            id: user.id,
            email: user.email,
            businessName: user.business_name,
            username: user.username,
            phone: user.phone,
            isEmailVerified: user.is_email_verified,
            isActive: user.is_active,
            createdAt: user.created_at,
            lastLoginAt: user.last_login_at
          };
        }
      }
    } catch (tokenError) {
      // Token is invalid but we continue without authentication
      console.log('Optional auth - invalid token:', tokenError.message);
    }
    
    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    next(); // Continue without authentication
  }
};

// Middleware to check if user is verified
const requireVerified = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      error: 'Access denied', 
      message: 'Authentication required' 
    });
  }

  if (!req.user.isEmailVerified) {
    return res.status(403).json({ 
      error: 'Access denied', 
      message: 'Email verification required',
      code: 'EMAIL_NOT_VERIFIED'
    });
  }
  
  next();
};

// Middleware to check if user owns the resource
const checkOwnership = (resourceKey = 'client_id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          error: 'Access denied', 
          message: 'Authentication required' 
        });
      }
      
      // Get the resource ID from params or body
      const resourceId = req.params[resourceKey] || req.body[resourceKey];
      
      if (resourceId && resourceId !== req.user.id) {
        return res.status(403).json({ 
          error: 'Access denied', 
          message: 'You can only access your own resources' 
        });
      }
      
      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({ 
        error: 'Internal server error', 
        message: 'Access check failed' 
      });
    }
  };
};

// Middleware to validate request body
const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        message: error.details[0].message,
        details: error.details
      });
    }
    
    next();
  };
};

// Middleware to check for active session
const requireActiveSession = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Authentication required' 
      });
    }
    
    const authHeader = req.headers.authorization;
    const token = authHeader.substring(7);
    
    // Check if session exists and is active
    const session = await dbHelpers.findOne('login_sessions', {
      client_id: req.user.id,
      session_token: token,
      is_active: true
    });
    
    if (!session) {
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Session not found or inactive' 
      });
    }
    
    // Check if session is expired
    if (new Date() > new Date(session.expires_at)) {
      // Mark session as inactive
      await dbHelpers.update('login_sessions', {
        is_active: false
      }, { id: session.id });
      
      return res.status(401).json({ 
        error: 'Access denied', 
        message: 'Session expired' 
      });
    }
    
    // Update last accessed time
    await dbHelpers.update('login_sessions', {
      last_accessed_at: new Date()
    }, { id: session.id });
    
    req.session = session;
    next();
  } catch (error) {
    console.error('Session check error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      message: 'Session check failed' 
    });
  }
};

// Legacy middleware for backward compatibility
const authenticate = auth;

// Alias for client authentication
const requireClient = auth;

// Admin authentication middleware
const requireAdmin = async (req, res, next) => {
  try {
    // First check if user is authenticated
    await new Promise((resolve, reject) => {
      auth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // Check if user has admin role (you can implement admin role logic here)
    // For now, we'll just check if user is authenticated and verified
    if (!req.user.isEmailVerified) {
      return res.status(403).json({ 
        error: 'Access denied', 
        message: 'Admin access requires email verification' 
      });
    }
    
    // TODO: Add admin role check here when you implement admin roles
    // For now, all verified users can access admin endpoints
    next();
  } catch (error) {
    console.error('Admin authentication error:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      message: 'Admin authentication failed' 
    });
  }
};

module.exports = {
  auth,
  authenticate, // Legacy alias
  requireClient,
  requireAdmin,
  optionalAuth,
  requireVerified,
  checkOwnership,
  validateRequest,
  requireActiveSession
}; 
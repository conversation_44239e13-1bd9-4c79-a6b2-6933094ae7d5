const express = require('express');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { dbHelpers } = require('../config/supabase');
const { adminAuth } = require('../middleware/adminAuth');
const router = express.Router();

// Get all clients assigned to admin (max 250)
router.get('/clients', adminAuth, async (req, res) => {
  try {
    const { page = 1, limit = 50, search } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT c.*, ps.plan_name, ps.call_minutes_used, ps.sms_sent, ps.calls_made
      FROM clients c
      LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
      LEFT JOIN plan_subscriptions ps ON c.id = ps.client_id AND ps.is_active = true
      WHERE aca.admin_id = $1
    `;
    let params = [req.admin.id];
    let paramIndex = 2;

    // Add search filter if provided
    if (search) {
      query += ` AND (c.username ILIKE $${paramIndex} OR c.email ILIKE $${paramIndex} OR c.business_name ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    query += ` ORDER BY c.created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(parseInt(limit), offset);

    const clients = await dbHelpers.query(query, params);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM clients c
      LEFT JOIN admin_client_assignments aca ON c.id = aca.client_id
      WHERE aca.admin_id = $1
    `;
    let countParams = [req.admin.id];

    if (search) {
      countQuery += ` AND (c.username ILIKE $2 OR c.email ILIKE $2 OR c.business_name ILIKE $2)`;
      countParams.push(`%${search}%`);
    }

    const totalResult = await dbHelpers.query(countQuery, countParams);
    const total = parseInt(totalResult[0].total);

    res.json({
      message: 'Clients retrieved successfully',
      clients: clients.map(client => ({
        id: client.id,
        username: client.username,
        email: client.email,
        businessName: client.business_name,
        businessSummary: client.business_summary,
        ownerName: client.owner_name,
        mobileNumber: client.mobile_number,
        businessEmail: client.business_email,
        whatsappNumber: client.whatsapp_number,
        assignedPlivoNumber: client.assigned_plivo_number,
        isActive: client.is_active,
        createdAt: client.created_at,
        lastLoginAt: client.last_login_at,
        planName: client.plan_name,
        usage: {
          callMinutes: client.call_minutes_used || 0,
          sms: client.sms_sent || 0,
          calls: client.calls_made || 0
        }
      })),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get admin clients error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve clients',
      message: 'An error occurred while retrieving clients'
    });
  }
});

// Assign Plivo number to client
router.put('/clients/:clientId/assign-number', adminAuth, async (req, res) => {
  try {
    const { plivoNumber } = req.body;
    const { clientId } = req.params;

    if (!plivoNumber) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Plivo number is required'
      });
    }

    // Validate phone number format
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(plivoNumber.replace(/[\s\-\(\)]/g, ''))) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Invalid phone number format'
      });
    }

    // Check if client is assigned to this admin
    const assignment = await dbHelpers.findOne('admin_client_assignments', {
      admin_id: req.admin.id,
      client_id: clientId
    });

    if (!assignment) {
      return res.status(403).json({ 
        error: 'Access denied',
        message: 'Client not assigned to this admin'
      });
    }

    // Check if number is already assigned to another client
    const existingAssignment = await dbHelpers.findOne('clients', {
      assigned_plivo_number: plivoNumber
    });

    if (existingAssignment && existingAssignment.id !== clientId) {
      return res.status(409).json({ 
        error: 'Number already assigned',
        message: 'This Plivo number is already assigned to another client'
      });
    }

    // Update client with assigned number
    const updatedClient = await dbHelpers.update('clients', {
      assigned_plivo_number: plivoNumber,
      updated_at: new Date()
    }, { id: clientId });

    res.json({
      message: 'Plivo number assigned successfully',
      client: {
        id: updatedClient.id,
        username: updatedClient.username,
        email: updatedClient.email,
        assignedPlivoNumber: updatedClient.assigned_plivo_number
      }
    });

  } catch (error) {
    console.error('Assign Plivo number error:', error);
    res.status(500).json({ 
      error: 'Failed to assign Plivo number',
      message: 'An error occurred while assigning Plivo number'
    });
  }
});

// Remove Plivo number from client
router.delete('/clients/:clientId/remove-number', adminAuth, async (req, res) => {
  try {
    const { clientId } = req.params;

    // Check if client is assigned to this admin
    const assignment = await dbHelpers.findOne('admin_client_assignments', {
      admin_id: req.admin.id,
      client_id: clientId
    });

    if (!assignment) {
      return res.status(403).json({ 
        error: 'Access denied',
        message: 'Client not assigned to this admin'
      });
    }

    // Remove assigned number
    const updatedClient = await dbHelpers.update('clients', {
      assigned_plivo_number: null,
      updated_at: new Date()
    }, { id: clientId });

    res.json({
      message: 'Plivo number removed successfully',
      client: {
        id: updatedClient.id,
        username: updatedClient.username,
        email: updatedClient.email,
        assignedPlivoNumber: updatedClient.assigned_plivo_number
      }
    });

  } catch (error) {
    console.error('Remove Plivo number error:', error);
    res.status(500).json({ 
      error: 'Failed to remove Plivo number',
      message: 'An error occurred while removing Plivo number'
    });
  }
});

// Get client details
router.get('/clients/:clientId', adminAuth, async (req, res) => {
  try {
    const { clientId } = req.params;

    // Check if client is assigned to this admin
    const assignment = await dbHelpers.findOne('admin_client_assignments', {
      admin_id: req.admin.id,
      client_id: clientId
    });

    if (!assignment) {
      return res.status(403).json({ 
        error: 'Access denied',
        message: 'Client not assigned to this admin'
      });
    }

    // Get client details with plan subscription
    const clientData = await dbHelpers.query(`
      SELECT c.*, ps.plan_name, ps.plan_price, ps.call_minutes_limit, ps.sms_limit, ps.calls_limit,
             ps.call_minutes_used, ps.sms_sent, ps.calls_made, ps.start_date, ps.end_date
      FROM clients c
      LEFT JOIN plan_subscriptions ps ON c.id = ps.client_id AND ps.is_active = true
      WHERE c.id = $1
    `, [clientId]);

    if (!clientData.length) {
      return res.status(404).json({ 
        error: 'Client not found',
        message: 'Client not found'
      });
    }

    const client = clientData[0];

    // Get recent activity
    const recentCalls = await dbHelpers.query(`
      SELECT caller_number, call_timestamp, duration_minutes, call_status
      FROM call_logs 
      WHERE client_id = $1 
      ORDER BY call_timestamp DESC 
      LIMIT 10
    `, [clientId]);

    const recentSms = await dbHelpers.query(`
      SELECT recipient_number, sms_timestamp, status
      FROM sms_logs 
      WHERE client_id = $1 
      ORDER BY sms_timestamp DESC 
      LIMIT 10
    `, [clientId]);

    res.json({
      message: 'Client details retrieved successfully',
      client: {
        id: client.id,
        username: client.username,
        email: client.email,
        businessName: client.business_name,
        businessSummary: client.business_summary,
        businessAddress: client.business_address,
        ownerName: client.owner_name,
        mobileNumber: client.mobile_number,
        businessEmail: client.business_email,
        whatsappNumber: client.whatsapp_number,
        assignedPlivoNumber: client.assigned_plivo_number,
        isActive: client.is_active,
        createdAt: client.created_at,
        lastLoginAt: client.last_login_at,
        planInfo: {
          planName: client.plan_name,
          planPrice: client.plan_price,
          startDate: client.start_date,
          endDate: client.end_date,
          limits: {
            callMinutes: client.call_minutes_limit,
            sms: client.sms_limit,
            calls: client.calls_limit
          },
          usage: {
            callMinutes: client.call_minutes_used || 0,
            sms: client.sms_sent || 0,
            calls: client.calls_made || 0
          }
        },
        recentActivity: {
          calls: recentCalls,
          sms: recentSms
        }
      }
    });

  } catch (error) {
    console.error('Get client details error:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve client details',
      message: 'An error occurred while retrieving client details'
    });
  }
});

// Assign clients to admin (for initial setup)
router.post('/assign-clients', adminAuth, async (req, res) => {
  try {
    const { clientIds } = req.body;

    if (!Array.isArray(clientIds) || clientIds.length === 0) {
      return res.status(400).json({ 
        error: 'Validation error',
        message: 'Client IDs array is required'
      });
    }

    // Check current assignment count for admin
    const currentAssignments = await dbHelpers.query(
      'SELECT COUNT(*) as count FROM admin_client_assignments WHERE admin_id = $1',
      [req.admin.id]
    );

    const currentCount = parseInt(currentAssignments[0].count);
    
    if (currentCount + clientIds.length > 250) {
      return res.status(400).json({ 
        error: 'Assignment limit exceeded',
        message: `Cannot assign more than 250 clients to an admin. Current: ${currentCount}, Requested: ${clientIds.length}`
      });
    }

    // Assign clients to admin
    const assignments = [];
    for (const clientId of clientIds) {
      // Check if client is already assigned to another admin
      const existingAssignment = await dbHelpers.findOne('admin_client_assignments', {
        client_id: clientId
      });

      if (!existingAssignment) {
        const assignment = await dbHelpers.insert('admin_client_assignments', {
          id: uuidv4(),
          admin_id: req.admin.id,
          client_id: clientId,
          assigned_at: new Date()
        });
        assignments.push(assignment);
      }
    }

    res.json({
      message: 'Clients assigned successfully',
      assignedCount: assignments.length,
      totalAssignments: currentCount + assignments.length
    });

  } catch (error) {
    console.error('Assign clients error:', error);
    res.status(500).json({ 
      error: 'Failed to assign clients',
      message: 'An error occurred while assigning clients'
    });
  }
});

module.exports = router;

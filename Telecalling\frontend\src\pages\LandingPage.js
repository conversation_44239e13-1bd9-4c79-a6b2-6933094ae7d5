import React, { useEffect, useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Phone, MessageSquare, Calendar, CreditCard, BarChart3, Clock, Shield, Zap, Users, CheckCircle, ArrowRight, Play, Star, Award, Globe, Headphones } from 'lucide-react';
import SignupModal from '../components/modals/SignupModal';
import LoginModal from '../components/modals/LoginModal';
import EmailVerificationModal from '../components/modals/EmailVerificationModal';
import ForgotPasswordModal from '../components/modals/ForgotPasswordModal';
import ResetPasswordModal from '../components/modals/ResetPasswordModal';
import PaymentModal from '../components/modals/PaymentModal';
import PaymentSuccessModal from '../components/modals/PaymentSuccessModal';
import { usePaymentFlow } from '../hooks/usePaymentFlow';
import '../styles/common.css';
import '../styles/landing.css';

const LandingPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [isSignupModalOpen, setIsSignupModalOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isEmailVerificationModalOpen, setIsEmailVerificationModalOpen] = useState(false);
  const [isForgotPasswordModalOpen, setIsForgotPasswordModalOpen] = useState(false);
  const [isResetPasswordModalOpen, setIsResetPasswordModalOpen] = useState(false);
  const [userEmail, setUserEmail] = useState('');

  // Payment flow hook
  const {
    showPaymentModal,
    showSuccessModal,
    selectedPlan,
    paymentResult,
    closePaymentModal,
    handlePaymentSuccess,
    closeSuccessModal
  } = usePaymentFlow();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Check URL parameters to open appropriate modal
  useEffect(() => {
    const showLogin = searchParams.get('showLogin');
    const showSignup = searchParams.get('showSignup');
    const showReset = searchParams.get('showReset');

    if (showLogin === 'true') {
      setIsLoginModalOpen(true);
      // Clear the URL parameter
      setSearchParams({});
    } else if (showSignup === 'true') {
      setIsSignupModalOpen(true);
      // Clear the URL parameter
      setSearchParams({});
    } else if (showReset === 'true') {
      setIsResetPasswordModalOpen(true);
      // Clear the URL parameter
      setSearchParams({});
    }
  }, [searchParams, setSearchParams]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const openSignupModal = (e, selectedPlan = null) => {
    if (e) e.preventDefault();
    if (selectedPlan) {
      // Store selected plan in localStorage for after signup
      localStorage.setItem('selectedPlan', JSON.stringify(selectedPlan));
    }
    setIsSignupModalOpen(true);
    setIsLoginModalOpen(false);
  };

  const openLoginModal = (e, selectedPlan = null) => {
    if (e) e.preventDefault();
    if (selectedPlan) {
      // Store selected plan in localStorage for after login
      localStorage.setItem('selectedPlan', JSON.stringify(selectedPlan));
    }
    setIsLoginModalOpen(true);
    setIsSignupModalOpen(false);
  };

  const closeModals = () => {
    setIsSignupModalOpen(false);
    setIsLoginModalOpen(false);
    setIsEmailVerificationModalOpen(false);
    setIsForgotPasswordModalOpen(false);
    setIsResetPasswordModalOpen(false);
    setUserEmail('');
  };

  const switchToLogin = () => {
    setIsSignupModalOpen(false);
    setIsLoginModalOpen(true);
  };

  const switchToSignup = () => {
    setIsLoginModalOpen(false);
    setIsSignupModalOpen(true);
  };

  // New navigation handlers
  const openForgotPassword = () => {
    console.log('Opening forgot password modal');
    setIsLoginModalOpen(false);
    setIsForgotPasswordModalOpen(true);
  };

  const openEmailVerification = (email) => {
    console.log('Opening email verification for:', email);
    setIsSignupModalOpen(false);
    setIsEmailVerificationModalOpen(true);
    setUserEmail(email);
  };

  const openResetPassword = () => {
    console.log('Opening reset password modal');
    setIsForgotPasswordModalOpen(false);
    setIsResetPasswordModalOpen(true);
  };

  const handleEmailVerified = () => {
    console.log('Email verified successfully!');
    setIsEmailVerificationModalOpen(false);
    setIsLoginModalOpen(true);
    alert('Email verified successfully!');
  };

  const handlePasswordReset = () => {
    console.log('Password reset successfully!');
    setIsResetPasswordModalOpen(false);
    setIsLoginModalOpen(true);
    alert('Password reset successfully!');
  };

  const backToLogin = () => {
    console.log('Going back to login');
    setIsForgotPasswordModalOpen(false);
    setIsResetPasswordModalOpen(false);
    setIsLoginModalOpen(true);
  };

  const features = [
    {
      icon: <Phone className="feature-icon" />,
      title: "AI Voice Assistant",
      description: "24/7 automated customer call handling with intelligent responses tailored to your business needs.",
      color: "primary"
    },
    {
      icon: <MessageSquare className="feature-icon" />,
      title: "WhatsApp Integration",
      description: "Automatic follow-ups and messaging to customers after calls with estimates and payment links.",
      color: "secondary"
    },
    {
      icon: <Calendar className="feature-icon" />,
      title: "Appointment Booking",
      description: "Let customers book appointments directly through voice calls with real-time availability.",
      color: "accent"
    },
    {
      icon: <CreditCard className="feature-icon" />,
      title: "Payment Processing",
      description: "Generate and send payment links instantly through WhatsApp for seamless transactions.",
      color: "primary"
    },
    {
      icon: <BarChart3 className="feature-icon" />,
      title: "Analytics Dashboard",
      description: "Track call logs, customer interactions, and business performance with detailed insights.",
      color: "secondary"
    },
    {
      icon: <Clock className="feature-icon" />,
      title: "24/7 Availability",
      description: "Never miss a customer call with round-the-clock automated assistance for your business.",
      color: "accent"
    }
  ];

  const plans = [
    {
      name: "Basic",
      price: "₹499",
      period: "/month",
      description: "Perfect for small businesses starting with voice automation",
      features: [
        "1 Phone Number",
        "Up to 100 calls/month",
        "Basic WhatsApp Integration",
        "Product Catalog (10 items)",
        "Call Logs & Analytics",
        "Email Support"
      ],
      popular: false
    },
    {
      name: "Pro",
      price: "₹999",
      period: "/month",
      description: "Ideal for growing businesses with higher call volumes",
      features: [
        "1 Phone Number",
        "Up to 500 calls/month",
        "Advanced WhatsApp Features",
        "Product Catalog (50 items)",
        "Appointment Booking",
        "Invoice Generation",
        "Payment Links",
        "Priority Support"
      ],
      popular: true
    },
    {
      name: "Business",
      price: "₹1,999",
      period: "/month",
      description: "For established businesses needing comprehensive automation",
      features: [
        "2 Phone Numbers",
        "Up to 1,500 calls/month",
        "Full WhatsApp Automation",
        "Unlimited Products",
        "Advanced Analytics",
        "Custom Bot Responses",
        "API Access",
        "Dedicated Support"
      ],
      popular: false
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "",
      description: "Tailored solutions for large-scale operations",
      features: [
        "Multiple Phone Numbers",
        "Unlimited Calls",
        "White-label Solution",
        "Custom Integrations",
        "Advanced Reporting",
        "Multi-location Support",
        "24/7 Phone Support",
        "Account Manager"
      ],
      popular: false
    }
  ];

  const testimonials = [
    {
      name: "Rajesh Kumar",
      business: "Kumar Electronics",
      location: "Delhi",
      rating: 5,
      testimonial: "VoiceBot has transformed our customer service. We now handle 3x more inquiries without hiring additional staff. The WhatsApp integration is fantastic!"
    },
    {
      name: "Priya Sharma",
      business: "Sharma Beauty Salon",
      location: "Mumbai",
      rating: 5,
      testimonial: "Appointment booking has become so easy! Customers can book 24/7 and I get automatic notifications. Our no-show rate has dropped by 60%."
    },
    {
      name: "Mohammed Ali",
      business: "Ali Auto Parts",
      location: "Bangalore",
      rating: 5,
      testimonial: "The payment link feature is amazing. Customers can pay instantly after getting quotes. Our cash flow has improved significantly."
    }
  ];

  const stats = [
    { number: "10,000+", label: "Businesses Trust Us" },
    { number: "1M+", label: "Calls Handled" },
    { number: "98%", label: "Customer Satisfaction" },
    { number: "24/7", label: "Support Available" }
  ];

  return (
    <div className="landing-page">


      {/* Header */}
      <header className={`landing-header ${isScrolled ? 'scrolled' : ''}`}>
        <div className="header-container">
          <Link to="/" className="logo">
            <Phone className="logo-icon" />
            <span>VoiceBot</span>
          </Link>
          
          <nav className="nav-menu">
            <a href="#features" className="nav-link">Features</a>
            <a href="#pricing" className="nav-link">Pricing</a>
            <a href="#testimonials" className="nav-link">Testimonials</a>
            <a href="#contact" className="nav-link">Contact</a>
          </nav>
          
          <div className="header-actions">
            <button onClick={openLoginModal} className="nav-link" style={{ background: 'none', border: 'none', cursor: 'pointer' }}>Login</button>
            <button onClick={openSignupModal} className="btn btn-primary">Get Started</button>
          </div>
          
          <div className={`mobile-menu-toggle ${isMobileMenuOpen ? 'active' : ''}`} onClick={toggleMobileMenu}>
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      <div className={`mobile-menu ${isMobileMenuOpen ? 'active' : ''}`}>
        <div className="mobile-menu-content">
          <div className="mobile-menu-header">
            <Link to="/" className="logo" onClick={closeMobileMenu}>
              <Phone className="logo-icon" />
              <span>VoiceBot</span>
            </Link>
            <button className="mobile-menu-close" onClick={closeMobileMenu}>
              ×
            </button>
          </div>
          
          <nav className="mobile-nav">
            <li><a href="#features" onClick={closeMobileMenu}>Features</a></li>
            <li><a href="#pricing" onClick={closeMobileMenu}>Pricing</a></li>
            <li><a href="#testimonials" onClick={closeMobileMenu}>Testimonials</a></li>
            <li><a href="#contact" onClick={closeMobileMenu}>Contact</a></li>
          </nav>
          
          <div className="mobile-actions">
            <button onClick={(e) => { openLoginModal(e); closeMobileMenu(); }} className="btn btn-outline">Login</button>
            <button onClick={(e) => { openSignupModal(e); closeMobileMenu(); }} className="btn btn-primary">Get Started</button>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="hero-section" style={{ position: 'relative', overflow: 'hidden' }}>
        {/* Animated SVG Graph Lines Background */}
        <svg
          className="hero-graph-bg"
          width="100%"
          height="100%"
          viewBox="0 0 1440 500"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            zIndex: 0,
            pointerEvents: 'none',
          }}
          preserveAspectRatio="none"
        >
          {/* Zig-zag lines for desktop */}
          {!isMobile && (
            <>
              <polyline
                points="0,400 200,300 400,350 600,200 800,250 1000,100 1200,180 1440,50"
                className="graph-line graph-line-1"
              />
              <polyline
                points="0,450 180,350 380,400 580,250 780,300 980,150 1180,230 1440,100"
                className="graph-line graph-line-2"
              />
              <polyline
                points="0,420 220,320 420,370 620,220 820,270 1020,120 1220,200 1440,80"
                className="graph-line graph-line-3"
              />
            </>
          )}
          {/* Zig-zag lines for mobile */}
          {isMobile && (
            <>
              <polyline
                points="0,400 120,300 240,350 360,200 480,250 600,100 720,180 840,50 960,120 1080,80 1200,40 1320,60 1440,20"
                className="graph-line graph-line-1"
              />
              <polyline
                points="0,450 100,350 220,400 340,250 460,300 580,150 700,230 820,100 940,170 1060,130 1180,90 1300,110 1440,70"
                className="graph-line graph-line-2"
              />
              <polyline
                points="0,420 140,320 260,370 380,220 500,270 620,120 740,200 860,80 980,140 1100,100 1220,60 1340,80 1440,40"
                className="graph-line graph-line-3"
              />
            </>
          )}
        </svg>
        {/* End SVG Graph Lines */}
        <div className="hero-container">
          <h1 className="hero-title">
            Transform Your Business with
            <span> AI Voice Assistant</span>
          </h1>
          <p className="hero-subtitle">
            Never miss a customer call again. Our AI-powered voice bot handles inquiries, books appointments, 
            sends estimates, and processes payments automatically - all in Hindi and English.
          </p>
          
          <div className="hero-actions">
            <button onClick={openSignupModal} className="hero-btn hero-btn-primary">
              Start Free Trial
              <ArrowRight size={20} />
            </button>
            <button className="hero-btn hero-btn-secondary">
              <Play size={20} />
              Watch Demo
            </button>
          </div>
          
          <div className="hero-features">
            <div className="hero-feature">
              <CheckCircle className="hero-feature-icon" />
              <span>No setup fees</span>
            </div>
            <div className="hero-feature">
              <CheckCircle className="hero-feature-icon" />
              <span>14-day free trial</span>
            </div>
            <div className="hero-feature">
              <CheckCircle className="hero-feature-icon" />
              <span>Cancel anytime</span>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section bg-primary">
        <div className="container">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">{stat.number}</div>
                <div className="text-secondary">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="features-section">
        <div className="section-container">
          <div className="section-header">
            <h2 className="section-title">
              Everything You Need to Automate Customer Calls
            </h2>
            <p className="section-subtitle">
              Powerful features designed specifically for Indian small and medium businesses
            </p>
          </div>
          
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-header">
                  <div className={`feature-icon bg-${feature.color}`}>
                    {feature.icon}
                  </div>
                  <h3 className="feature-title">{feature.title}</h3>
                </div>
                <p className="feature-description">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="how-it-works-section">
        <div className="section-container">
          <div className="section-header">
            <h2 className="section-title">How It Works</h2>
            <p className="section-subtitle">Get started in just 3 simple steps</p>
          </div>
          
          <div className="steps-grid">
            <div className="step-card">
              <div className="step-number bg-primary">1</div>
              <h3 className="step-title">Sign Up & Setup</h3>
              <p className="step-description">Create your account and complete the business onboarding in 5 minutes</p>
            </div>
            <div className="step-card">
              <div className="step-number bg-secondary">2</div>
              <h3 className="step-title">Get Your Number</h3>
              <p className="step-description">We assign a dedicated phone number and configure your AI assistant</p>
            </div>
            <div className="step-card">
              <div className="step-number bg-accent">3</div>
              <h3 className="step-title">Start Receiving Calls</h3>
              <p className="step-description">Your AI assistant is live and ready to handle customer calls 24/7</p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="pricing-section">
        <div className="section-container">
          <div className="section-header">
            <h2 className="section-title">Simple, Transparent Pricing</h2>
            <p className="section-subtitle">Choose the plan that fits your business needs</p>
          </div>
          
          <div className="pricing-grid">
            {plans.map((plan, index) => (
              <div key={index} className={`pricing-card ${plan.popular ? 'popular' : ''}`}>
                <div className="pricing-header">
                  <h3 className="pricing-name">{plan.name}</h3>
                  <div className="pricing-price">
                    {plan.price}
                    <span className="pricing-period">{plan.period}</span>
                  </div>
                  <p className="pricing-description">{plan.description}</p>
                </div>
                
                <ul className="pricing-features">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="pricing-feature">
                      <CheckCircle className="pricing-feature-icon" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <button
                  onClick={(e) => {
                    if (plan.name === 'Enterprise') {
                      // Handle enterprise contact sales
                      window.location.href = 'mailto:<EMAIL>?subject=Enterprise Plan Inquiry';
                    } else {
                      // Store plan info and redirect to signup
                      const planInfo = {
                        name: plan.name.toLowerCase(),
                        displayName: plan.name,
                        price: parseFloat(plan.price.replace('₹', '').replace(',', '')),
                        features: plan.features
                      };
                      openSignupModal(e, planInfo);
                    }
                  }}
                  className={`pricing-cta ${plan.popular ? 'primary' : 'outline'}`}
                >
                  {plan.name === 'Enterprise' ? 'Contact Sales' : 'Get Started'}
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="testimonials-section">
        <div className="section-container">
          <div className="section-header">
            <h2 className="section-title">What Our Customers Say</h2>
            <p className="section-subtitle">Join thousands of businesses already using VoiceBot</p>
          </div>
          
          <div className="testimonials-grid">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="testimonial-card">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="testimonial-text">"{testimonial.testimonial}"</p>
                <div className="testimonial-author">
                  <h4 className="testimonial-name">{testimonial.name}</h4>
                  <p className="testimonial-business">{testimonial.business}</p>
                  <p className="testimonial-location">{testimonial.location}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-container">
          <h2 className="cta-title">
            Ready to Transform Your Business?
          </h2>
          <p className="cta-subtitle">
            Join thousands of businesses already using VoiceBot to grow their customer base
          </p>
          <div className="cta-actions">
            <button onClick={openSignupModal} className="cta-btn cta-btn-primary">
              Start Your Free Trial
              <ArrowRight size={20} />
            </button>
            <button className="cta-btn cta-btn-secondary">
              <Headphones size={20} />
              Schedule Demo
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-container">
          <div className="footer-grid">
            <div>
              <div className="footer-logo">
                <Phone className="w-6 h-6 mr-2" />
                <span>VoiceBot</span>
              </div>
              <p className="footer-description">
                Empowering Indian businesses with AI-powered voice automation and customer engagement solutions.
              </p>
            </div>
            
            <div>
              <h3>Product</h3>
              <ul className="footer-links">
                <li className="footer-link"><a href="#features">Features</a></li>
                <li className="footer-link"><a href="#pricing">Pricing</a></li>
                <li className="footer-link"><a href="#">API</a></li>
                <li className="footer-link"><a href="#">Integrations</a></li>
              </ul>
            </div>
            
            <div>
              <h3>Support</h3>
              <ul className="footer-links">
                <li className="footer-link"><a href="#">Help Center</a></li>
                <li className="footer-link"><a href="#">Contact Us</a></li>
                <li className="footer-link"><a href="#">Status</a></li>
                <li className="footer-link"><a href="#">Community</a></li>
              </ul>
            </div>
            
            <div>
              <h3>Company</h3>
              <ul className="footer-links">
                <li className="footer-link"><a href="#">About</a></li>
                <li className="footer-link"><a href="#">Blog</a></li>
                <li className="footer-link"><a href="#">Careers</a></li>
                <li className="footer-link"><a href="#">Privacy</a></li>
              </ul>
            </div>
          </div>
          
          <div className="footer-bottom">
            <p>&copy; 2025 VoiceBot. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Modals */}
      <SignupModal 
        isOpen={isSignupModalOpen}
        onClose={closeModals}
        onSwitchToLogin={switchToLogin}
        onEmailVerification={openEmailVerification}
      />
      <LoginModal 
        isOpen={isLoginModalOpen}
        onClose={closeModals}
        onSwitchToSignup={switchToSignup}
        onForgotPassword={openForgotPassword}
      />
      <EmailVerificationModal
        isOpen={isEmailVerificationModalOpen}
        onClose={closeModals}
        onVerifySuccess={handleEmailVerified}
        userEmail={userEmail}
      />
      <ForgotPasswordModal
        isOpen={isForgotPasswordModalOpen}
        onClose={closeModals}
        onBackToLogin={backToLogin}
        onResetEmailSent={(email) => setUserEmail(email)}
        onResetPassword={openResetPassword}
      />
      <ResetPasswordModal
        isOpen={isResetPasswordModalOpen}
        onClose={closeModals}
        onPasswordReset={handlePasswordReset}
        onBackToLogin={backToLogin}
        userEmail={userEmail}
        resetToken="sample-reset-token"
      />

      {/* Payment Modals */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={closePaymentModal}
        selectedPlan={selectedPlan}
        onSuccess={handlePaymentSuccess}
      />
      <PaymentSuccessModal
        isOpen={showSuccessModal}
        onClose={closeSuccessModal}
        paymentData={paymentResult}
      />
    </div>
  );
};

export default LandingPage; 